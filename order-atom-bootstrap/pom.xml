<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.yxt.order</groupId>
    <artifactId>order-atom-service</artifactId>
    <version>1.0.0</version>
  </parent>

  <groupId>com.yxt.order.atom.starter</groupId>
  <artifactId>order-atom-bootstrap</artifactId>
  <packaging>jar</packaging>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.yxt.order.atom</groupId>
      <artifactId>order-atom-server</artifactId>
      <version>1.0.0</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
        <exclusion>
          <artifactId>yxt-common-alarm</artifactId>
          <groupId>com.yxt</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>


  <build>
    <finalName>order-atom-service</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

</project>