package com.yxt.order.atom.bootstrap;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.job.compensate.CompensateHdOfflineOrderMissPromotionCouponInfoHandler;
import com.yxt.order.atom.job.compensate.CompensateRefundDataHandler;
import com.yxt.order.atom.order.entity.OfflineOrderCouponDO;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineOrderPromotionDO;
import com.yxt.order.atom.order.mapper.OfflineOrderCouponMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderPromotionMapper;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.junit.Test;

/**
 * @author: moatkon
 * @time: 2024/11/28 14:25
 */
@Slf4j
public class CompleteRefundDataTest extends BaseTest {

  @Resource
  private CompensateRefundDataHandler compensateRefundDataHandler;

  @Resource
  private CompensateHdOfflineOrderMissPromotionCouponInfoHandler compensateHdOfflineOrderMissPromotionCouponInfoHandler;


  @Test
  public void test() {
    while (true) {
      try {
        compensateRefundDataHandler.execute();
      } catch (Exception e) {
        log.error("异常", e);
      }
    }
  }

  @Test
  public void test2() {
    while (true) {
      try {
        compensateHdOfflineOrderMissPromotionCouponInfoHandler.execute();
      } catch (Exception e) {
        log.error("异常", e);
      }
    }
  }

  @Resource
  private OfflineOrderRepository offlineOrderRepository;

  @Test
  public void testAll() {
    DynamicDataSourceContextHolder.push(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME);
    for (String orderNo : Lists.newArrayList(
//        "1010040224194630075", // promotion vip
//        "2310479961943112411", // promotion not vip
//        "1379439653486150090", // coupon vip
        "2345820231889992411" // coupon not vip
    )) {
      OfflineOrderDO offlineOrderDO = buildForTest(orderNo);
      offlineOrderRepository.compensateHdMissPromotionCouponData(49085L, offlineOrderDO);
    }


  }

  @Resource
  private OfflineOrderPromotionMapper offlineOrderPromotionMapper;

  @Resource
  private OfflineOrderCouponMapper offlineOrderCouponMapper;
  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  private OfflineOrderDO buildForTest(String orderNo) {

    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<OfflineOrderDO> queryOrder = new LambdaQueryWrapper<>();
      queryOrder.eq(OfflineOrderDO::getOrderNo, orderNo);
      OfflineOrderDO offlineOrderDO = offlineOrderMapper.selectOne(queryOrder);

      LambdaQueryWrapper<OfflineOrderPromotionDO> queryPromotion = new LambdaQueryWrapper<>();
      queryPromotion.eq(OfflineOrderPromotionDO::getOrderNo, orderNo);
      List<OfflineOrderPromotionDO> offlineOrderPromotionList = offlineOrderPromotionMapper.selectList(
          queryPromotion);

      // 补充券信息
      LambdaQueryWrapper<OfflineOrderCouponDO> queryCoupon = new LambdaQueryWrapper<>();
      queryCoupon.eq(OfflineOrderCouponDO::getOrderNo, orderNo);
      List<OfflineOrderCouponDO> offlineOrderCouponDOList = offlineOrderCouponMapper.selectList(
          queryCoupon);

      offlineOrderDO.setOfflineOrderPromotionDOList(offlineOrderPromotionList);
      offlineOrderDO.setOfflineOrderCouponDOList(offlineOrderCouponDOList);
      return offlineOrderDO;
    }

  }


}
