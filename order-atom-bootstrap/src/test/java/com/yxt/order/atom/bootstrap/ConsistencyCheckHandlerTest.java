package com.yxt.order.atom.bootstrap;

import com.yxt.order.atom.job.ConsistencyCheckHandler;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 * @author: moatkon
 * @time: 2024/12/25 11:01
 */
@Slf4j
public class ConsistencyCheckHandlerTest extends BaseTest{

  @Resource
  private ConsistencyCheckHandler consistencyCheckHandler;

  @Test
  public void test(){
    for (;;) {
      try {
        consistencyCheckHandler.execute();
        System.out.println("ok");
      } catch (Exception e) {
        e.printStackTrace();
        log.error("",e);
      }
    }
  }
}
