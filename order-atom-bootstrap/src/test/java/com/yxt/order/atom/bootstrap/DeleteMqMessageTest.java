package com.yxt.order.atom.bootstrap;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.repository.MqMessageRepository;
import com.yxt.order.atom.sdk.mqmessage.req.MqMessageDeleteReqDto;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年05月09日 11:56
 * @email: <EMAIL>
 */
public class DeleteMqMessageTest extends BaseTest {

  @Resource
  private MqMessageRepository mqMessageRepository;

  @Test
  public void deleteMsg() {
    DynamicDataSourceContextHolder.push(DATA_SOURCE.ORDER_OFFLINE);
    MqMessageDeleteReqDto mqMessageDeleteReqDto = JsonUtils.toObject(
        "{\"mqMsgStatus\":\"HANDLED\",\"days\":\"-10\"}", MqMessageDeleteReqDto.class);
    mqMessageRepository.delete(mqMessageDeleteReqDto);
  }

}
