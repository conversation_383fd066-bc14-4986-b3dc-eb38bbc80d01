package com.yxt.order.atom.bootstrap;

import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import java.util.Set;
import javax.annotation.Resource;
import org.junit.Test;

/**
 * @author: moatkon
 * @time: 2024/12/27 9:36
 */
public class OfflineOrderTableShardingHintAlgorithmTest extends BaseTest {

  @Resource
  private OfflineOrderTableShardingHintAlgorithm offlineOrderTableShardingHintAlgorithm;


  @Test
  public void testFetch(){
    Set<String> strings = offlineOrderTableShardingHintAlgorithm.logicTableSet();
    System.out.println(strings);
//    System.out.println(SHARDING_TABLE_LIST);
    System.out.println();
  }
}
