package com.yxt.order.atom.bootstrap;

import cn.hutool.core.io.FileUtil;
import com.yxt.order.types.utils.ShardingHelper;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.junit.Test;

/**
 * 分表算法测试
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月18日 10:20
 * @email: <EMAIL>
 */
public class SplitTableAlgorithmTest {

  /**
   * 40多万userId
   */
  @Test
  public void userId40w() {
    try {
      // 只有用户id,一个用户一个单
      List<String> useIdList = FileUtil.readLines("D:\\userId.txt", StandardCharsets.UTF_8);

      // k: 分表位置 v: 数量
      Map<Integer, Long> dataMap = new HashMap<>();
      useIdList.stream().forEach(userId -> {
        Integer position = Integer.valueOf(ShardingHelper.getTableIndexByUserId(userId));
        if (dataMap.containsKey(position)) {
          Long l = dataMap.get(position);
          dataMap.put(position, l + 1);
        } else {
          dataMap.put(position, 1L);
        }
      });

      System.out.println("done");

      Map<Integer, Long> sortedMap = new TreeMap<>(dataMap);
      sortedMap.forEach((k, v) -> System.out.printf("分表: %s,   数量: %s\n", k, v));
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }


  /**
   * 7000多万userID
   *
   * @throws IOException
   */
  @Test
  public void dataUserId7000w() throws IOException {
    final LineIterator it = FileUtils.lineIterator(new File("D:\\allUserId.txt"), "UTF-8");
    Map<Integer, Long> dataMap = new HashMap<>();

    try {
      while (it.hasNext()) {
        final String userId = it.nextLine();
//        System.out.println(userId);
        Integer position = Integer.valueOf(ShardingHelper.getTableIndexByUserId(userId));
        if (dataMap.containsKey(position)) {
          Long l = dataMap.get(position);
          dataMap.put(position, l + 1);
        } else {
          dataMap.put(position, 1L);
        }
      }
    } finally {
      LineIterator.closeQuietly(it);
    }

    // 14s左右
    Map<Integer, Long> sortedMap = new TreeMap<>(dataMap);
    sortedMap.forEach((k, v) -> System.out.printf("表:%s,数量: %s\n", k, v));
  }

  @Test
  public void checkRepeat() throws IOException {
    final LineIterator it = FileUtils.lineIterator(new File("D:\\log_file\\id_generate_test2.log"),
        "UTF-8");
    Map<String, Long> dataMap = new HashMap<>();

    try {
      while (it.hasNext()) {
        String id = it.nextLine();
        id = id.substring(id.length() - 12); //30 threads,143w+,无重复。如果有异常可以通过重试机制处理
        if (dataMap.containsKey(id)) {
          Long l = dataMap.get(id);
          dataMap.put(id, l + 1);
        } else {
          dataMap.put(id, 1L);
        }
      }
    } finally {
      LineIterator.closeQuietly(it);
    }

    dataMap.forEach((k, v) -> {
      if (v > 1) {
        System.out.println(k);
      }
    });

  }

  public static void main(String[] args) {
    String userId = "1797193745755527169"; // 5755527169
    System.out.println(userId.substring(userId.length() - 10));
  }
}
