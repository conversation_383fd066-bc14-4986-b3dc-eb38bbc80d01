package com.yxt.order.atom.bootstrap.es;

import static com.yxt.order.atom.migration.config.ThreadPoolConfig.MIGRATION_THREAD_POOL;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.yxt.order.atom.bootstrap.BaseTest;
import com.yxt.order.atom.order.es.doc.EsOrder;
import com.yxt.order.atom.order.es.doc.EsOrderItem;
import com.yxt.order.atom.order.es.mapper.EsOrderMapper;
import com.yxt.order.types.es_order.EsOrderStatus;
import com.yxt.order.types.es_order.EsOrderType;
import com.yxt.order.types.es_order.EsServiceMode;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年09月03日 17:45
 * @email: <EMAIL>
 */
@Slf4j
public class FakeDataGenerateTest extends BaseTest {

  SnowflakeRandomGenerator generator = new SnowflakeRandomGenerator();

  @Resource
  private EsOrderMapper esOrderMapper;


  static ImportGoodsDataListener goodsDataListener = new ImportGoodsDataListener();
  static ImportStoreDataListener storeDataListener = new ImportStoreDataListener();
  static ImportUserDataListener userDataListener = new ImportUserDataListener();

  public static void main(String[] args) {
    new FakeDataGenerateTest().testGenerateFakeData();
  }

  @Qualifier(MIGRATION_THREAD_POOL)
  @Resource
  private ThreadPoolExecutor migrationThreadPool;

  @Test
  public void testGenerateFakeData() {
    String filename = "D:\\log_file\\线下单数据清单.xlsx";
    List<SheetNoName> sheetNoNameList = getSheetName(filename);
    for (SheetNoName sheetNoName : sheetNoNameList) {
      try {
        if ("商品信息".equals(sheetNoName.getSheetName())) {
          readExcel(filename, sheetNoName.getSheetNo(), new GoodsData(), goodsDataListener);
        } else if ("会员信息".equals(sheetNoName.getSheetName())) {
          readExcel(filename, sheetNoName.getSheetNo(), new UserData(), userDataListener);
        } else if ("门店信息".equals(sheetNoName.getSheetName())) {
          readExcel(filename, sheetNoName.getSheetNo(), new StoreData(), storeDataListener);
        } else {
          throw new RuntimeException("不匹配");
        }
      } catch (FileNotFoundException e) {
        throw new RuntimeException(e);
      }
    }

    List<GoodsData> goodsDataList = goodsDataListener.getDataList();
    List<UserData> userDataList = userDataListener.getDataList();
    List<StoreData> storeDataList = storeDataListener.getDataList();

    int dataSize = 1000 * 10000;
    AtomicInteger count = new AtomicInteger();

    for (int threadCount = 0; threadCount < 60; threadCount++) {
      migrationThreadPool.submit(() -> {
        while (true) {
          extracted(userDataList, count, storeDataList, goodsDataList, dataSize);
        }
      });
    }

    try {
      Thread.sleep(1000 * 60 * 60 * 24 * 8);
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }


  }

  private void extracted(List<UserData> userDataList, AtomicInteger count,
      List<StoreData> storeDataList, List<GoodsData> goodsDataList, int dataSize) {
    List<List<UserData>> partition = Lists.partition(userDataList, 100);
    for (List<UserData> userList : partition) {
      try {
        int i = count.incrementAndGet();
        EsOrder esOrder = new EsOrder();
        esOrder.setOrderNumber(String.valueOf(generator.generateRandom19DigitNumber()));
        esOrder.setOrderNo(esOrder.getOrderNumber());
        esOrder.setThirdOrderNo(esOrder.getOrderNumber());
        esOrder.setEsOrderType(EsOrderType.ORDER);

        String storeCode = getRandomElement(storeDataList).getStoreCode();
        esOrder.setOnlineStoreCode(storeCode);
        esOrder.setOrganizationCode(storeCode);
        esOrder.setPlatformCode("43");

        String userId = getRandomElement(userList).getUserId();
        esOrder.setUserId(userId);
        esOrder.setServiceMode(EsServiceMode.O2O);
        esOrder.setEsOrderStatus(EsOrderStatus.DONE);
        Date date = generateRandomDateWithinLast3Months();
        esOrder.setPayTime(date);
        esOrder.setCompleteTime(date);
        esOrder.setCreateTime(date);

        List<GoodsData> randomElements = getRandomElements(goodsDataList, random.nextInt(10));

        esOrder.setEsOrderItemList(randomElements.stream().map(r -> {
          EsOrderItem esOrderItem = new EsOrderItem();
          esOrderItem.setCommodityCode(r.getErpCode());
          esOrderItem.setCommodityName(r.getErpName());
          esOrderItem.setCommodityCount(BigDecimal.valueOf(random.nextInt(10)));
          return esOrderItem;
        }).collect(Collectors.toList()));

        if (i % 2 == 0) {
          esOrder.setEsOrderType(EsOrderType.REFUND);
          esOrder.setRefundNo(esOrder.getOrderNumber());
          esOrder.setThirdRefundNo(esOrder.getOrderNumber());
          esOrder.setOrderNo(Strings.EMPTY);
          esOrder.setThirdOrderNo(Strings.EMPTY);
        }

        if (i % 7 == 0) {
          esOrder.setPlatformCode(ThirdPlatformCodeEnum.KE_CHUAN.name());
          esOrder.setServiceMode(EsServiceMode.B2C);
        }
        if (i % 3 == 0) {
          esOrder.setPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
          esOrder.setServiceMode(EsServiceMode.POS);
        }
        // id如果已经存在,就会返回0 源码 org.dromara.easyes.core.kernel.BaseEsMapperImpl.doInsert
        Integer insert = esOrderMapper.insert(esOrder);

        if (i > dataSize) {
          log.info("生产总数:" + i);
          break;
        }
      } catch (Exception e) {
        log.error("异常", e);
      }

    }
  }

  public static Date generateRandomDateWithinLast3Months() {
    LocalDate now = LocalDate.now();
    LocalDate threeMonthsAgo = now.minus(Period.ofMonths(3));

    int daysBetween = (int) (now.toEpochDay() - threeMonthsAgo.toEpochDay());
    int randomDays = random.nextInt(daysBetween + 1);

    LocalDate randomLocalDate = threeMonthsAgo.plusDays(randomDays);

    // 转换 LocalDate 为 Date
    return Date.from(randomLocalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
  }

  public static <T> List<T> getRandomElements(Collection<T> collection, int numElements) {
    if (collection == null || collection.isEmpty() || numElements <= 0) {
      return new ArrayList<>();
    }

    List<T> list = Lists.newArrayList(collection);
    int size = list.size();
    numElements = Math.min(numElements, size);

    List<T> result = new ArrayList<>(numElements);
    for (int i = 0; i < numElements; i++) {
      int index = random.nextInt(size - i);
      result.add(list.get(index));
      // Swap the selected element with the last unselected element
      T temp = list.get(size - i - 1);
      list.set(size - i - 1, list.get(index));
      list.set(index, temp);
    }

    return result;
  }

  // 688 + 688 + 1072
  private static void readExcel(String filename, Integer sheetNo, Object object,
      AnalysisEventListener listener) throws FileNotFoundException {
    File file = new File(filename);
    FileInputStream fis = new FileInputStream(file);
    EasyExcel.read(fis, object.getClass(), listener).sheet(sheetNo).doRead();
  }

  private static List<SheetNoName> getSheetName(String filename) {
    List<SheetNoName> sheetNoNameList = new ArrayList<>();
    // 创建 ExcelReader 对象
    ExcelReader excelReader = EasyExcel.read(new File(filename)).build();
    List<ReadSheet> readSheets = excelReader.excelExecutor().sheetList();
    for (ReadSheet readSheet : readSheets) {
      SheetNoName sheetNoName = new SheetNoName();
      sheetNoName.setSheetNo(readSheet.getSheetNo());
      sheetNoName.setSheetName(readSheet.getSheetName());
      sheetNoNameList.add(sheetNoName);
    }
    // 关闭 ExcelReader 对象
    excelReader.finish();
    return sheetNoNameList;
  }

  private static final Random random = new Random();

  public static <T> T getRandomElement(Collection<T> collection) {
    if (collection == null || collection.isEmpty()) {
      return null;
    }
    int size = collection.size();
    return Iterables.get(collection, random.nextInt(size));
  }

}
