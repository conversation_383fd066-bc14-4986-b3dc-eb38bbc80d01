package com.yxt.order.atom.bootstrap.generate;

import static com.yxt.order.atom.bootstrap.migration.Constant.ALL_SCHEMA;
import static com.yxt.order.atom.bootstrap.migration.Constant.HANA_MAIN_TABLE_LIST;

import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;

public class GenerateMigrateSQL {

  /**
   * 查hana增量数据是否有
   */
  @Test
  public void growDataGenerateForHana() {
    String[] schemaCompanyArray = ALL_SCHEMA.split("\n");
    for (String schema : schemaCompanyArray) {
      for (int i = 0; i < HANA_MAIN_TABLE_LIST.size(); i++) {
        String table = HANA_MAIN_TABLE_LIST.get(i);
        String unionAll = (HANA_MAIN_TABLE_LIST.size() - 1== i)?";":"union all";
        System.out.println(rangeGrowth("select count(1) from " + schema + "." + table + " where ",unionAll) );
      }

      System.out.println();
    }

  }

  /**
   * 查MySQL增量数据是否有
   */
  @Test
  public void growDataGenerateForMySQL() {
    String[] schemaCompanyArray = ALL_SCHEMA.split("\n");
    for (String schema : schemaCompanyArray) {
      for (int i = 0; i < HANA_MAIN_TABLE_LIST.size(); i++) {
        String table = HANA_MAIN_TABLE_LIST.get(i);
        String unionAll = (HANA_MAIN_TABLE_LIST.size() - 1 == i) ? ";" : "union all";
        // MySQL
        System.out.println(rangeGrowth(
            "select max(id) from " + table + "_" + schema + "_20241007_2025011512" + " where ",
            unionAll));
      }

      System.out.println();
    }

  }


  @Test
  public void yearGenerateYearForHana() {
    String schema = "SCHX_USERS";
    Integer startYear = 2018;
    Integer endYear = 2024;
    rangeYearCount("select count(1) from " + schema + ".XF_TRANSSALESTOTAL where ", startYear,
        endYear);
    System.out.println();
    rangeYearCount("select count(1) from " + schema + ".XF_TRANSSALESITEM where ", startYear,
        endYear);
    System.out.println();
    rangeYearCount("select count(1) from " + schema + ".XF_TRANSSALESTENDER where ", startYear,
        endYear);
  }

  @Test
  public void quarterGenerateForHana() {
    String schema = "CQHX_USERS";
    Integer startYear = 2018;
    Integer endYear = 2024;
    rangeQuarterCount("select count(1) from " + schema + ".XF_TRANSSALESTOTAL where ", startYear,
        endYear);
    System.out.println();
    rangeQuarterCount("select count(1) from " + schema + ".XF_TRANSSALESITEM where ", startYear,
        endYear);
    System.out.println();

    rangeQuarterCount("select count(1) from " + schema + ".XF_TRANSSALESTENDER where ", startYear,
        endYear);
  }

  @Test
  public void quarterGenerateForMySQL() {
    String schema = "CDHX_USERS";
    Integer startYear = 2020;
    Integer endYear = 2024;

    rangeSchemaQuarter("select max(id) from XF_TRANSSALESTOTAL_" + schema, startYear, endYear);
    System.out.println();
    rangeSchemaQuarter("select max(id) from XF_TRANSSALESITEM_" + schema, startYear, endYear);
    System.out.println();
    rangeSchemaQuarter("select max(id) from XF_TRANSSALESTENDER_" + schema, startYear, endYear);
  }


  @Test
  public void monthGenerateForMySQL() {
    String schema = "YNHX_DATA01";
    Integer startYear = 2021;
    Integer endYear = 2024;

    rangeSchemaMonth("select max(id) from XF_TRANSSALESTOTAL_" + schema, startYear, endYear);
    System.out.println();
    rangeSchemaMonth("select max(id) from XF_TRANSSALESITEM_" + schema, startYear, endYear);
    System.out.println();
    rangeSchemaMonth("select max(id) from XF_TRANSSALESTENDER_" + schema, startYear, endYear);
  }


  private ArrayList<String> rangeSchemaMonth(String schema, int startYear, int endYear) {
    Assert.assertTrue("最大年份不能超过2024", endYear <= 2024);

    ArrayList<String> list = Lists.newArrayList();

    for (int year = startYear; year <= endYear; year++) {
      for (int month = 1; month <= 12; month++) {
        String format = String.format("%s_%s_%s union all ", schema, year, month);
        System.out.println(format);
        list.add(format);
      }
    }
    return list;
  }


  @Test
  public void yearGenerateYearForMySQL() {
    String schema = "HNHX_DATA01";
    Integer startYear = 2019;
    Integer endYear = 2024;

    rangeSchemaTableNameYear("select max(id) from XF_TRANSSALESTOTAL_" + schema, startYear,
        endYear);
    System.out.println();
    rangeSchemaTableNameYear("select max(id) from XF_TRANSSALESITEM_" + schema, startYear, endYear);
    System.out.println();
    rangeSchemaTableNameYear("select max(id) from XF_TRANSSALESTENDER_" + schema, startYear,
        endYear);
  }

  @Test
  public void normal() {
//    String schema = "TJQCHX_DATA01";
    Lists.newArrayList("TJQCHX_DATA01", "SXGSHX_DATA01", "HENHX_DATA01", "TJHX_DATA01",
        "SHHX_DATA01").forEach(schema -> {
      System.out.println("-- " + schema);
      System.out.println("select count(1) from XF_TRANSSALESTOTAL_" + schema
          + " where XF_CREATETIME <= '2024-10-07 00:00:00' union all");
      System.out.println("select count(1) from XF_TRANSSALESITEM_" + schema
          + " where XF_CREATETIME <= '2024-10-07 00:00:00' union all");
      System.out.println("select count(1) from XF_TRANSSALESTENDER_" + schema
          + " where XF_CREATETIME <= '2024-10-07 00:00:00';");

      System.out.println();
      System.out.println();
    });


  }

  public List<String> rangeSchemaTableNameYear(String schema, Integer startYear, Integer endYear) {
//    Assert.assertTrue("最大年份不能超过2024", endYear <= 2024);
    List<String> list = Lists.newArrayList();
    for (int year = startYear; year <= endYear; year++) {
      String format = String.format("%s_%s union all ", schema, year);
      System.out.println(format);
      list.add(format);
    }
    return list;
  }

  public List<String> rangeSchemaQuarter(String schema, Integer startYear, Integer endYear) {
    Assert.assertTrue("最大年份不能超过2024", endYear <= 2024);

    ArrayList<String> list = Lists.newArrayList();

    for (int year = startYear; year <= endYear; year++) {
      for (int quarter = 1; quarter <= 4; quarter++) {
        String format = String.format("%s_%s_Q%s union all ", schema, year, quarter);
        System.out.println(format);
        list.add(format);
      }
    }
    return list;
  }


  public void rangeQuarterCount(String schemaAndTable, Integer startYear, Integer endYear) {
    Assert.assertTrue("最大年份不能超过2024", endYear <= 2024);

    for (int year = startYear; year <= endYear; year++) {
      for (int quarter = 1; quarter <= 4; quarter++) {
        String sqlCondition = generateQuarterSQLCondition(schemaAndTable, year, quarter);
//        System.out.println(sqlCondition);

        System.out.println(String.format("debug: %s_Q%s sql: %s", year, quarter, sqlCondition));

      }
    }
  }


  public static String generateQuarterSQLCondition(String schemaAndTable, int year, int quarter) {
    String startDate, endDate;
    switch (quarter) {
      case 1:
        startDate = year + "-01-01 00:00:00";
        endDate = year + "-04-01 00:00:00";
        break;
      case 2:
        startDate = year + "-04-01 00:00:00";
        endDate = year + "-07-01 00:00:00";
        break;
      case 3:
        startDate = year + "-07-01 00:00:00";
        endDate = year + "-10-01 00:00:00";
        break;
      case 4:
        startDate = year + "-10-01 00:00:00";
        endDate = (year + 1) + "-01-01 00:00:00";
        if (year == 2024) {
          endDate = year + "-10-07 00:00:00";
          return schemaAndTable + "XF_CREATETIME >= '" + startDate + "' AND XF_CREATETIME <= '"
              + endDate + "' union all ";
        }
        break;
      default:
        throw new IllegalArgumentException("无效的季度: " + quarter);
    }
    return schemaAndTable + "XF_CREATETIME >= '" + startDate + "' AND XF_CREATETIME < '" + endDate
        + "' union all ";
  }

  public void rangeYearCount(String schemaAndTable, Integer startYear, Integer endYear) {
    Assert.assertTrue("最大年份不能超过2024", endYear <= 2024);

    for (int year = startYear; year <= endYear; year++) {
      String sqlCondition = generateSQLCondition(schemaAndTable, year);
      System.out.println(sqlCondition);
    }
  }

  public String rangeGrowth(String schemaAndTable, String unionAll) {
    return schemaAndTable
        + " XF_CREATETIME >= '2024-10-07 00:00:00' AND XF_CREATETIME <= '2025-01-15 12:00:00' "
        + unionAll;
  }


  public String generateSQLCondition(String schemaAndTable, int year) {
    if (year == 2024) {
      return schemaAndTable + "XF_CREATETIME >= '" + year
          + "-01-01 00:00:00' AND XF_CREATETIME <= '2024-10-07 00:00:00' union all ";
    }

    return schemaAndTable + "XF_CREATETIME >= '" + year + "-01-01 00:00:00' AND XF_CREATETIME < '"
        + (year + 1) + "-01-01 00:00:00' union all ";
  }


}
