package com.yxt.order.atom.bootstrap.generate.fix;

import cn.hutool.core.io.FileUtil;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年07月31日 16:07
 * @email: <EMAIL>
 */
public class FixMemberMigration {

  public static void main(String[] args) {
//    String s = extractedUserIdLIst();
//    String s2 = extractedLoginIdLIst();
    String s3 = extractedPayLIst();
    String s1 = extractedItemLIst();
    System.out.println();
  }

  public static String extractedUserIdLIst() {
    List<String> contentList = FileUtil.readLines(
        "C:\\Users\\<USER>\\Desktop\\排重202408010944.txt",
        StandardCharsets.UTF_8);

    if (CollectionUtils.isEmpty(contentList)) {
      throw new RuntimeException("extractedUserIdLIst 操作文件为空,不处理");
    }

    contentList = contentList.stream().distinct().collect(Collectors.toList());

    StringBuilder sb = new StringBuilder();
//    sb.append(" `会员卡ID(实物卡号)` in (");

    String flag1 = "根据会员Id在迁移库中找不到会员信息:";
    int size = contentList.size();
    for (int i = 0; i < size; i++) {
      String value = contentList.get(i);
      if (!value.contains(flag1)) {
        continue;
      }
      value = value.substring(flag1.length() + 1, value.length() - 1);

      sb.append(String.format(" '%s' ", value));

      if (i != size - 1) {
        sb.append(",");
      }

    }
//    sb.append(" ) ");
    if (sb.toString().substring(sb.toString().length() - 1).equals(",")) {
      String s = sb.toString().substring(0, sb.length() - 1);
      return s;
    }
    return sb.toString();
  }

  public static String extractedLoginIdLIst() {
    List<String> contentList = FileUtil.readLines(
        "C:\\Users\\<USER>\\Desktop\\排重202408011027.txt",
        StandardCharsets.UTF_8);

    if (CollectionUtils.isEmpty(contentList)) {
      throw new RuntimeException("extractedUserIdLIst 操作文件为空,不处理");
    }

    contentList = contentList.stream().distinct().collect(Collectors.toList());

    StringBuilder sb = new StringBuilder();
//    sb.append(" `会员卡ID(实物卡号)` in (");

    String flag1 = "查不到人";
    int size = contentList.size();
    for (int i = 0; i < size; i++) {
      String value = contentList.get(i);
      if (!value.contains(flag1)) {
        continue;
      }
      value = value.substring(flag1.length() + 1, value.length() - 1);

      sb.append(String.format(" '%s' ", value));

      if (i != size - 1) {
        sb.append(",");
      }

    }
//    sb.append(" ) ");
    if (sb.toString().substring(sb.toString().length() - 1).equals(",")) {
      String s = sb.toString().substring(0, sb.length() - 1);
      return s;
    }
    return sb.toString();
  }

  public static String extractedPayLIst() {
    List<String> contentList = FileUtil.readLines(
        "C:\\Users\\<USER>\\Desktop\\hana_pay.txt",
        StandardCharsets.UTF_8);

    if (CollectionUtils.isEmpty(contentList)) {
      throw new RuntimeException("extractedPayLIst 操作文件为空,不处理");
    }

    contentList = contentList.stream().distinct().collect(Collectors.toList());

    StringBuilder sb = new StringBuilder();

    String flag1 = "支付信息为空";
    int size = contentList.size();
    for (int i = 0; i < size; i++) {
      String value = contentList.get(i);
      if (!value.contains(flag1)) {
        continue;
      }
      value = value.replace("\"", "");
      String[] split = value.split(",");

      sb.append(String.format(" ('%s','%s') ", split[1], split[2]));

      if (i != size - 1) {
        sb.append(",");
      }

    }
//    sb.append(" ) ");
    if (sb.toString().substring(sb.toString().length() - 1).equals(",")) {
      String s = sb.toString().substring(0, sb.length() - 1);
      return s;
    }
    return sb.toString();
  }

  public static String extractedItemLIst() {
    List<String> contentList = FileUtil.readLines(
        "C:\\Users\\<USER>\\Desktop\\正单明细迁移库中不存在.txt",
        StandardCharsets.UTF_8);

    if (CollectionUtils.isEmpty(contentList)) {
      throw new RuntimeException("extractedItemLIst 操作文件为空,不处理");
    }

    contentList = contentList.stream().distinct().collect(Collectors.toList());

    StringBuilder sb = new StringBuilder();

    String flag1 = "正单明细迁移库中不存在";
    int size = contentList.size();
    for (int i = 0; i < size; i++) {
      String value = contentList.get(i);
      if (!value.contains(flag1)) {
        continue;
      }
      value = value.replace("\"", "");
      String[] split = value.split(",");

      sb.append(String.format(" ('%s','%s') ", split[1], split[2]));

      if (i != size - 1) {
        sb.append(",");
      }

    }
//    sb.append(" ) ");
    if (sb.toString().substring(sb.toString().length() - 1).equals(",")) {
      String s = sb.toString().substring(0, sb.length() - 1);
      return s;
    }
    return sb.toString();
  }

}
