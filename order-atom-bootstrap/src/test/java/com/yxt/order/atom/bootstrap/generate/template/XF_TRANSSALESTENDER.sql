CREATE TABLE XF_TRANSSALESTENDER_${schema} (
                                     id INT AUTO_INCREMENT PRIMARY KEY,
                                     XF_STORECODE VARCHAR(6),
                                     XF_TILLID VARCHAR(3),
                                     XF_TXDATE DATE,
                                     XF_TXSERIAL DOUBLE,
                                     XF_POSTDATE VARCHAR(21),
                                     XF_TXTIME VARCHAR(6),
                                     XF_TXBATCH DOUBLE,
                                     XF_DOCNO VARCHAR(10),
                                     XF_VOIDDOCNO VARCHAR(10),
                                     XF_TXTYPE DOUBLE,
                                     XF_TXHOUR DOUBLE,
                                     XF_CASHIERCODE VARCHAR(10),
                                     XF_TENDERCODE VARCHAR(2),
                                     XF_SPECIFICEDTYPE DOUBLE,
                                     XF_PAYAMOUNT DECIMAL(16, 4),
                                     XF_BASEAMOUNT DECIMAL(16, 4),
                                     XF_EXTENDPARAM VARCHAR(250),
                                     XF_CREATETIME VARCHAR(21),
                                     <PERSON><PERSON>_DESTLOCATIONLIST VARCHAR(250),
                                     <PERSON><PERSON>_EXCESSMONEY DECIMAL(16, 4),
                                     CRM_EXECUTED VARCHAR(1),
                                     CRM_EXECUTED1 VARCHAR(1),
                                     UNIQUE KEY (XF_STORECODE, XF_TILLID, XF_TXDATE, XF_TXSERIAL),
                                     KEY `idx_docNo_storeCode` (`XF_DOCNO`,`XF_STORECODE`),
                                     KEY `idx_tendercode` (`XF_TENDERCODE`),
                                     KEY `idx_create_time` (`XF_CREATETIME`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;