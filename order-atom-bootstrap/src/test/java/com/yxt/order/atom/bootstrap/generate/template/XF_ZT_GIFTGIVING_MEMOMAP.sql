-- 只有云南公司启用了 YNHX_DATA01
CREATE TABLE XF_ZT_GIFTGIVING_MEMOMAP_${schema} (
                                            `id` int NOT NULL AUTO_INCREMENT,
                                            `XF_STORECODE` VARCHAR(10),
                                            `XF_TILLID` VARCHAR(3),
                                            `XF_TXDATE` DATE,
                                            `XF_TXTIME` VARCHAR(6),
                                            `XF_DOCNO` VARCHAR(10),
                                            `XF_GEDOCNO` VARCHAR(10),
                                            `XF_GIFTEVENTID` VARCHAR(30),
                                            `XF_GIFTEVENTAMT` DECIMAL(12,4),
                                            `XF_TTLGIFTAMT` DECIMAL(12,4),
                                            <PERSON><PERSON>AR<PERSON> KEY (`id`),
                                            UNIQUE KEY (`XF_STORECODE`, `XF_TILLID`, `XF_TXDATE`, `XF_TXTIME`, `XF_DOCNO`),
                                            <PERSON><PERSON>Y `idx_docNo_storeCode` (`XF_DOCNO`,`XF_STORECODE`) USING BTREE,
                                            <PERSON><PERSON>Y `idx_geDocNo_storeCode` (`XF_GEDOCNO`,`XF_STORECODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;