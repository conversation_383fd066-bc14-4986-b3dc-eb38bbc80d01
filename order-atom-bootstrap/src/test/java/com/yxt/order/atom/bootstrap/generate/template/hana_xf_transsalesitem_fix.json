{
  "core": {
    "transport" : {
      "channel": {
        "speed": {
          "channel": ${channel},
          "byte":  ${byte},
          "record": ${record}
        }
      }
    }
  },
  "job": {
    "setting": {
      "speed": {
        "channel": ${channel},
        "byte":  ${byte},
        "record": ${record}
      }
    },
    "content": [
      {
        "reader": {
          "name": "rdbmsreader",
          "parameter": {
            "username": "${hanaUsername}",
            "password": "${hanaPassword}",
            "connection": [
              {
                "querySql": [
                  "select XF_STORECODE,XF_TILLID,XF_TXDATE,XF_TXSERIAL,XF_TXTIME,XF_TXBATCH,XF_DOCNO,XF_VOIDDOCNO,XF_TXTYPE,XF_TXHOUR,XF_CASHIER,XF_SALESMAN,XF_VIPCODE,XF_DEMOGRAPCODE,XF_DEMOGRAPDATA,XF_PLU,XF_<PERSON>Y<PERSON>,XF_COLOR,XF_SIZE,<PERSON>F_ITEMLOTNUM,<PERSON>F_QTYSOLD,<PERSON>F_AMTSOLD,<PERSON>F_COSTSOLD,XF_MARKDOWNAMT,XF_<PERSON>ISCOUNTAMT,XF_PROMOTIONAMT,XF_TAXAMOUNT1,XF_TAXAMOUNT2,XF_TAXRATE1,XF_TAXRATE2,XF_EXSTK2SALES,XF_ORGUPRICE,XF_ISDEPOSIT,XF_ISWHOLESALE,XF_ISPRICEALTERNATE,XF_ISPRICEOVERRIDE,XF_ISNEWITEM,XF_PRICEAPPROVE,XF_COUPONNUMBER,XF_DISCOUNTAPPROVE,XF_ITEMDISCOUNTAMT,XF_TTLDISCOUNTLESS,XF_PROMID1,XF_PROMAMT1,XF_PROMQTY1,XF_PROMID2,XF_PROMAMT2,XF_PROMQTY2,XF_PROMID3,XF_PROMAMT3,XF_PROMQTY3,XF_PROMID4,XF_PROMAMT4,XF_PROMQTY4,XF_PROMID5,XF_PROMAMT5,XF_PROMQTY5,XF_SALESITEMREMARK,XF_EXTENDPARAM,XF_DESTLOCATIONLIST,XF_PRICECENTER,XF_COSTCENTER,XF_POSTDATE,XF_CREATETIME,XF_ISPOSTING,CRM_EXECUTED,CRM_EXECUTED1,XF_HXALLDISCLESS1 from ${schema}.XF_TRANSSALESITEM where (XF_STORECODE,XF_DOCNO) in ( ${fixItemSql} ) "
                ],
                "jdbcUrl": [
                  "${hanaUrl}"
                ]
              }
            ],
            "fetchSize": ${fetchSize}
          }
        },
        "writer": {
          "name": "mysqlwriter",
          "parameter": {
            "username": "${mySqlUsername}",
            "password": "${mySqlPassword}",
            "column": [
              "XF_STORECODE",
              "XF_TILLID",
              "XF_TXDATE",
              "XF_TXSERIAL",
              "XF_TXTIME",
              "XF_TXBATCH",
              "XF_DOCNO",
              "XF_VOIDDOCNO",
              "XF_TXTYPE",
              "XF_TXHOUR",
              "XF_CASHIER",
              "XF_SALESMAN",
              "XF_VIPCODE",
              "XF_DEMOGRAPCODE",
              "XF_DEMOGRAPDATA",
              "XF_PLU",
              "XF_STYLE",
              "XF_COLOR",
              "XF_SIZE",
              "XF_ITEMLOTNUM",
              "XF_QTYSOLD",
              "XF_AMTSOLD",
              "XF_COSTSOLD",
              "XF_MARKDOWNAMT",
              "XF_DISCOUNTAMT",
              "XF_PROMOTIONAMT",
              "XF_TAXAMOUNT1",
              "XF_TAXAMOUNT2",
              "XF_TAXRATE1",
              "XF_TAXRATE2",
              "XF_EXSTK2SALES",
              "XF_ORGUPRICE",
              "XF_ISDEPOSIT",
              "XF_ISWHOLESALE",
              "XF_ISPRICEALTERNATE",
              "XF_ISPRICEOVERRIDE",
              "XF_ISNEWITEM",
              "XF_PRICEAPPROVE",
              "XF_COUPONNUMBER",
              "XF_DISCOUNTAPPROVE",
              "XF_ITEMDISCOUNTAMT",
              "XF_TTLDISCOUNTLESS",
              "XF_PROMID1",
              "XF_PROMAMT1",
              "XF_PROMQTY1",
              "XF_PROMID2",
              "XF_PROMAMT2",
              "XF_PROMQTY2",
              "XF_PROMID3",
              "XF_PROMAMT3",
              "XF_PROMQTY3",
              "XF_PROMID4",
              "XF_PROMAMT4",
              "XF_PROMQTY4",
              "XF_PROMID5",
              "XF_PROMAMT5",
              "XF_PROMQTY5",
              "XF_SALESITEMREMARK",
              "XF_EXTENDPARAM",
              "XF_DESTLOCATIONLIST",
              "XF_PRICECENTER",
              "XF_COSTCENTER",
              "XF_POSTDATE",
              "XF_CREATETIME",
              "XF_ISPOSTING",
              "CRM_EXECUTED",
              "CRM_EXECUTED1",
              "XF_HXALLDISCLESS1"
            ],
            "connection": [
              {
                "table": [
                  "xf_transsalesitem_${schema}"
                ],
                "jdbcUrl": "${mySqlUrl}"
              }
            ],
            "batchSize": ${batchSize}
          }
        }
      }
    ]
  }
}
