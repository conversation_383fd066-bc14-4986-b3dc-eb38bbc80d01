package com.yxt.order.atom.bootstrap.migration;

import java.util.ArrayList;
import java.util.List;
import lombok.Data;

public class BatchRangeGenerator {

  public static void main(String[] args) {
    // 示例输入
    Long start = 0L;
    Long end = 10L;
    Long batchSize = 3L;

    // 生成批次范围
    List<Range> ranges = generateBatchRanges(start, end, batchSize);

    // 输出结果
    for (Range range : ranges) {
      System.out.println("[" + range.start + "," + range.end + "]");
    }
  }

  /**
   * 生成批次范围列表
   *
   * @param start     开始值
   * @param end       结束值
   * @param batchSize 批次大小
   * @return 范围列表
   */
  public static List<Range> generateBatchRanges(Long start, Long end, Long batchSize) {
    List<Range> ranges = new ArrayList<>();

    Long current = start;
    while (current <= end) {
      Long batchEnd = Math.min(current + batchSize - 1, end);
      ranges.add(new Range(current, batchEnd));
      current = batchEnd + 1;
    }

    return ranges;
  }


  /**
   * 表示一个范围的类
   */
  @Data
  public static class Range {

    Long start;
    Long end;

    public Range(Long start, Long end) {
      this.start = start;
      this.end = end;
    }
  }
}