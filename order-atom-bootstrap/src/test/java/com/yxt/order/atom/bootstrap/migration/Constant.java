package com.yxt.order.atom.bootstrap.migration;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import java.util.List;
import java.util.Map;

/**
 * @author: moatkon
 * @time: 2025/3/4 19:43
 */
public class Constant {
  public static final String UN_DONE_ORDER = "0\n"
      + "0\n"
      + "0\n"
      + "42\n"
      + "0\n"
      + "8\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "425\n"
      + "1084\n"
      + "1\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "8\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "1\n"
      + "97\n"
      + "0\n"
      + "0";


  public static final String UN_DONE_ORDER_SCHEMA = "cqhx_users_2024\n"
      + "cqhx_users_20241007_2025011512\n"
      + "gxhx_users_20241007_2025011512\n"
      + "gxhx_users_2024_q3\n"
      + "gxhx_users_2024_q4\n"
      + "gzhx_users_2024\n"
      + "gzhx_users_20241007_2025011512\n"
      + "henhx_data01\n"
      + "henhx_data01_20241007_2025011512\n"
      + "hennyhx_data01_20241007_2025011512\n"
      + "hnhx_data01_2024\n"
      + "hnhx_data01_20241007_2025011512\n"
      + "schx_users_2024\n"
      + "schx_users_20241007_2025011512\n"
      + "shhx_data01\n"
      + "shhx_data01_20241007_2025011512\n"
      + "sxgshx_data01\n"
      + "sxgshx_data01_20241007_2025011512\n"
      + "sxhx_users_2024\n"
      + "sxhx_users_20241007_2025011512\n"
      + "tjhx_data01\n"
      + "tjhx_data01_20241007_2025011512\n"
      + "tjqchx_data01\n"
      + "tjqchx_data01_20241007_2025011512\n"
      + "ynhx_data01_20241007_2025011512\n"
      + "ynhx_data01_2024_10\n"
      + "ynhx_data01_2024_7\n"
      + "ynhx_data01_2024_8\n"
      + "ynhx_data01_2024_9";


  public static final String ALL_SCHEMA = "YNHX_DATA01\n"
      + "GXHX_USERS\n"
      + "GZHX_USERS\n"
      + "SCHX_USERS\n"
      + "SXHX_USERS\n"
      + "CQHX_USERS\n"
      + "CDHX_USERS\n"
      + "SHHX_DATA01\n"
      + "TJHX_DATA01\n"
      + "HNHX_DATA01\n"
      + "HENHX_DATA01\n"
      + "SXGSHX_DATA01\n"
      + "TJQCHX_DATA01\n"
      + "HENNYHX_DATA01\n"
      + "ZYHX_USERS";

  public static final Map<String, String> SCHEMA_COMPANY_MAP = ImmutableMap.<String, String>builder()
      .put("ZYHX_USERS","中药科技")
      .put("HENNYHX_DATA01","河南康健")
      .put("TJQCHX_DATA01","天津乾昌")
      .put("SXGSHX_DATA01","山西广生")
      .put("HENHX_DATA01","河南")
      .put("TJHX_DATA01","天津")
      .put("SHHX_DATA01","上海")
      .put("SCHX_USERS","攀枝花")
      .put("SXHX_USERS","山西")
      .put("GZHX_USERS","贵州")
      .put("CQHX_USERS","重庆")
      .put("HNHX_DATA01","海南")
      .put("GXHX_USERS","广西")
      .put("CDHX_USERS","四川")
      .put("YNHX_DATA01","云南")
      .build();


  public static final List<String> HANA_MAIN_TABLE_LIST = Lists.newArrayList(
      "XF_TRANSSALESTOTAL",
      "XF_TRANSSALESITEM",
      "XF_TRANSSALESTENDER"
  );

  public static final List<String> HANA_MIGRATION_CONFIG_LIST = Lists.newArrayList("cdhx_users_2020_q1",
      "cdhx_users_2020_q2",
      "cdhx_users_2020_q3",
      "cdhx_users_2020_q4",
      "cdhx_users_2021_q1",
      "cdhx_users_2021_q2",
      "cdhx_users_2021_q3",
      "cdhx_users_2021_q4",
      "cdhx_users_2022_q1",
      "cdhx_users_2022_q2",
      "cdhx_users_2022_q3",
      "cdhx_users_2022_q4",
      "cdhx_users_2023_q1",
      "cdhx_users_2023_q2",
      "cdhx_users_2023_q3",
      "cdhx_users_2023_q4",
      "cdhx_users_20241007_2025011512",
      "cdhx_users_2024_q1",
      "cdhx_users_2024_q2",
      "cdhx_users_2024_q3",
      "cqhx_users_2018",
      "cqhx_users_2019",
      "cqhx_users_2020",
      "cqhx_users_2021",
      "cqhx_users_2022",
      "cqhx_users_2023",
      "cqhx_users_2024",
      "cqhx_users_20241007_2025011512",
      "gxhx_users_2020_q1",
      "gxhx_users_2020_q2",
      "gxhx_users_2020_q3",
      "gxhx_users_2020_q4",
      "gxhx_users_2021_q1",
      "gxhx_users_2021_q2",
      "gxhx_users_2021_q3",
      "gxhx_users_2021_q4",
      "gxhx_users_2022_q1",
      "gxhx_users_2022_q2",
      "gxhx_users_2022_q3",
      "gxhx_users_2022_q4",
      "gxhx_users_2023_q1",
      "gxhx_users_2023_q2",
      "gxhx_users_2023_q3",
      "gxhx_users_2023_q4",
      "gxhx_users_20241007_2025011512",
      "gxhx_users_2024_q1",
      "gxhx_users_2024_q2",
      "gxhx_users_2024_q3",
      "gxhx_users_2024_q4",
      "gzhx_users_2018",
      "gzhx_users_2019",
      "gzhx_users_2020",
      "gzhx_users_2021",
      "gzhx_users_2022",
      "gzhx_users_2023",
      "gzhx_users_2024",
      "gzhx_users_20241007_2025011512",
      "henhx_data01",
      "henhx_data01_20241007_2025011512",
      "hennyhx_data01",
      "hennyhx_data01_20241007_2025011512",
      "hnhx_data01_2019",
      "hnhx_data01_2020",
      "hnhx_data01_2021",
      "hnhx_data01_2022",
      "hnhx_data01_2023",
      "hnhx_data01_2024",
      "hnhx_data01_20241007_2025011512",
      "schx_users_2018",
      "schx_users_2019",
      "schx_users_2020",
      "schx_users_2021",
      "schx_users_2022",
      "schx_users_2023",
      "schx_users_2024",
      "schx_users_20241007_2025011512",
      "shhx_data01",
      "shhx_data01_20241007_2025011512",
      "sxgshx_data01",
      "sxgshx_data01_20241007_2025011512",
      "sxhx_users_2020",
      "sxhx_users_2021",
      "sxhx_users_2022",
      "sxhx_users_2023",
      "sxhx_users_2024",
      "sxhx_users_20241007_2025011512",
      "tjhx_data01",
      "tjhx_data01_20241007_2025011512",
      "tjqchx_data01",
      "tjqchx_data01_20241007_2025011512",
      "ynhx_data01_20092013",
      "ynhx_data01_2021_1",
      "ynhx_data01_2021_10",
      "ynhx_data01_2021_11",
      "ynhx_data01_2021_12",
      "ynhx_data01_2021_2",
      "ynhx_data01_2021_3",
      "ynhx_data01_2021_4",
      "ynhx_data01_2021_5",
      "ynhx_data01_2021_6",
      "ynhx_data01_2021_7",
      "ynhx_data01_2021_8",
      "ynhx_data01_2021_9",
      "ynhx_data01_2022_1",
      "ynhx_data01_2022_10",
      "ynhx_data01_2022_11",
      "ynhx_data01_2022_12",
      "ynhx_data01_2022_2",
      "ynhx_data01_2022_3",
      "ynhx_data01_2022_4",
      "ynhx_data01_2022_5",
      "ynhx_data01_2022_6",
      "ynhx_data01_2022_7",
      "ynhx_data01_2022_8",
      "ynhx_data01_2022_9",
      "ynhx_data01_2023_1",
      "ynhx_data01_2023_10",
      "ynhx_data01_2023_11",
      "ynhx_data01_2023_12",
      "ynhx_data01_2023_2",
      "ynhx_data01_2023_3",
      "ynhx_data01_2023_4",
      "ynhx_data01_2023_5",
      "ynhx_data01_2023_6",
      "ynhx_data01_2023_7",
      "ynhx_data01_2023_8",
      "ynhx_data01_2023_9",
      "ynhx_data01_20241007_2025011512",
      "ynhx_data01_2024_1",
      "ynhx_data01_2024_10",
      "ynhx_data01_2024_2",
      "ynhx_data01_2024_3",
      "ynhx_data01_2024_4",
      "ynhx_data01_2024_5",
      "ynhx_data01_2024_6",
      "ynhx_data01_2024_7",
      "ynhx_data01_2024_8",
      "ynhx_data01_2024_9",
      "zyhx_users",
      "zyhx_users_20241007_2025011512");



  public static final String ING = "cqhx_users_2024\n"
      + "cqhx_users_20241007_2025011512\n"
      + "gxhx_users_20241007_2025011512\n"
      + "gxhx_users_2024_q3\n"
      + "gxhx_users_2024_q4\n"
      + "gzhx_users_2024\n"
      + "gzhx_users_20241007_2025011512\n"
      + "henhx_data01\n"
      + "henhx_data01_20241007_2025011512\n"
      + "hennyhx_data01_20241007_2025011512\n"
      + "hnhx_data01_2024\n"
      + "hnhx_data01_20241007_2025011512\n"
      + "schx_users_2024\n"
      + "schx_users_20241007_2025011512\n"
      + "shhx_data01\n"
      + "shhx_data01_20241007_2025011512\n"
      + "sxgshx_data01\n"
      + "sxgshx_data01_20241007_2025011512\n"
      + "sxhx_users_2024\n"
      + "sxhx_users_20241007_2025011512\n"
      + "tjhx_data01\n"
      + "tjhx_data01_20241007_2025011512\n"
      + "tjqchx_data01\n"
      + "tjqchx_data01_20241007_2025011512\n"
      + "ynhx_data01_20241007_2025011512\n"
      + "ynhx_data01_2024_10\n"
      + "ynhx_data01_2024_7\n"
      + "ynhx_data01_2024_8\n"
      + "ynhx_data01_2024_9";
  public static final String ID = "308\n"
      + "408\n"
      + "423\n"
      + "424\n"
      + "471\n"
      + "480\n"
      + "481\n"
      + "482\n"
      + "492\n"
      + "493\n"
      + "494\n"
      + "497\n"
      + "499\n"
      + "500\n"
      + "531\n"
      + "532\n"
      + "533\n"
      + "539\n"
      + "540\n"
      + "541\n"
      + "542\n"
      + "579\n"
      + "580\n"
      + "581\n"
      + "587\n"
      + "588\n"
      + "589\n"
      + "590\n"
      + "591\n"
      + "592\n"
      + "593\n"
      + "599\n"
      + "600\n"
      + "601\n"
      + "602\n"
      + "639\n"
      + "640\n"
      + "641\n"
      + "647\n"
      + "648\n"
      + "649\n"
      + "650\n"
      + "651\n"
      + "652\n"
      + "653\n"
      + "659\n"
      + "660\n"
      + "661\n"
      + "662\n"
      + "663\n"
      + "664\n"
      + "665\n"
      + "671\n"
      + "672\n"
      + "673\n"
      + "674\n"
      + "899\n"
      + "900\n"
      + "901\n"
      + "902\n"
      + "911\n"
      + "945\n"
      + "951\n"
      + "952\n"
      + "958\n"
      + "959";

  public static final String MIN_ID = "0\n"
      + "2603429\n"
      + "153774\n"
      + "131330\n"
      + "6202036\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "7508493\n"
      + "8440156\n"
      + "0\n"
      + "2661476\n"
      + "279112\n"
      + "0\n"
      + "1746053\n"
      + "207662\n"
      + "8102784\n"
      + "1592527\n"
      + "7639778\n"
      + "723290\n"
      + "233861\n"
      + "241120\n"
      + "0\n"
      + "1625170\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "26876\n"
      + "246284\n"
      + "0\n"
      + "1230523\n"
      + "1382164\n"
      + "369890\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "199265\n"
      + "325987\n"
      + "0\n"
      + "0\n"
      + "122961\n"
      + "0\n"
      + "173950\n"
      + "86942\n"
      + "85059\n"
      + "46852\n"
      + "247952\n"
      + "1026204\n"
      + "2382667\n"
      + "3917634\n"
      + "82344\n"
      + "8165\n"
      + "266305\n"
      + "2791206\n"
      + "533126\n"
      + "1256087";
  public static final String MAX_ID = "0\n"
      + "2603429\n"
      + "3634042\n"
      + "136110\n"
      + "6906465\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "9236968\n"
      + "9237804\n"
      + "0\n"
      + "4019457\n"
      + "3454987\n"
      + "0\n"
      + "1746132\n"
      + "8158675\n"
      + "8624409\n"
      + "8615824\n"
      + "7639790\n"
      + "8468823\n"
      + "8877038\n"
      + "8468699\n"
      + "0\n"
      + "1625207\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "6251793\n"
      + "4309321\n"
      + "0\n"
      + "1230523\n"
      + "2894503\n"
      + "1927909\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "0\n"
      + "199364\n"
      + "326086\n"
      + "0\n"
      + "0\n"
      + "122961\n"
      + "0\n"
      + "215923\n"
      + "229087\n"
      + "248575\n"
      + "243647\n"
      + "29909303\n"
      + "31274046\n"
      + "32782470\n"
      + "33362525\n"
      + "1243512\n"
      + "10111184\n"
      + "9833439\n"
      + "9833443\n"
      + "9416086\n"
      + "9416111";

  public static final String PAY_DATA_NOT_EXIST = "S13KZ04853,KZ04,tjqchx_data01_20241007_2025011512\n"
      + "S13KZ04853,KZ04,tjqchx_data01_20241007_2025011512\n"
      + "S13KZ04853,KZ04,tjqchx_data01_20241007_2025011512\n"
      + "S14KZ28134,KZ28,tjqchx_data01_20241007_2025011512\n"
      + "S14KZ28134,KZ28,tjqchx_data01_20241007_2025011512\n"
      + "S13KZ04853,KZ04,tjqchx_data01_20241007_2025011512\n"
      + "S14KZ28134,KZ28,tjqchx_data01_20241007_2025011512\n"
      + "S14KZ28134,KZ28,tjqchx_data01_20241007_2025011512\n"
      + "S17KZ30454,KZ30,tjqchx_data01\n"
      + "S17KZ30454,KZ30,tjqchx_data01\n"
      + "S17KZ30566,KZ30,tjqchx_data01\n"
      + "S17KZ30566,KZ30,tjqchx_data01\n"
      + "V200003043,E193,schx_users_2024\n"
      + "V200003043,E193,schx_users_2024\n"
      + "V200003043,E193,schx_users_2024\n"
      + "S13KZ04853,KZ04,tjqchx_data01_20241007_2025011512\n"
      + "S14KZ28134,KZ28,tjqchx_data01_20241007_2025011512\n"
      + "S14KZ28134,KZ28,tjqchx_data01_20241007_2025011512\n"
      + "V200003043,E193,schx_users_2024\n"
      + "S14KZ28134,KZ28,tjqchx_data01_20241007_2025011512\n"
      + "S13KZ04853,KZ04,tjqchx_data01_20241007_2025011512\n"
      + "S14KZ28134,KZ28,tjqchx_data01_20241007_2025011512\n"
      + "S13KZ04853,KZ04,tjqchx_data01_20241007_2025011512\n"
      + "S13KZ04853,KZ04,tjqchx_data01_20241007_2025011512\n"
      + "S13KZ04853,KZ04,tjqchx_data01_20241007_2025011512\n"
      + "S13KZ04853,KZ04,tjqchx_data01_20241007_2025011512\n"
      + "S13KZ04853,KZ04,tjqchx_data01_20241007_2025011512\n"
      + "S13KZ04853,KZ04,tjqchx_data01_20241007_2025011512\n"
      + "S14KZ28134,KZ28,tjqchx_data01_20241007_2025011512\n"
      + "S14KZ28134,KZ28,tjqchx_data01_20241007_2025011512\n"
      + "S14KZ28134,KZ28,tjqchx_data01_20241007_2025011512\n"
      + "S14KZ28134,KZ28,tjqchx_data01_20241007_2025011512\n"
      + "V200003043,E193,schx_users_2024";


  public static final String PAY_DATA_NOT_EXIST_DETAIL = "{\"id\":173950,\"thirdOrderNo\":\"S13KZ04853\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-13T15:42:32.000+0800\",\"txDate\":\"2024-10-13\",\"txTime\":\"154232\",\"orderId\":24101304853,\"actualPayAmount\":41.8600,\"actualCollectAmount\":41.8600,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ04\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":173950,\"thirdOrderNo\":\"S13KZ04853\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-13T15:42:32.000+0800\",\"txDate\":\"2024-10-13\",\"txTime\":\"154232\",\"orderId\":24101304853,\"actualPayAmount\":41.8600,\"actualCollectAmount\":41.8600,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ04\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":173950,\"thirdOrderNo\":\"S13KZ04853\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-13T15:42:32.000+0800\",\"txDate\":\"2024-10-13\",\"txTime\":\"154232\",\"orderId\":24101304853,\"actualPayAmount\":41.8600,\"actualCollectAmount\":41.8600,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ04\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":215923,\"thirdOrderNo\":\"S14KZ28134\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-14T16:41:10.000+0800\",\"txDate\":\"2024-10-14\",\"txTime\":\"164110\",\"orderId\":24101428134,\"actualPayAmount\":33.0000,\"actualCollectAmount\":33.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ28\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":215923,\"thirdOrderNo\":\"S14KZ28134\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-14T16:41:10.000+0800\",\"txDate\":\"2024-10-14\",\"txTime\":\"164110\",\"orderId\":24101428134,\"actualPayAmount\":33.0000,\"actualCollectAmount\":33.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ28\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":173950,\"thirdOrderNo\":\"S13KZ04853\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-13T15:42:32.000+0800\",\"txDate\":\"2024-10-13\",\"txTime\":\"154232\",\"orderId\":24101304853,\"actualPayAmount\":41.8600,\"actualCollectAmount\":41.8600,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ04\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":215923,\"thirdOrderNo\":\"S14KZ28134\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-14T16:41:10.000+0800\",\"txDate\":\"2024-10-14\",\"txTime\":\"164110\",\"orderId\":24101428134,\"actualPayAmount\":33.0000,\"actualCollectAmount\":33.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ28\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":215923,\"thirdOrderNo\":\"S14KZ28134\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-14T16:41:10.000+0800\",\"txDate\":\"2024-10-14\",\"txTime\":\"164110\",\"orderId\":24101428134,\"actualPayAmount\":33.0000,\"actualCollectAmount\":33.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ28\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":1047938,\"thirdOrderNo\":\"S17KZ30454\",\"thirdRefundNo\":\"\",\"createTime\":\"2023-08-17T18:53:38.000+0800\",\"txDate\":\"2023-08-17\",\"txTime\":\"185338\",\"orderId\":23081730454,\"actualPayAmount\":2600.0000,\"actualCollectAmount\":2600.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"999\",\"picker\":\"\",\"storeCode\":\"KZ30\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":1047938,\"thirdOrderNo\":\"S17KZ30454\",\"thirdRefundNo\":\"\",\"createTime\":\"2023-08-17T18:53:38.000+0800\",\"txDate\":\"2023-08-17\",\"txTime\":\"185338\",\"orderId\":23081730454,\"actualPayAmount\":2600.0000,\"actualCollectAmount\":2600.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"999\",\"picker\":\"\",\"storeCode\":\"KZ30\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":1047983,\"thirdOrderNo\":\"S17KZ30566\",\"thirdRefundNo\":\"\",\"createTime\":\"2023-08-17T21:12:14.000+0800\",\"txDate\":\"2023-08-17\",\"txTime\":\"211214\",\"orderId\":23081730566,\"actualPayAmount\":2000.0000,\"actualCollectAmount\":2000.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"999\",\"picker\":\"\",\"storeCode\":\"KZ30\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":1047983,\"thirdOrderNo\":\"S17KZ30566\",\"thirdRefundNo\":\"\",\"createTime\":\"2023-08-17T21:12:14.000+0800\",\"txDate\":\"2023-08-17\",\"txTime\":\"211214\",\"orderId\":23081730566,\"actualPayAmount\":2000.0000,\"actualCollectAmount\":2000.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"999\",\"picker\":\"\",\"storeCode\":\"KZ30\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":8616193,\"thirdOrderNo\":\"V200003043\",\"thirdRefundNo\":\"2200003022\",\"createTime\":\"2024-04-22T10:41:55.000+0800\",\"txDate\":\"2024-04-22\",\"txTime\":\"104155\",\"orderId\":1024042200003043,\"actualPayAmount\":0.0000,\"actualCollectAmount\":0.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"1019801\",\"picker\":\"\",\"storeCode\":\"E193\",\"clientCode\":\"900030131122\",\"migration\":0}\n"
      + "{\"id\":8616193,\"thirdOrderNo\":\"V200003043\",\"thirdRefundNo\":\"2200003022\",\"createTime\":\"2024-04-22T10:41:55.000+0800\",\"txDate\":\"2024-04-22\",\"txTime\":\"104155\",\"orderId\":1024042200003043,\"actualPayAmount\":0.0000,\"actualCollectAmount\":0.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"1019801\",\"picker\":\"\",\"storeCode\":\"E193\",\"clientCode\":\"900030131122\",\"migration\":0}\n"
      + "{\"id\":8616193,\"thirdOrderNo\":\"V200003043\",\"thirdRefundNo\":\"2200003022\",\"createTime\":\"2024-04-22T10:41:55.000+0800\",\"txDate\":\"2024-04-22\",\"txTime\":\"104155\",\"orderId\":1024042200003043,\"actualPayAmount\":0.0000,\"actualCollectAmount\":0.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"1019801\",\"picker\":\"\",\"storeCode\":\"E193\",\"clientCode\":\"900030131122\",\"migration\":0}\n"
      + "{\"id\":173950,\"thirdOrderNo\":\"S13KZ04853\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-13T15:42:32.000+0800\",\"txDate\":\"2024-10-13\",\"txTime\":\"154232\",\"orderId\":24101304853,\"actualPayAmount\":41.8600,\"actualCollectAmount\":41.8600,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ04\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":215923,\"thirdOrderNo\":\"S14KZ28134\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-14T16:41:10.000+0800\",\"txDate\":\"2024-10-14\",\"txTime\":\"164110\",\"orderId\":24101428134,\"actualPayAmount\":33.0000,\"actualCollectAmount\":33.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ28\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":215923,\"thirdOrderNo\":\"S14KZ28134\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-14T16:41:10.000+0800\",\"txDate\":\"2024-10-14\",\"txTime\":\"164110\",\"orderId\":24101428134,\"actualPayAmount\":33.0000,\"actualCollectAmount\":33.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ28\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":8616193,\"thirdOrderNo\":\"V200003043\",\"thirdRefundNo\":\"2200003022\",\"createTime\":\"2024-04-22T10:41:55.000+0800\",\"txDate\":\"2024-04-22\",\"txTime\":\"104155\",\"orderId\":1024042200003043,\"actualPayAmount\":0.0000,\"actualCollectAmount\":0.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"1019801\",\"picker\":\"\",\"storeCode\":\"E193\",\"clientCode\":\"900030131122\",\"migration\":0}\n"
      + "{\"id\":215923,\"thirdOrderNo\":\"S14KZ28134\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-14T16:41:10.000+0800\",\"txDate\":\"2024-10-14\",\"txTime\":\"164110\",\"orderId\":24101428134,\"actualPayAmount\":33.0000,\"actualCollectAmount\":33.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ28\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":173950,\"thirdOrderNo\":\"S13KZ04853\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-13T15:42:32.000+0800\",\"txDate\":\"2024-10-13\",\"txTime\":\"154232\",\"orderId\":24101304853,\"actualPayAmount\":41.8600,\"actualCollectAmount\":41.8600,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ04\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":215923,\"thirdOrderNo\":\"S14KZ28134\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-14T16:41:10.000+0800\",\"txDate\":\"2024-10-14\",\"txTime\":\"164110\",\"orderId\":24101428134,\"actualPayAmount\":33.0000,\"actualCollectAmount\":33.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ28\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":173950,\"thirdOrderNo\":\"S13KZ04853\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-13T15:42:32.000+0800\",\"txDate\":\"2024-10-13\",\"txTime\":\"154232\",\"orderId\":24101304853,\"actualPayAmount\":41.8600,\"actualCollectAmount\":41.8600,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ04\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":173950,\"thirdOrderNo\":\"S13KZ04853\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-13T15:42:32.000+0800\",\"txDate\":\"2024-10-13\",\"txTime\":\"154232\",\"orderId\":24101304853,\"actualPayAmount\":41.8600,\"actualCollectAmount\":41.8600,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ04\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":173950,\"thirdOrderNo\":\"S13KZ04853\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-13T15:42:32.000+0800\",\"txDate\":\"2024-10-13\",\"txTime\":\"154232\",\"orderId\":24101304853,\"actualPayAmount\":41.8600,\"actualCollectAmount\":41.8600,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ04\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":173950,\"thirdOrderNo\":\"S13KZ04853\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-13T15:42:32.000+0800\",\"txDate\":\"2024-10-13\",\"txTime\":\"154232\",\"orderId\":24101304853,\"actualPayAmount\":41.8600,\"actualCollectAmount\":41.8600,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ04\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":173950,\"thirdOrderNo\":\"S13KZ04853\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-13T15:42:32.000+0800\",\"txDate\":\"2024-10-13\",\"txTime\":\"154232\",\"orderId\":24101304853,\"actualPayAmount\":41.8600,\"actualCollectAmount\":41.8600,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ04\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":173950,\"thirdOrderNo\":\"S13KZ04853\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-13T15:42:32.000+0800\",\"txDate\":\"2024-10-13\",\"txTime\":\"154232\",\"orderId\":24101304853,\"actualPayAmount\":41.8600,\"actualCollectAmount\":41.8600,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ04\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":215923,\"thirdOrderNo\":\"S14KZ28134\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-14T16:41:10.000+0800\",\"txDate\":\"2024-10-14\",\"txTime\":\"164110\",\"orderId\":24101428134,\"actualPayAmount\":33.0000,\"actualCollectAmount\":33.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ28\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":215923,\"thirdOrderNo\":\"S14KZ28134\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-14T16:41:10.000+0800\",\"txDate\":\"2024-10-14\",\"txTime\":\"164110\",\"orderId\":24101428134,\"actualPayAmount\":33.0000,\"actualCollectAmount\":33.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ28\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":215923,\"thirdOrderNo\":\"S14KZ28134\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-14T16:41:10.000+0800\",\"txDate\":\"2024-10-14\",\"txTime\":\"164110\",\"orderId\":24101428134,\"actualPayAmount\":33.0000,\"actualCollectAmount\":33.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ28\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":215923,\"thirdOrderNo\":\"S14KZ28134\",\"thirdRefundNo\":\"\",\"createTime\":\"2024-10-14T16:41:10.000+0800\",\"txDate\":\"2024-10-14\",\"txTime\":\"164110\",\"orderId\":24101428134,\"actualPayAmount\":33.0000,\"actualCollectAmount\":33.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"998\",\"picker\":\"\",\"storeCode\":\"KZ28\",\"clientCode\":\"\",\"migration\":0}\n"
      + "{\"id\":8616193,\"thirdOrderNo\":\"V200003043\",\"thirdRefundNo\":\"2200003022\",\"createTime\":\"2024-04-22T10:41:55.000+0800\",\"txDate\":\"2024-04-22\",\"txTime\":\"104155\",\"orderId\":1024042200003043,\"actualPayAmount\":0.0000,\"actualCollectAmount\":0.0000,\"posCashierDeskNo\":\"01\",\"cashier\":\"1019801\",\"picker\":\"\",\"storeCode\":\"E193\",\"clientCode\":\"900030131122\",\"migration\":0}";
}
