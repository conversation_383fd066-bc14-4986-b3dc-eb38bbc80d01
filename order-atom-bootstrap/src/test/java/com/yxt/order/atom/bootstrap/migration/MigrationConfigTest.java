package com.yxt.order.atom.bootstrap.migration;

import static com.yxt.order.atom.bootstrap.migration.Constant.HANA_MIGRATION_CONFIG_LIST;
import static com.yxt.order.atom.bootstrap.migration.Constant.PAY_DATA_NOT_EXIST;
import static com.yxt.order.atom.bootstrap.migration.Constant.PAY_DATA_NOT_EXIST_DETAIL;
import static com.yxt.order.atom.bootstrap.migration.Constant.SCHEMA_COMPANY_MAP;
import static com.yxt.order.atom.bootstrap.migration.Constant.UN_DONE_ORDER;
import static com.yxt.order.atom.bootstrap.migration.Constant.UN_DONE_ORDER_SCHEMA;
import static com.yxt.order.atom.bootstrap.migration.MigrationUtils.longV;

import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.atom.repair.dto.StartEndId;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import org.junit.Test;

/**
 * @author: moatkon
 * @time: 2025/2/26 14:45
 */
public class MigrationConfigTest  {

  @Test
  public void generateCurl(){
    String path = "D:\\github\\codes\\order-atom-service\\order-atom-bootstrap\\src\\test\\java\\com\\yxt\\order\\atom\\bootstrap\\migration\\id_schema.log";
    List<String> orderList = FileUtil.readLines(new File(path), StandardCharsets.UTF_8);
    Multimap<String,String> orderMultiMap = HashMultimap.create(); // k: schema v:订单total_id
    for (String order : orderList) {
      String[] split = order.split(",");
      String id = split[0];
      String schema = split[1];
      orderMultiMap.put(schema,id);
    }

    Map<String, Collection<String>> map = orderMultiMap.asMap();
    for (String orderSchema : map.keySet()) {
      Collection<String> orderPkIdList = map.get(orderSchema);

      for (List<String> pIdList : Lists.partition(Lists.newArrayList(orderPkIdList), 100)) {
//        String curlFormat = String.format("curl -X POST \"http://localhost:8080/specify-archive-batch\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
//            + "\"{\\\"hanaOrderInfoIdList\\\":"+JsonUtils.toJson(pIdList)+",\\\"migrationSort\\\":\\\"ORDER\\\",\\\"targetSchema\\\":\\\""+orderSchema+"\\\"}\"");
//        System.out.println(curlFormat);

        // 生成校验SQL
        for (String id : pIdList) {
          String sqlFormat = String.format("select migration,extend_json from xf_transsalestotal_%s where id = %s union all ",orderSchema,id);
          System.out.println(sqlFormat);
        }
//
//        System.out.println();
      }

    }








//    for (String content : orderList) {
//      System.out.println(content);
//    }

  }

  @Test
  public void testGetUnDownOrder(){

    String[] countArr = UN_DONE_ORDER.split("\n");
    String[] schemaArr = UN_DONE_ORDER_SCHEMA.split("\n");
//    System.out.println(countArr.length);
//    System.out.println(schemaArr.length);

    for (int i = 0; i < countArr.length; i++) {

      String count = countArr[i];
      if("0".equals(count)){
        continue;
      }

      String schema = schemaArr[i];

      String sql = String.format(
          "select  '%s' as targetSchema,t.* from xf_transsalestotal_%s t where migration = 0 and XF_CREATETIME>=\"2024-07-31 00:00:00\" AND XF_CREATETIME<\"2025-01-16 00:00:00\" and XF_SELLINGAMOUNT >= 0 union all "
          , schema, schema);

      System.out.println(sql);

    }


  }

  @Test
  public void testCheckPayDataDetail(){
    String[] hanaOrderInfoArr = PAY_DATA_NOT_EXIST_DETAIL.split("\n");
    String[] schemaArr = PAY_DATA_NOT_EXIST.split("\n");
    for (int i = 0; i < hanaOrderInfoArr.length; i++) {
      HanaOrderInfo hanaOrderInfo = JsonUtils.toObject(hanaOrderInfoArr[i], new TypeReference<HanaOrderInfo>() {
      });

      String schema = schemaArr[i].split(",")[2];

      String format = String.format("select migration from xf_transsalestotal_%s where id = %s union all  ", schema,
          hanaOrderInfo.getId());

//      String format = String.format("update xf_transsalestotal_%s set migration = 5 where id = %s and migration = 0; ", schema,
//          hanaOrderInfo.getId());



      System.out.println(format);
    }
  }
  @Test
  public void testCheckPayData(){
    for (String row : PAY_DATA_NOT_EXIST.split("\n")) {
      String[] cellArr = row.split(",");
      String orderNo = cellArr[0];
      String storeCode = cellArr[1];
      String mysqlSchema = cellArr[2];

      String hanaSchema = fetchHanaSchemaFromDefineSchema(mysqlSchema);

      String order = String.format("select * from %s.XF_TRANSSALESTOTAL p  where p.XF_DOCNO = '%s' and p.XF_STORECODE = '%s';",hanaSchema,orderNo,storeCode);
      String pay = String.format("select * from %s.XF_TRANSSALESTENDER p  where p.XF_DOCNO = '%s' and p.XF_STORECODE = '%s';",hanaSchema,orderNo,storeCode);
      System.out.println(order);
      System.out.println(pay);
      System.out.println();
    }
  }

  private String fetchHanaSchemaFromDefineSchema(String defineSchema){
    for (String key : SCHEMA_COMPANY_MAP.keySet()) {
      if(defineSchema.toLowerCase().contains(key.toLowerCase())){
        return key;
      }
    }
    throw new RuntimeException("解析失败");
  }


  @Test
  public void testGenerateConfigJson(){
    String[] idArr = Constant.ID.split("\n");
    String[] minIdArr = Constant.MIN_ID.split("\n");
    String[] maxIdArr = Constant.MAX_ID.split("\n");

    System.out.println(idArr.length);
    System.out.println(minIdArr.length);
    System.out.println(maxIdArr.length);

    for (int i = 0; i <= idArr.length - 1; i++) {

      StartEndId startEndId = new StartEndId();
      startEndId.setStartId(longV(minIdArr[i]));
      startEndId.setEndId(longV(maxIdArr[i]));

      String sql = String.format("update hana_migration set config_json = '%s' where id = %s;",
          JsonUtils.toJson(startEndId),
          idArr[i]);
      System.out.println(sql);


    }


  }

  @Test
  public void testFirstBatch(){
    System.out.println(HANA_MIGRATION_CONFIG_LIST.size());
    for (String s : HANA_MIGRATION_CONFIG_LIST) {
      System.out.println("select count(1) count, '"+s+"' as targetSchema from xf_transsalestotal_"+s+" where migration = 0 and XF_CREATETIME>=\"2024-07-31 00:00:00\" AND XF_CREATETIME<\"2025-01-16 00:00:00\" and XF_SELLINGAMOUNT >= 0 union all ");
    }
  }





  public static void main(String[] args) {
    StartEndId startEndId = new StartEndId();
    startEndId.setStartId(650L);
    startEndId.setEndId(6622306L);
    System.out.println(JsonUtils.toJson(startEndId));
  }

  @Test
  public void generateDeleteOrderSQL(){
    String sqlStr = "    delete from offline_order_cashier_desk where  order_no = #{orderNo};\n"
        + "    delete from offline_order_coupon where  order_no = #{orderNo};\n"
        + "    delete from offline_order_detail where  order_no = #{orderNo};\n"
        + "    delete from offline_order_detail_pick where  order_no = #{orderNo};\n"
        + "    delete from offline_order_med_ins_settle where  order_no = #{orderNo};\n"
        + "    delete from offline_order_organization where  order_no = #{orderNo};\n"
        + "    delete from offline_order_pay where  order_no = #{orderNo};\n"
        + "    delete from offline_order_prescription where  order_no = #{orderNo};\n"
        + "    delete from offline_order_promotion where  order_no = #{orderNo};\n"
        + "    delete from offline_order_user where  order_no = #{orderNo};";

    String[] split = sqlStr.split("\n");
    for (int i = 0; i < split.length; i++) {
      String sql = "<delete id=\"deleteAll"+i+"\">\n"
          + "    "+split[i]+"\n"
          + "  </delete>";

      System.out.println(sql);
    }
  }
    @Test
  public void generateDeleteRefundOrderSQL(){
    String sqlStr = "delete from offline_refund_order_cashier_desk where refund_no = #{refundNo};\n"
        + "    delete from offline_refund_order_detail where refund_no = #{refundNo};\n"
        + "    delete from offline_refund_order_med_ins_settle where refund_no = #{refundNo};\n"
        + "    delete from offline_refund_order_organization where refund_no = #{refundNo};\n"
        + "    delete from offline_refund_order_pay where refund_no = #{refundNo};\n"
        + "    delete from offline_refund_order_user where refund_no = #{refundNo};";

    String[] split = sqlStr.split("\n");
    for (int i = 0; i < split.length; i++) {
      String sql = "  <delete id=\"deleteAll"+i+"\">\n"
          + "    "+split[i]+"\n"
          + "  </delete>";

      System.out.println(sql);
    }
  }

}
