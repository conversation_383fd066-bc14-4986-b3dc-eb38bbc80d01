package com.yxt.order.atom.bootstrap.migration;

import com.yxt.lang.util.JsonUtils;
import com.yxt.order.types.repair.RepairScene;
import java.util.List;
import java.util.Objects;
import lombok.Data;

public class URL {

  @Data
  public static class URLParam {

    private Long startId;
    private Long endId;
    private String startDateStr;
    private String endDateStr;
    private String scene;
    private RepairScene repairScene;
    private List<String> shardingNoList;
    private Boolean onlyHandleKeChuan = false;
    private String suffix;
    private String targetSchema;

    public String startEndIdScene() {
      return String.format("%s_%s", this.startId, this.endId);
    }


  }

  public static String getFixOfflineRefundOrderCreatedUrl(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/fixOfflineRefundOrderCreated\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"startId\\\":%s,\\\"endId\\\":%s,\\\"scene\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s}\"";
    return String.format(format, param.getStartId(), param.getEndId(), param.getScene(),
        JsonUtils.toJson(param.getShardingNoList()));
  }

  public static String getFixOfflineOrderCreatedUrl(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/fixOfflineOrderCreated\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"startId\\\":%s,\\\"endId\\\":%s,\\\"scene\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s}\"";
    return String.format(format, param.getStartId(), param.getEndId(), param.getScene(),
        JsonUtils.toJson(param.getShardingNoList()));
  }

  public static String getRemoveEsOrderRepeatedDataUrl(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/removeEsOrderRepeatedData\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"startId\\\":%s,\\\"endId\\\":%s,\\\"scene\\\":\\\"%s\\\",\\\"suffix\\\":\\\"%s\\\"}\"";
    return String.format(format, param.getStartId(), param.getEndId(), param.getScene(),
        param.getSuffix());
  }

  public static String getRemoveRepeatedRefundOrderDataUrl(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/removeRepeatedRefundOrderData\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"endId\\\":%s,\\\"onlyHandleKeChuan\\\":%s,\\\"scene\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s,\\\"startId\\\":%s}\"";
    return String.format(format, param.getEndId(), param.getOnlyHandleKeChuan(), param.getScene(),
        JsonUtils.toJson(param.getShardingNoList()), param.getStartId());
  }

  public static String getRemoveRepeatedOrderDataUrl(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/removeRepeatedOrderData\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"endId\\\":%s,\\\"onlyHandleKeChuan\\\":%s,\\\"scene\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s,\\\"startId\\\":%s}\"";
    return String.format(format, param.getEndId(), param.getOnlyHandleKeChuan(), param.getScene(),
        JsonUtils.toJson(param.getShardingNoList()), param.getStartId());
  }

  public static String getFixUnprocessableOfflineOrderUrl(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/fixUnprocessableOfflineOrder\" -H \"Request-Origion:SwaggerBootstrapUi\" -H \"accept:*/*\" -H \"Authorization:\" -H \"empCode:\" -H \"userId:\" -H \"Content-Type:application/json\" -d "
            + "\"{\\\"startId\\\":%s,\\\"endId\\\":%s,\\\"onlyHandleKeChuan\\\":%s,\\\"scene\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s}\"";
    return String.format(format, param.getStartId(), param.getEndId(),
        param.getOnlyHandleKeChuan(), param.getScene(),
        JsonUtils.toJson(param.getShardingNoList()));
  }

  public static String getFixUnprocessableOfflineRefundOrderUrl(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/fixUnprocessableOfflineRefundOrder\" -H \"Request-Origion:SwaggerBootstrapUi\" -H \"accept:*/*\" -H \"Authorization:\" -H \"empCode:\" -H \"userId:\" -H \"Content-Type:application/json\" -d "
            + "\"{\\\"startId\\\":%s,\\\"endId\\\":%s,\\\"onlyHandleKeChuan\\\":%s,\\\"scene\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s}\"";
    return String.format(format, param.getStartId(), param.getEndId(),
        param.getOnlyHandleKeChuan(), param.getScene(),
        JsonUtils.toJson(param.getShardingNoList()));
  }

  public static String getFixAmount(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/flushAmtAmount\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"startId\\\":%s,\\\"endId\\\":%s,\\\"scene\\\":\\\"%s\\\",\\\"targetSchema\\\":\\\"%s\\\"}\"";
    return String.format(format, param.getStartId(), param.getEndId(), param.getScene(),
        param.getTargetSchema());
  }

  public static String getFlashEsOrderUrl(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/1.0/es-order/flashDataToEs\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"startDate\\\":\\\"%s\\\",\\\"endDate\\\":\\\"%s\\\",\\\"monitorKey\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s}\"";
    return String.format(format, param.getStartDateStr(), param.getEndDateStr(), param.getScene(),
        JsonUtils.toJson(param.getShardingNoList()));
  }


  public static String getFlashEsMemberOrderUrl(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/1.0/es-member-order/flashDataToEs\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"startDate\\\":\\\"%s\\\",\\\"endDate\\\":\\\"%s\\\",\\\"monitorKey\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s}\"";
    return String.format(format, param.getStartDateStr(), param.getEndDateStr(), param.getScene(),
        JsonUtils.toJson(param.getShardingNoList()));
  }

  public static String getFixKeChuanAmountUrl(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/fix/ke-chuan/detail-amount-error\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"startDate\\\":\\\"%s\\\",\\\"endDate\\\":\\\"%s\\\",\\\"monitorKey\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s}\"";
    return String.format(format, param.getStartDateStr(), param.getEndDateStr(), param.getScene(),
        JsonUtils.toJson(param.getShardingNoList()));
  }


  public static String getFixHaidianErrorByScene(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/abstractOrderRepairData\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"startId\\\":%s,\\\"endId\\\":%s,\\\"monitoryKey\\\":\\\"%s\\\",\\\"repairScene\\\":\\\"%s\\\"}\"";

    return String.format(format, param.getStartId(), param.getEndId(), param.getScene(),
        param.getRepairScene().name());
  }

  public static String getGenerateRemoveHaiDian(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/removeHdRepeatedOrder\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"startId\\\":%s,\\\"endId\\\":%s,\\\"targetSchema\\\":\\\"%s\\\",\\\"scene\\\":\\\"%s\\\"}\"";

    return String.format(format, param.getStartId(), param.getEndId(), param.getTargetSchema(),
        param.getScene());
  }

  public static String offlineOrderPlatformCheckDataCollect(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/offlineOrderPlatformCheckDataCollect\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"startId\\\":%s,\\\"endId\\\":%s,\\\"targetSchema\\\":\\\"%s\\\",\\\"scene\\\":\\\"%s\\\"}\"";

    return String.format(format, param.getStartId(), param.getEndId(), param.getTargetSchema(),
        param.getScene());
  }


  public static String fixOfflineOrderPlatformCode(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/fixOfflineOrderMistakePlatformData\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"scene\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s}\"";
    if (Objects.nonNull(param.getStartId()) && Objects.nonNull(param.getEndId())) {
      format =
          "curl -X POST \"http://localhost:8080/fixOfflineOrderMistakePlatformData\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" "
              + "-d \"{\\\"scene\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s,\\\"startId\\\":%s,\\\"endId\\\":%s}\"";
      return String.format(format,
          String.format("%s_%s_%s", param.getScene(), param.getStartId(), param.getEndId()),
          JsonUtils.toJson(param.getShardingNoList()), param.getStartId(), param.getEndId());
    }

    return String.format(format, param.getScene(), JsonUtils.toJson(param.getShardingNoList()));
  }


  public static String fixOfflineRefundOrderPlatformCode(URLParam param) {
    String format =
        "curl -X POST \"http://localhost:8080/fixOfflineRefundOrderMistakePlatformData\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"scene\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s}\"";

    if (Objects.nonNull(param.getStartId()) && Objects.nonNull(param.getEndId())) {
      format =
          "curl -X POST \"http://localhost:8080/fixOfflineRefundOrderMistakePlatformData\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" "
              + "-d \"{\\\"scene\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s,\\\"startId\\\":%s,\\\"endId\\\":%s}\"";
      return String.format(format, param.getScene(), JsonUtils.toJson(param.getShardingNoList()),
          param.getStartId(), param.getEndId());
    }
    return String.format(format, param.getScene(), JsonUtils.toJson(param.getShardingNoList()));
  }

  public static String flashEsMemberOrderOnlineData(URLParam param) {
    String template =
        "curl -X POST \"http://localhost:8080/2.0/es-member-order/flashDataToEs\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"startDate\\\":\\\"%s\\\",\\\"endDate\\\":\\\"%s\\\",\\\"flashType\\\":\\\"ONLINE\\\",\\\"monitorKey\\\":\\\"%s\\\"}\"";
    return String.format(template, param.getStartDateStr(), param.getEndDateStr(),
        param.getScene());
  }

  public static String flashEsMemberOrderOfflinelineData(URLParam param) {
    String template =
        "curl -X POST \"http://localhost:8080/2.0/es-member-order/flashDataToEs\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"startDate\\\":\\\"%s\\\",\\\"endDate\\\":\\\"%s\\\",\\\"flashType\\\":\\\"OFFLINE\\\",\\\"monitorKey\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s}\"";
    return String.format(template, param.getStartDateStr(), param.getEndDateStr(), param.getScene(),
        JsonUtils.toJson(param.getShardingNoList()));
  }

  public static String flashOfflineOrderManageEs(URLParam param) {
    String template =
        "curl -X POST \"http://order-atom-service.svc.k8s.test.hxyxt.com/1.0/offline-order-manage/flashDataToEs\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"startDate\\\":\\\"%s\\\",\\\"endDate\\\":\\\"%s\\\",\\\"monitorKey\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s}\"";
    return String.format(template, param.getStartDateStr(), param.getEndDateStr(), param.getScene(),
        JsonUtils.toJson(param.getShardingNoList()));
  }


  public static String flashEsOfflineOrderManageData(URLParam param) {
    String template =
        "curl -X POST \"http://localhost:8080/1.0/offline-order-manage/flashDataToEs\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" "
            + "-d \"{\\\"startDate\\\":\\\"%s\\\",\\\"endDate\\\":\\\"%s\\\",\\\"monitorKey\\\":\\\"%s\\\",\\\"shardingValueList\\\":%s}\"";

    return String.format(template, param.getStartDateStr(), param.getEndDateStr(), param.getScene(),
        JsonUtils.toJson(param.getShardingNoList()));
  }


  public static String cleanMigratedData(URLParam param) {
    String template =
        "curl -X POST \"http://localhost:8080/stage2/remigrate/order-data-relation-hana-old-delete\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"endId\\\":%s,\\\"scene\\\":\\\"%s\\\",\\\"startId\\\":%s}\"";

    return String.format(template, param.getEndId(), param.getScene(), param.getStartId());
  }


  public static String collectHdWrongPlatformCode(URLParam param) {
    String template =
        "curl -X POST \"http://localhost:8080/proofreadingMigrationDataOfThirdOrderNoWithPlatformCode\" -H  \"Request-Origion:SwaggerBootstrapUi\" -H  \"accept:*/*\" -H  \"Authorization:\" -H  \"empCode:\" -H  \"userId:\" -H  \"Content-Type:application/json\" -d "
            + "\"{\\\"targetSchema\\\":\\\"%s\\\",\\\"scene\\\":\\\"%s\\\",\\\"startId\\\":%s,\\\"endId\\\":%s}\"";

    return String.format(template, param.getTargetSchema(), param.getScene(), param.getStartId(),
        param.getEndId());
  }

  public static String fixHdWrongPlatformCode(URLParam param) {
    String template =
        "curl -X POST \"http://localhost:8080/proofreadingMigrationDataOfThirdOrderNoWithPlatformCode2Fix\" -H \"Request-Origion:SwaggerBootstrapUi\" -H \"accept:*/*\" -H \"Authorization:\" -H \"empCode:\" -H \"userId:\" -H \"Content-Type:application/json\" -d "
            + "\"{\\\"scene\\\":\\\"%s\\\",\\\"startId\\\":%s,\\\"endId\\\":%s}\"";

    return String.format(template, param.getScene(), param.getStartId(),
        param.getEndId());
  }




}
