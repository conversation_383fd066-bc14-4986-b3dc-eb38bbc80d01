package com.yxt.order.atom.bootstrap.migration.stage_2;

import java.io.File;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.List;
import lombok.Data;
import org.apache.commons.io.FileUtils;
import org.junit.Test;

/**
 * 生成进度配置
 */
public class GetHanaMigrationStage2MigrateFail {

  // 所有的
//  private final String fileName = "D:\\github\\codes\\order-atom-service\\order-atom-bootstrap\\src\\test\\java\\com\\yxt\\order\\atom\\bootstrap\\migration\\stage_2\\所有的schema.log";

  // 正在需要迁移的文件名
  private final String actualNeedHandleFileName = "D:\\github\\codes\\order-atom-service\\order-atom-bootstrap\\src\\test\\java\\com\\yxt\\order\\atom\\bootstrap\\migration\\stage_2\\真正需要迁移的正单schema.log";


  /**
   * 初始化迁移正单的起止Id
   */
  @Test
  public void testInitMigrationOrderFail() throws IOException {

    String template = "select '%s',t.* "
        + "from xf_transsalestotal_%s  as t where XF_CREATETIME >= '%s' and XF_CREATETIME < '%s' and migration = 0 and XF_NETAMOUNT %s 0";

//    List<String> allSchemaList = FileUtils.readLines(new File(fileName), Charset.defaultCharset());
    List<String> allSchemaList = FileUtils.readLines(new File(actualNeedHandleFileName), Charset.defaultCharset());

    int count = 0;
    int size = allSchemaList.size();

    for (String schema : allSchemaList) {
      count = count + 1;
      Process templateField = new Process();
      templateField.setMigrationOrder("ORDER");
      templateField.setTargetSchema(schema);
      templateField.setStartTime("2024-04-10 00:00:00");
      templateField.setEndTime("2024-07-31 00:00:00");

      String result = String.format(template,templateField.getTargetSchema(), templateField.getTargetSchema(),
          templateField.getStartTime(),
          templateField.getEndTime(),
          "ORDER".equals(templateField.getMigrationOrder()) ? ">=" : "<");

      if (count != size) {
        result = result + " union all ";
      }
      System.out.println(result);

    }

  }

  @Test
  public void testGenerateMigrationRefund() {

  }


  @Data
  public class Process {

    private String migrationOrder;
    private String targetSchema;
    private String startTime;
    private String endTime;

  }

}
