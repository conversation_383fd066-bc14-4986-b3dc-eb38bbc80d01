select count(1) from xf_transsalestotal_cdhx_users_2024_q2 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_cdhx_users_2024_q3 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_cqhx_users_2024 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_gxhx_users_2024_q2 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_gxhx_users_2024_q3 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_gzhx_users_2024 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_henhx_data01 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_hennyhx_data01 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_hnhx_data01_2024 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_schx_users_2024 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_shhx_data01 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_sxgshx_data01 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_sxhx_users_2024 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_tjhx_data01 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_tjqchx_data01 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_ynhx_data01_2024_4 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_ynhx_data01_2024_5 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_ynhx_data01_2024_6 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0 union all
select count(1) from xf_transsalestotal_ynhx_data01_2024_7 where XF_CREATETIME >= '2024-04-10 00:00:00' and XF_CREATETIME < '2024-07-31 00:00:00' and migration = 0 and XF_NETAMOUNT >= 0