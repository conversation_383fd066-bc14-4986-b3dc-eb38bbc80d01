package com.yxt.order.atom.open.sdk.domain.es_order;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.open.sdk.req.es_order.EsOrderQueryReqDTO;
import com.yxt.order.atom.open.sdk.res.es_order.EsOrderResDTO;
import com.yxt.order.atom.open.sdk.res.es_order.EsOrderUserIdResDTO;
import io.swagger.annotations.ApiOperation;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月07日 16:43
 * @email: <EMAIL>
 */
public interface EsOrderApi {

  String URL = "/1.0/es-order-open-sdk";


  @ApiOperation(value = "[es]查询订单列表UserId记录,只通过店、品(多个)、时间")
  @PostMapping(URL + "/page-query-userId")
  ResponseBase<PageDTO<EsOrderUserIdResDTO>> pageQueryUserId(
      @Valid @RequestBody EsOrderQueryReqDTO reqDTO);


  @ApiOperation(value = "[es]查询订单列表记录")
  @PostMapping(URL + "/page-query")
  ResponseBase<PageDTO<EsOrderResDTO>> pageQuery(
      @Valid @RequestBody EsOrderQueryReqDTO reqDTO);


}
