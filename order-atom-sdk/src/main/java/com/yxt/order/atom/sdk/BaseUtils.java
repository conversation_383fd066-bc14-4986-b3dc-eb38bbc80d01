package com.yxt.order.atom.sdk;

import java.util.Date;
import java.util.Objects;
import org.apache.logging.log4j.util.Strings;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年07月03日 10:39
 * @email: <EMAIL>
 */
public class BaseUtils {

  public static String getNumStr(Date date) {
    if (Objects.isNull(date)) {
      return Strings.EMPTY;
    }
    return date.toString().replaceAll("[^0-9]", "");
  }

  public static void main(String[] args) {
    System.out.println(getNumStr(new Date()));
  }
}
