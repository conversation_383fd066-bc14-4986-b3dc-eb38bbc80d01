package com.yxt.order.atom.sdk.account_info.req;

import lombok.Getter;

@Getter
public enum AccountFialEnum {

  /**
   * 库存异常
   */
  STOCK_ERROR(1,"库存"),

  /**
   * 金额异常
   */
  AMOUNT_ERROR(2,"金额");

  private Integer code;
  private String msg;

  AccountFialEnum(Integer code, String msg) {
    this.code = code;
    this.msg = msg;
  }

  public Integer getCode() {
    return code;
  }

  public String getMsg() {
    return msg;
  }

  public static String getMsgByCode(Integer code) {
    for (AccountFialEnum accountFialEnum : AccountFialEnum.values()) {
      if(accountFialEnum.getCode().equals(code)) {
        return accountFialEnum.getMsg();
      }
    }
    return STOCK_ERROR.msg;
  }

}
