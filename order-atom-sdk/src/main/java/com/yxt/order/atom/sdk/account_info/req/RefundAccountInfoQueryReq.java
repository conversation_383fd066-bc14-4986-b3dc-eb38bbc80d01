package com.yxt.order.atom.sdk.account_info.req;

import com.yxt.lang.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class RefundAccountInfoQueryReq extends PageBase {
  /**
   * 三方平台订单号
   */
  @ApiModelProperty(value = "三方平台订单号")
  private String thirdOrderNo;

  private String erpCode;

  /**
   * 系统订单号
   */
  @ApiModelProperty(value = "系统订单号")
  private String orderNo;

  /**
   * HD_H1-海典H1  HD_H2-海典H2  KC-科传
   */
  @ApiModelProperty(value = "HD_H1-海典H1  HD_H2-海典H2  KC-科传")
  private String posMode;

  /**
   * OMS-心云作业   WMS-erp作业  O2O统一默认为 OMS
   */
  @ApiModelProperty(value = "OMS-心云作业   WMS-erp作业  O2O统一默认为 OMS")
  private String pickType;

  /**
   * 三方平台编码：27 美团、24 饿百、11 京东到家、43 微商城、48 阿里健康、44 平安中心仓、46 平安城市仓、45 平安O2O、1001 京东健康
   */
  @ApiModelProperty(value = "三方平台编码：27 美团、24 饿百、11 京东到家、43 微商城、48 阿里健康、44 平安中心仓、46 平安城市仓、45 平安O2O、1001 京东健康")
  private String thirdPlatCode;

  /**
   * 所属机构编码
   */
  @ApiModelProperty(value = "所属机构编码")
  private String organizationCode;

  /**
   * 组织机构父链路id路径
   */
  @ApiModelProperty(value = "组织机构父链路id路径")
  private String orgParentPath;

  /**
   * 下账机构编码 传入到pos下账的机构编码
   */
  @ApiModelProperty(value = "下账机构编码 传入到pos下账的机构编码")
  private String accOrganizationCode;

  /**
   * 下账机构父路径链路
   */
  @ApiModelProperty(value = "下账机构父路径链路")
  private String accOrgParentPath;


  /**
   * 下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败
   */
  @ApiModelProperty(value = "下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败")
  private String state;

  @ApiModelProperty(value = "零售流水号")
  private String saleNo;

  /**
   * 子公司编码
   * */
  @ApiModelProperty(value = "子公司编码 传orgId  全部传 -99")
  private String subCompanyCode;

  private List<String> subCompanyList;


  /**
   * 创建时间
   */
  @ApiModelProperty(value = "下单开始时间")
  private String startAcceptTime;

  @ApiModelProperty(value = "下单结束时间")
  private String endAcceptTime;

  @ApiModelProperty(value = "下账开始时间")
  private String startAccountTime;

  @ApiModelProperty(value = "下账结束时间")
  private String endAccountTime;

  private String startCreateTime;

  private String endCreateTime;


}
