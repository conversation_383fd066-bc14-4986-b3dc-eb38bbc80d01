package com.yxt.order.atom.sdk.biz_log;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.biz_log.req.SaveBizLogReq;
import com.yxt.order.atom.sdk.biz_log.req.SearchBizLogBatchReq;
import com.yxt.order.atom.sdk.biz_log.req.SearchBizLogReq;
import com.yxt.order.atom.sdk.common.order_world.BizLogInfoDTO;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface BizLogAtomApi {

  @PostMapping(ORDER_ENDPOINT + "/log/save")
  ResponseBase<Void> saveLog(@RequestBody SaveBizLogReq req);

  @PostMapping(ORDER_ENDPOINT + "/log/search")
  ResponseBase<List<BizLogInfoDTO>> searchLog(@RequestBody SearchBizLogReq req);

  @PostMapping(ORDER_ENDPOINT + "/log/search/batch")
  ResponseBase<List<BizLogInfoDTO>> searchLogBatch(@RequestBody SearchBizLogBatchReq req);
}
