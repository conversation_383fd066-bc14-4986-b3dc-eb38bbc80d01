package com.yxt.order.atom.sdk.common.data;

import lombok.Data;

@Data
public class CommodityStockChangeRecordDTO {

  private String merCode;

  private String orderNo;

  private String afterSaleNo;

  private String businessType;

  private Long serialNumber;

  private String erpCode;

  private Integer erpCount;

  /**
   * 订单所属组织机构编码
   */
  private String orderOrganizationCode;

  /**
   * 库存所属的组织机构编码
   */
  private String stockOrganizationCode;

  /**
   * 1 锁库存 2 解锁库存3，锁定库存中4，解锁库存中5，解锁库存失败6，锁库存失败
   */
  private Integer type;


  private String remark;

}
