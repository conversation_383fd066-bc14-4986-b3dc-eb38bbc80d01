package com.yxt.order.atom.sdk.common.order_world;

import com.yxt.order.common.utils.YxtDateUtils;
import java.time.LocalDateTime;
import java.time.ZoneId;
import lombok.Data;

@Data
public class BizLogInfoDTO {

  // 什么人，在什么时间、什么环境，对什么东西，做了什么操作，产生了什么结果
  /**
   * 操作人id
   */
  private String operatorId;

  /**
   * 操作人名
   */
  private String operatorName;

  /**
   * 操作时间
   */
  private LocalDateTime operateTime;

  /**
   * 操作时间戳(毫秒级)
   */
  private Long operateTimeStamp;

  /**
   * 操作服务
   */
  private String operateService;

  /**
   * 操作产生的traceId
   */
  private String traceId;

  /**
   * 被操作的业务对象标识id，如订单号，售后单号等
   */
  private String bizNo;

  /**
   * 业务场景，如订单、售后单等
   */
  private String bizScene;

  /**
   * 具体执行的动作，如创建、修改、删除等
   */
  private String bizAction;

  /**
   * 操作结果
   */
  private Boolean bizResult;

  /**
   * 操作结果描述
   */
  private String bizResultDesc;

  /**
   * 扩展字段1
   */
  private String extensionNum1;

  /**
   * 扩展字段2
   */
  private String extensionNum2;

  /**
   * 扩展字段3
   */
  private String extensionNum3;

  /**
   * 扩展字段，存储一些自定义的扩展信息，以JSON String格式存储
   */
  private String extJson;

  public void setOperateTime(LocalDateTime operateTime) {
    this.operateTime = operateTime;
    this.operateTimeStamp = operateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
  }
}
