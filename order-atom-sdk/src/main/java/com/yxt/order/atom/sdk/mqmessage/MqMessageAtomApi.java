package com.yxt.order.atom.sdk.mqmessage;


import static com.yxt.order.atom.sdk.OrderAtomServiceName.MQ_MESSAGE_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.mqmessage.req.MqMessageDeleteReqDto;
import com.yxt.order.atom.sdk.mqmessage.req.SaveMqMessageReqDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月01日 14:31
 * @email: <EMAIL>
 */
public interface MqMessageAtomApi {

  /**
   * 保存线下单
   *
   * @param saveOfflineOrderReqDto
   */
  @PostMapping(MQ_MESSAGE_ENDPOINT + "/save")
  ResponseBase<Boolean> save(
      @RequestBody SaveMqMessageReqDto saveOfflineOrderReqDto);


  @PostMapping(MQ_MESSAGE_ENDPOINT + "/delete")
  ResponseBase<Boolean> delete(
      @RequestBody MqMessageDeleteReqDto deleteReqDto);
}
