package com.yxt.order.atom.sdk.mqmessage.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月15日 16:17
 * @email: <EMAIL>
 */
@Data
public class SaveMqMessageReqDto {

  private Long id; // update时有

  private String msgId;

  private String msg;

  private String mqMsgStatus;

  private String remark;

  private String msgType;

  private String createdBy;

  private String updatedBy;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date createdTime;


  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date updatedTime;

  private Long version;
}
