package com.yxt.order.atom.sdk.offline_order;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.OFFLINE_ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.offline_order.dto.ExistOfflineOrderResDto;
import com.yxt.order.atom.sdk.offline_order.dto.ExistOfflineRefundOrderResDto;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderStagingReqDto;
import com.yxt.order.atom.sdk.offline_order.req.CompensateHdMissPromotionCouponReqDto;
import com.yxt.order.atom.sdk.offline_order.req.CompensateRefundDataReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderExistsReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderExistsReqDto;
import com.yxt.order.atom.sdk.offline_order.req.SaveOfflineOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.req.SaveOfflineRefundOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.res.ExistOrderInfo;
import com.yxt.order.atom.sdk.offline_order.res.ExistRefundOrderInfo;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年04月01日 14:31
 * @email: <EMAIL>
 */
public interface OfflineOrderAtomApi {


  /**
   * 保存线下单
   *
   * @param saveOfflineOrderReqDto
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-order/save")
  ResponseBase<Boolean> saveOfflineOrder(
      @RequestBody @Valid SaveOfflineOrderReqDto saveOfflineOrderReqDto);

  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-order/exists")
  ResponseBase<Boolean> offlineOrderExists(
      @RequestBody @Valid OfflineOrderExistsReqDto reqDto);

  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-order/exists/info")
  ResponseBase<ExistOrderInfo> offlineOrderExistsInfo(
      @RequestBody @Valid OfflineOrderExistsReqDto reqDto);

  /**
   * 根据三方单号查订单信息
   *
   * @param reqDto
   * @return
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-order/query-by-thirdOrderNo")
  ResponseBase<ExistOfflineOrderResDto> offlineOrderQueryByThirdOrderNo(
      @RequestBody @Valid OfflineOrderExistsReqDto reqDto);

  /**
   * 保存线下单退款单
   *
   * @param saveOfflineRefundOrderReqDto
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-refund-order/save")
  ResponseBase<Boolean> saveOfflineRefundOrder(
      @RequestBody @Valid SaveOfflineRefundOrderReqDto saveOfflineRefundOrderReqDto);

  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-refund-order/exists")
  ResponseBase<Boolean> offlineRefundOrderExists(
      @RequestBody @Valid OfflineRefundOrderExistsReqDto reqDto);

  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-refund-order/exists/info")
  ResponseBase<ExistRefundOrderInfo> offlineRefundOrderExistsInfo(
      @RequestBody @Valid OfflineRefundOrderExistsReqDto reqDto);

  /**
   * 根据三方退单号查定信息
   *
   * @param reqDto
   * @return
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-refund-order/query-by-thirdRefundNo")
  ResponseBase<ExistOfflineRefundOrderResDto> offlineOrderQueryByThirdRefundNo(
      @RequestBody @Valid OfflineRefundOrderExistsReqDto reqDto);


  /**
   * 暂存
   *
   * @param dto
   * @return
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline/stageOfflineOrder")
  ResponseBase<Boolean> stageOfflineOrder(@RequestBody @Valid OfflineOrderStagingReqDto dto);

  /**
   * 保存线下单退款单
   *
   * @param compensateRefundDataReqDto
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-refund-order/compensateRefundOrderData")
  ResponseBase<Boolean> compensateRefundOrderData(
      @RequestBody @Valid CompensateRefundDataReqDto compensateRefundDataReqDto);

  /**
   * 补偿信息中心缺失的促销和券信息
   *
   * @param req
   */
  @PostMapping(OFFLINE_ORDER_ENDPOINT + "/offline-refund-order/compensateHdMissPromotionCouponData")
  ResponseBase<Boolean> compensateHdMissPromotionCouponData(@RequestBody @Valid CompensateHdMissPromotionCouponReqDto req);
}
