package com.yxt.order.atom.sdk.offline_order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
public class OfflineRefundOrderDTO {

  private String orderNo;
  private String storeCode;
  private String userId;

  private String refundNo;
  private String parentRefundNo;

  private String thirdPlatformCode;

  private String thirdRefundNo;
  private String parentThirdRefundNo;

  private String thirdOrderNo;

  private String refundType;

  private String afterSaleType;

  private String refundState;

  private String reason;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date created;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date applyTime;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date completeTime;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date billTime;

  private BigDecimal totalAmount;

  private BigDecimal shopRefund;

  private BigDecimal consumerRefund;

  private String createdBy;

  private String updatedBy;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date createdTime;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date updatedTime;

  private Long version;

  // 优惠券核销流水号
  private String serialNo;


  // 是否参加促销的标识, true,false
  private String isOnPromotion;

  private String migration;

  public void absAmount() {
    totalAmount = totalAmount.abs();
    shopRefund = shopRefund.abs();
    consumerRefund = consumerRefund.abs();
  }
}