package com.yxt.order.atom.sdk.offline_order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
public class OfflineRefundOrderDetailDTO {

  private String orderNo;

  private String refundNo;

  private String refundDetailNo;

  private String rowNo;

  private String platformSkuId;

  private String erpCode;

  private String erpName;

  private BigDecimal refundCount;

  private String refundStatus;

  private String giftType;

  private BigDecimal originalPrice;

  private BigDecimal price;

  private BigDecimal commodityCostPrice;

  private BigDecimal totalAmount;

  private BigDecimal discountShare;

  private BigDecimal discountAmount;

  private BigDecimal billPrice;

  private BigDecimal billAmount;

  private String createdBy;

  private String updatedBy;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date createdTime;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date updatedTime;

  private Long version;


  // 是否参加促销的标识, true,false
  private String isOnPromotion;

  private String detachable;


  /**
   * 商品五级分类编码
   */
  private String fiveClass;

  /**
   * 商品五级分类Name
   */
  private String fiveClassName;

  /**
   * 生产商
   */
  private String manufacture;
  /**
   * 商品规格
   */
  private String commoditySpec;

  private String mainPic;

  private String salerId;

  private String salerName;

  // 过账含税成本价
  private BigDecimal postedCostWithTaxPrice;

  // 过账成本价
  private BigDecimal postedCostPrice;

  // 过账税率
  private BigDecimal postedCostTax;

  private List<OfflineRefundOrderDetailTraceDTO> offlineRefundOrderDetailTraceDTOList;

  private List<OfflineRefundOrderDetailPickDTO> offlineRefundOrderDetailPickDTOList;

  public void absAmount() {
    refundCount = refundCount.abs();
    originalPrice = originalPrice.abs();
    price = price.abs();
    commodityCostPrice = commodityCostPrice.abs();
    totalAmount = totalAmount.abs();
    discountShare = discountShare.abs();
    discountAmount = discountAmount.abs();
    billPrice = billPrice.abs();
    billAmount = billAmount.abs();
  }
}