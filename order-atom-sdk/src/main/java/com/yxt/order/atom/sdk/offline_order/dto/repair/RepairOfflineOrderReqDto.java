package com.yxt.order.atom.sdk.offline_order.dto.repair;

import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDetailDTO;
import java.util.List;
import lombok.Data;

/**
 * 保存线下单
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月01日 14:32
 * @email: <EMAIL>
 */
@Data
public class RepairOfflineOrderReqDto {
  private OfflineOrderDTO offlineOrderDTO;
  private List<OfflineOrderDetailDTO> offlineOrderDetailDTOList;
}
