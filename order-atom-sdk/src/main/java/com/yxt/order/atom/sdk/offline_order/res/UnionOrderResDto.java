package com.yxt.order.atom.sdk.offline_order.res;

import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月05日 17:31
 * @email: <EMAIL>
 */
@Data
public class UnionOrderResDto {

  private Boolean unionOrder;

  private List<String> orderNoList;

  public static UnionOrderResDto empty() {
    UnionOrderResDto empty = new UnionOrderResDto();
    empty.setUnionOrder(Boolean.FALSE);
    return empty;
  }

  public void union(List<String> orderNoList) {
    this.unionOrder = Boolean.TRUE;
    this.orderNoList = orderNoList;
  }
}
