package com.yxt.order.atom.sdk.offline_order.res.manage;

import com.yxt.order.types.offline.enums.OfflineOrderStateEnum;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2025/3/27 14:22
 */
@Data
public class OfflineOrderRes {

  @ApiModelProperty("系统单号")
  private String orderNo;

  /**
   * @see ThirdPlatformCodeEnum
   */
  @ApiModelProperty("POS")
  private String thirdPlatformCode;

  @ApiModelProperty("平台单号")
  private String thirdOrderNo;

  /**
   * @see OfflineOrderStateEnum
   */
  @ApiModelProperty("订单状态")
  private String offlineOrderState;

  @ApiModelProperty("下单时间")
  private Date created;

  @ApiModelProperty("实付金额")
  private BigDecimal actualPayAmount;

  @ApiModelProperty("实收金额")
  private BigDecimal actualCollectAmount;

  @ApiModelProperty("门店编码")
  private String storeCode;

  @ApiModelProperty("门店名称")
  private String storeName;


}
