package com.yxt.order.atom.sdk.offline_order.res.manage;

import com.yxt.order.types.offline.enums.AfterSaleTypeEnum;
import com.yxt.order.types.offline.enums.RefundStateEnum;
import com.yxt.order.types.offline.enums.RefundTypeEnum;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2025/3/27 14:22
 */
@Data
public class OfflineRefundOrderRes {

  @ApiModelProperty("系统退款单号")
  private String refundNo;

  @ApiModelProperty("系统订单号")
  private String orderNo;

  /**
   * @see ThirdPlatformCodeEnum
   */
  @ApiModelProperty("POS")
  private String thirdPlatformCode;

  @ApiModelProperty("平台退款单号")
  private String thirdRefundNo;

  @ApiModelProperty("平台订单号")
  private String thirdOrderNo;

  /**
   * @see RefundTypeEnum
   */
  @ApiModelProperty("退款类型")
  private String refundType;

  /**
   * @see AfterSaleTypeEnum
   */
  @ApiModelProperty("售后类型")
  private String afterSaleType;

  /**
   * @see RefundStateEnum
   */
  @ApiModelProperty("退款单状态")
  private String refundState;

  @ApiModelProperty("退款申请时间")
  private Date created;

  @ApiModelProperty("退款金额")
  private BigDecimal consumerRefund;

  @ApiModelProperty("门店编码")
  private String storeCode;

  @ApiModelProperty("门店名称")
  private String storeName;

}
