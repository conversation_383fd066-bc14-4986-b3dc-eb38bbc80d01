package com.yxt.order.atom.sdk.online_order.account_check;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.common.data.AccountCheckPullJobDTO;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckCleanReqDto;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckPullJobPageQueryReqDto;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckPullJobSaveReqDto;
import com.yxt.order.atom.sdk.online_order.account_check.req.AccountCheckSaveReqDto;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface AccountCheckAtomCmdApi {

  @PostMapping(ORDER_ENDPOINT + "/account-check/save")
  ResponseBase<Void> saveAccountCheck(@RequestBody AccountCheckSaveReqDto req);

  @PostMapping(ORDER_ENDPOINT + "/account-check/pull-job/save")
  ResponseBase<Void> saveAccountCheckPullJob(@RequestBody AccountCheckPullJobSaveReqDto req);

  @PostMapping(ORDER_ENDPOINT + "/account-check/pull-job/query/page")
  ResponseBase<PageDTO<AccountCheckPullJobDTO>> queryAccountCheckPullJobPage(@RequestBody @Valid AccountCheckPullJobPageQueryReqDto req);

  @PostMapping(ORDER_ENDPOINT + "/account-check/clean")
  ResponseBase<Void> cleanAccountCheck(@RequestBody @Valid AccountCheckCleanReqDto req);
}
