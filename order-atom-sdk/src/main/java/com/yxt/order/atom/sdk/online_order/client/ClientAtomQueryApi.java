package com.yxt.order.atom.sdk.online_order.client;


import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.common.data.StoreBillConfigDTO;
import com.yxt.order.atom.sdk.online_order.client.req.OnlineClientQueryReqDto;
import com.yxt.order.atom.sdk.online_order.store.req.GetOnlineStoreByPlatformShopIdReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryBillConfigByIdReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryBillConfigReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryClientReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryDsOnlineStoreReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryStoreAccessReq;
import com.yxt.order.atom.sdk.online_order.store.req.QuerySysStoreInfoReq;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineClientResDto;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineStoreResDto;
import com.yxt.order.atom.sdk.online_order.store.res.OnlineStoreInfoResDto;
import com.yxt.order.atom.sdk.online_order.store.res.The3DsOnlineClientResDto;
import com.yxt.order.atom.sdk.online_order.store.res.The3DsStoreResDto;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


public interface ClientAtomQueryApi {

  @PostMapping(ORDER_ENDPOINT + "/client/queryClient")
  ResponseBase<List<DsOnlineClientResDto>> queryClient(@RequestBody OnlineClientQueryReqDto req);

}
