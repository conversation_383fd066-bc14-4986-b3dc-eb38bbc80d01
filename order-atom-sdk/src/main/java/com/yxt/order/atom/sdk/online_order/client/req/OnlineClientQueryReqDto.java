package com.yxt.order.atom.sdk.online_order.client.req;

import com.yxt.order.types.order.MerCode;
import com.yxt.order.types.order.ThirdPlatformCode;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class OnlineClientQueryReqDto {

  @ApiModelProperty(value = "三方平台编码")
  private ThirdPlatformCode platformCode;

  @ApiModelProperty(value = "商户编码")
  private MerCode merCode;

  @ApiModelProperty(value = "网店编码")
  private List<String> onlineClientCodeList;

  @ApiModelProperty(value = "服务模式O2O B2C")
  private String serviceMode;
}
