package com.yxt.order.atom.sdk.online_order.order_info;


import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.common.data.OrderBusinessConsumerMessageDTO;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.OrderInfoQryByScaleBatchReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.OrderInfoQryByScaleReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.OrderInfoQryReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QueryOrderBusinessConsumerMessageReq;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QueryOrderDetailByThirdReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QueryOrderDetailReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QuerySimpleOrderReqByThirdDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QuerySimpleOrderReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.res.FullOrderDtoResDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.res.SimpleOrderInfoResDto;
import java.util.List;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年02月23日 14:11
 * @email: <EMAIL>
 */
public interface OrderAtomQryApi {

  // 查询订单的所有信息
  @PostMapping(ORDER_ENDPOINT + "/order/full")
  ResponseBase<FullOrderDtoResDto> queryFullOrder(
      @RequestBody QueryOrderDetailReqDto dto);

  // 查询订单的所有信息
  @PostMapping(ORDER_ENDPOINT + "/order/full-by-third-req")
  ResponseBase<FullOrderDtoResDto> queryFullOrderByThirdReq(
      @RequestBody QueryOrderDetailByThirdReqDto dto);

  @PostMapping(ORDER_ENDPOINT + "/order/getOrderInfo")
  ResponseBase<OrderInfoResDto> getOrderInfo(@RequestBody OrderInfoQryReqDto orderNoDto);

  //  notey 对象先不用DP对象,后面统一调整(根据编译结果来)
  @PostMapping(ORDER_ENDPOINT + "/order/query/simple")
  ResponseBase<SimpleOrderInfoResDto> queryOrderInfo(
      @RequestBody QuerySimpleOrderReqDto dto);

  @PostMapping(ORDER_ENDPOINT + "/order/query/simple/third")
  ResponseBase<SimpleOrderInfoResDto> queryOrderInfo(
      @RequestBody QuerySimpleOrderReqByThirdDto dto);

  @PostMapping(ORDER_ENDPOINT + "/order/query/OrderBusinessConsumerMessage")
  ResponseBase<List<OrderBusinessConsumerMessageDTO>> queryOrderBusinessConsumerMessageList(
      @RequestBody QueryOrderBusinessConsumerMessageReq dto);

  @PostMapping(ORDER_ENDPOINT + "/order/query/by-scale")
  ResponseBase<FullOrderDtoResDto> getOrderInfoByScale(@RequestBody @Valid OrderInfoQryByScaleReqDto request);

  @PostMapping(ORDER_ENDPOINT + "/order/query/by-scale/batch")
  ResponseBase<List<FullOrderDtoResDto>> getOrderInfoBatchByScale(@RequestBody @Valid OrderInfoQryByScaleBatchReqDto request);
}
