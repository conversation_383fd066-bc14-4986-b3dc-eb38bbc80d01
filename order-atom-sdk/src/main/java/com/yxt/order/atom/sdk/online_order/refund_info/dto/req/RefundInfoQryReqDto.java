package com.yxt.order.atom.sdk.online_order.refund_info.dto.req;

import com.yxt.order.types.order.DataVersion;
import com.yxt.order.types.order.RefundOrderNo;
import com.yxt.order.types.order.enums.RefundErpStatusEnum;
import com.yxt.order.types.order.enums.RefundQryScaleEnum;
import com.yxt.order.types.order.enums.RefundStateEnum;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefundInfoQryReqDto {

  @ApiModelProperty(value = "查询规模")
  private List<RefundQryScaleEnum> qryScaleList;

  @ApiModelProperty(value = "内部退款单号")
  private RefundOrderNo refundOrderNo;

  @ApiModelProperty(value = "退款状态")
  private RefundStateEnum refundState;

  @ApiModelProperty(value = "下账状态")
  private RefundErpStatusEnum refundErpStatus;

  @ApiModelProperty(value = "退款单版本号")
  private DataVersion dataVersion;

  public RefundInfoQryReqDto(List<RefundQryScaleEnum> qryScaleList, RefundOrderNo refundOrderNo) {
    this.qryScaleList = qryScaleList;
    this.refundOrderNo = refundOrderNo;
  }
}
