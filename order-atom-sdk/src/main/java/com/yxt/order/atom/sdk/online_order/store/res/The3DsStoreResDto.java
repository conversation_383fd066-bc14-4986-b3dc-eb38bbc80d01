package com.yxt.order.atom.sdk.online_order.store.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class The3DsStoreResDto {

  @ApiModelProperty(value = "商户编码")
  private String merCode;

  @ApiModelProperty(value = "appid")
  private String appid;

  @ApiModelProperty(value = "app_secret")
  private String appSecret;

  @ApiModelProperty(value = "网店编码")
  private String onlineClientCode;

  @ApiModelProperty(value = "外部网店编码")
  private String onlineClientOutCode;

  @ApiModelProperty(value = "token")
  private String accessToken;

  @ApiModelProperty(value = "平台编码")
  private String platformCode;

  @ApiModelProperty(value = "平台侧店铺编码")
  private String platformShopId;

  @ApiModelProperty("address_id")
  private Long addressId;

  @ApiModelProperty("门店编码")
  private String onlineStoreCode;
}
