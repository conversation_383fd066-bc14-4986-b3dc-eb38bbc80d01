package com.yxt.order.atom.sdk.order_info.req;

import com.yxt.lang.dto.PageBase;
import java.util.Date;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2024/12/10 16:34
 */
@Data
public class MemberRefundOrderSimpleReqDto extends PageBase {

  /**
   * 会员ID
   */
  @NotEmpty(message = "userId can not null")
  private String userId;

  /**
   * 下单开始时间
   */
  @NotNull(message = "createdStart can not null")
  private Date createdStart;

  /**
   * 下单结束时间
   */
  @NotNull(message = "createdEnd can not null")
  private Date createdEnd;

  /**
   * （下单）门店编码
   */
  private String storeCode;

  /**
   * 会员号
   */
  private String userCardNo;

  /**
   * 订单来源 ONLINE-线上订单 POS-线下订单
   */
  private String orderSource;

  /**
   * 退单状态 退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消
   */
  private Integer refundStatus;

  /**
   * 三方订单号
   */
  private String thirdOrderNo;

  /**
   * 系统单号
   */
  private String orderNo;

  /**
   * 系统退单号
   */
  private String refundNo;

  /**
   * 退单三方单号
   */
  private String thirdRefundNo;


  private String erpName;

  private String erpCode;
}
