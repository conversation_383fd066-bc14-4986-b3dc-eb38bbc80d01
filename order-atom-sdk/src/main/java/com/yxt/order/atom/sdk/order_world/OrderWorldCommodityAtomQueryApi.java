package com.yxt.order.atom.sdk.order_world;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.order_world.req.CommodityStockChangeRecordSearchReq;
import com.yxt.order.atom.sdk.order_world.res.CommodityStockChangeRecordRes;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface OrderWorldCommodityAtomQueryApi {

  @PostMapping(ORDER_ENDPOINT + "/commodity/stock-change-record/query")
  ResponseBase<List<CommodityStockChangeRecordRes>> queryCommodityStockStockChangeRecord(@RequestBody CommodityStockChangeRecordSearchReq req);

}
