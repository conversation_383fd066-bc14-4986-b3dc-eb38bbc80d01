package com.yxt.order.atom.sdk.order_world.req;

import com.yxt.order.types.order_world.OrderCountStatisticType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;

@ApiModel("订单数量聚合统计")
@Data
public class EsOrderWorldOrderCountStatisticReq {

  @ApiModelProperty(value = "筛选条件")
  private List<EsOrderWorldOrderSearchCondition> searchConditionList;

  @ApiModelProperty(value = "聚合条件")
  @NotNull(message = "聚合条件不能为空")
  private OrderCountStatisticType statisticType;
}
