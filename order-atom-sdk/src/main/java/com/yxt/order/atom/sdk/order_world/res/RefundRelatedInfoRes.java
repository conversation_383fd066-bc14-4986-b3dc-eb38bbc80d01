package com.yxt.order.atom.sdk.order_world.res;

import com.yxt.order.atom.sdk.common.order_world.RefundOrderAmountDTO;
import com.yxt.order.atom.sdk.common.order_world.RefundOrderDTO;
import com.yxt.order.atom.sdk.common.order_world.RefundOrderDetailDTO;
import com.yxt.order.atom.sdk.common.order_world.RefundOrderPayDTO;
import com.yxt.order.atom.sdk.common.order_world.RefundOrderUserDTO;
import java.util.List;
import lombok.Data;

@Data
public class RefundRelatedInfoRes {

  private RefundOrderDTO refundOrder;

  private List<RefundOrderDetailDTO> refundOrderDetailList;

  private RefundOrderAmountDTO refundOrderAmount;

  private List<RefundOrderPayDTO> refundOrderPayList;

  private RefundOrderUserDTO refundOrderUser;
}
