package com.yxt.order.atom.sdk.org_order.req;

import com.yxt.order.common.es.EsPageRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("门店退单分页查询")
public class EsOrgRefundPageQueryReqDTO extends EsPageRequestDTO {

  @ApiModelProperty(value = "查询条件列表")
  @NotEmpty(message = "筛选条件不能为空")
  private List<EsOrgRefundSearchConditionDTO> searchConditionList;

}
