package com.yxt.order.atom.sdk.org_order.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@ApiModel("正单金额统计")
@AllArgsConstructor
@NoArgsConstructor
public class EsOrgOrderAmountStaticResDTO {

  @ApiModelProperty(value = "销售金额")
  private BigDecimal orderAmount;

}
