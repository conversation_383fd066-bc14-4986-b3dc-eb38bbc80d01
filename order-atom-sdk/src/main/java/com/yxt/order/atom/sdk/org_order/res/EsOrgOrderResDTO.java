package com.yxt.order.atom.sdk.org_order.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;


@Data
@ApiModel(value = "订单分页列表")
public class EsOrgOrderResDTO {

  private String id;

  @ApiModelProperty("系统单号")
  private String orderNo;

  @ApiModelProperty("三方订单号")
  private String thirdOrderNo;

  @ApiModelProperty("下单时间,秒级时间戳")
  private Date created;

  @ApiModelProperty("创建时间,秒级时间戳")
  private Date createTime;

  @ApiModelProperty("支付时间,秒级时间戳")
  private Date payTime;

  @ApiModelProperty("支付日期（冗余）")
  private String payDate;

  @ApiModelProperty("线上门店编码")
  private String storeCode;

  @ApiModelProperty("机构编码（线下实际发货门店）")
  private String orgCode;

  @ApiModelProperty("下单线上门店编码")
  private String sourceStoreCode;

  @ApiModelProperty("下单线下机构编码")
  private String sourceOrgCode;

  @ApiModelProperty("订单状态 5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭")
  private Integer orderStatus;

  @ApiModelProperty("下账状态 20, 待锁定 30, 待下账 99, 下账失败 100, 已下账 110, 已取消 120,已退款")
  private Integer erpStatus;

  @ApiModelProperty("下账时间")
  private Date erpTime;

  @ApiModelProperty("零售流水")
  private String erpSaleNo;

  @ApiModelProperty("配送方式")
  private String deliveryType;

  @ApiModelProperty("订单来源 ONLINE-线上订单 OFFLINE-线下订单")
  private String orderSource;

  @ApiModelProperty("平台编码")
  private String platformCode;

  @ApiModelProperty("订单标记")
  private List<String> orderFlags;

  @ApiModelProperty("异常标记")
  private String lockFlag;

  @ApiModelProperty("下账金额")
  private BigDecimal billAmount;

  @ApiModelProperty("订单明细")
  private List<EsOrgOrderDetailDTO> detailList;

  @ApiModelProperty("会员编码(唯一值)")
  private String userCardNo;

  @ApiModelProperty("会员ID (心云)")
  private String userId;

  @ApiModelProperty("门店类型 DIRECT_SALES-直营 JOIN-加盟")
  private String storeType;

  // 模型自由字段,其他表适配该字段
  // 默认0-未删除
  private Long deleted;

}
