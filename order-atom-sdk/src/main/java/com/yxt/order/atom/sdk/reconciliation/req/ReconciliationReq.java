package com.yxt.order.atom.sdk.reconciliation.req;

import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2025/4/23 16:17
 */
@Data
public class ReconciliationReq {

  private String companyCode;

  private String storeCode;

  @NotEmpty
  private String createdStart;

  @NotEmpty
  private String createdEnd;

  @NotEmpty
  private String thirdPlatformCode;

  // 使用统一接口时必传
  private ReconciliationType reconciliationType;


}
