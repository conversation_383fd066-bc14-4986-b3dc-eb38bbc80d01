package com.yxt.order.atom.common.sharding;

import static com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm.commonTableRouteFunc;
import static com.yxt.order.common.constants.Constant.OFFLINE_SHARDING_NUM;
import static com.yxt.order.types.utils.ShardingHelper.MIGRATE_NO_SET;

import cn.hutool.extra.spring.SpringUtil;
import com.yxt.order.atom.common.sharding.OfflineOrderHit.QueryHit.OfflineDataBaseEnum;
import com.yxt.order.types.utils.ShardingHelper;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.sharding.hint.HintShardingAlgorithm;
import org.apache.shardingsphere.api.sharding.hint.HintShardingValue;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 数据库
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月09日 18:46
 * @email: <EMAIL>
 */
@Slf4j
@Component
public class OfflineOrderDatabaseShardingHintAlgorithm implements
    HintShardingAlgorithm<OfflineOrderHit> {

  private final List<String> DEFAULT_DB = OfflineDataBaseEnum.OFFLINE_ORDER.getActualDataSourceList();
  private final List<String> OFFLINE_ORDER_ARCHIVE_DB = OfflineDataBaseEnum.OFFLINE_ORDER_ARCHIVE.getActualDataSourceList();

  public final static Integer OFFLINE_DB_START_YY_MM = 2406; // 线下单库初始的年月归档表



  /**
   * @param databaseCollection available data sources
   * @param hintShardingValue  sharding value
   * @return
   */
  @Override
  public Collection<String> doSharding(Collection<String> databaseCollection,
      HintShardingValue<OfflineOrderHit> hintShardingValue) {

    int yyMMDatabaseStartValue = yyMMDatabaseStartValue();

    Collection<OfflineOrderHit> values = hintShardingValue.getValues();

    Optional<OfflineOrderHit> first = values.stream().findFirst();
    if (!first.isPresent()) {
      throw new RuntimeException("[database]强制分片无数据,请检查使用方式!!!");
    }
    OfflineOrderHit hit = first.get();

    // 1.手动指定数据库优先级最高
    if (Objects.nonNull(hit.getQueryHit()) && Objects.nonNull(
        hit.getQueryHit().getDataBaseEnum())) {
      return hit.getQueryHit().getDataBaseEnum().getActualDataSourceList();
    }

    if(!StringUtils.isEmpty(hit.getDefineNo())){ // 需要判断空,有地方是直接设置seq的,没有任何单号
      String vipTag = ShardingHelper.getVipTag(hit.getDefineNo());
      if (MIGRATE_NO_SET.contains(vipTag)) {
        return OFFLINE_ORDER_ARCHIVE_DB;
      }
    }

    // 2. 获取分表值,就知道是会员还是非会员了,
    //    2.1如果是会员,只路由到线上库;
    //    2.2如果是非会员,2406之前的,路由到归档库年月表,之后的路由到线上的归档库年月表
    //       所以,在每一次迁移归档数据时,需要考虑yyMMDatabaseStartValue配置调整
    String tableIndexStr = commonTableRouteFunc(hit);
    int tableIndex = Integer.parseInt(tableIndexStr);
    if (tableIndex <= OFFLINE_SHARDING_NUM) {
      return DEFAULT_DB; // 会员订单统一路由到线下单库
    }

    // 3. 根据归档库年月值判断走哪个库
    if (tableIndex >= yyMMDatabaseStartValue) {
      return DEFAULT_DB;// 路由值在指定的年月后则走线下单库的归档表
    } else {
      return OFFLINE_ORDER_ARCHIVE_DB; // 否则,统一走线下单归档库。即线下单库中的年月归档数据在yyMMDatabaseStartValue之前的都应该迁移到线下单归档库
    }
  }

  /**
   * 指定年月起始值,为数据迁移做准备
   * @return
   */
  public static int yyMMDatabaseStartValue() {
    String yyMMDatabaseStartValueStr = SpringUtil.getProperty("yyMMDatabaseStartValue");
    int yyMMDatabaseStartValue;
    try {
      yyMMDatabaseStartValue =
          StringUtils.isEmpty(yyMMDatabaseStartValueStr) ? OFFLINE_DB_START_YY_MM
              : Integer.parseInt(yyMMDatabaseStartValueStr);
    } catch (Exception e) {
      log.warn("格式化:{} 异常,采用默认值:{}",yyMMDatabaseStartValueStr, OFFLINE_DB_START_YY_MM);
      yyMMDatabaseStartValue = OFFLINE_DB_START_YY_MM;
    }
    return yyMMDatabaseStartValue;
  }

  public static void main(String[] args) {
    System.out.println(Integer.parseInt("01"));
    System.out.println(Integer.parseInt("000132"));
    System.out.println(Integer.parseInt("12"));
    System.out.println(Integer.parseInt("120"));
    System.out.println(255 <= OFFLINE_SHARDING_NUM);
    System.out.println(0 <= OFFLINE_SHARDING_NUM);
    System.out.println(2406 >= 2406);
  }
}
