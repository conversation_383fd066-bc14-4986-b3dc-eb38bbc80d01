package com.yxt.order.atom.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * BigDecimal工具类，用于处理浮点数精度问题
 */
public class BigDecimalUtil {

    /**
     * 将double值转换为BigDecimal，保留指定小数位数
     * 
     * @param value 要转换的double值
     * @param scale 小数位数
     * @param roundingMode 舍入模式
     * @return BigDecimal
     */
    public static BigDecimal toBigDecimal(double value, int scale, RoundingMode roundingMode) {
        // 使用String构造函数避免浮点数精度问题
        return new BigDecimal(String.valueOf(value)).setScale(scale, roundingMode);
    }

    /**
     * 将double值转换为BigDecimal，保留2位小数，四舍五入
     * 
     * @param value 要转换的double值
     * @return BigDecimal
     */
    public static BigDecimal toBigDecimal(double value) {
        return toBigDecimal(value, 2, RoundingMode.HALF_UP);
    }
    
    /**
     * 将double值转换为BigDecimal，保留指定小数位数，四舍五入
     * 
     * @param value 要转换的double值
     * @param scale 小数位数
     * @return BigDecimal
     */
    public static BigDecimal toBigDecimal(double value, int scale) {
        return toBigDecimal(value, scale, RoundingMode.HALF_UP);
    }
}
