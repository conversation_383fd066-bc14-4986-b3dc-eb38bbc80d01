package com.yxt.order.atom.common.utils;

import com.yxt.order.common.CommonDateUtils;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月23日 11:15
 * @email: <EMAIL>
 */
@Slf4j
public class OrderDateUtils {

  public static Boolean theSameDate(Date startDate, Date endDate) {
    return yyyyMMdd(startDate).equals(yyyyMMdd(endDate));
  }


  public static String yyyyMMdd(Date date) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);

    int year = calendar.get(Calendar.YEAR);
    int month = calendar.get(Calendar.MONTH) + 1;
    int day = calendar.get(Calendar.DAY_OF_MONTH);
    return String.format("%04d%02d%02d", year, month, day);
  }


  public static Date convertToBeijingTime(Date date) {
    // 创建一个Calendar对象，并设置为当前日期时间
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);

    // 获取当前时区的偏移量（以毫秒为单位）
    int currentOffset = calendar.getTimeZone().getOffset(calendar.getTimeInMillis());

    // 设置为北京时间时区（Asia/Shanghai）
    TimeZone beijingTimeZone = TimeZone.getTimeZone("Asia/Shanghai");
    calendar.setTimeZone(beijingTimeZone);

    // 获取北京时间时区的偏移量（以毫秒为单位）
    int beijingOffset = beijingTimeZone.getOffset(calendar.getTimeInMillis());

    // 计算时区差异，并调整时间
    long adjustedTime = calendar.getTimeInMillis() + (beijingOffset - currentOffset);

    // 创建一个新的Date对象，表示北京时间
    return new Date(adjustedTime);
  }

  /**
   * 是否超过指定天数
   *
   * @param startDateStr
   * @param endDateStr
   * @param days
   * @return
   */
  public static boolean isExceedingDays(String startDateStr, String endDateStr, Integer days) {
    try {
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      Date startDate = sdf.parse(startDateStr);
      Date endDate = sdf.parse(endDateStr);

      long diffInMillis = endDate.getTime() - startDate.getTime();
      long diffInDays = TimeUnit.DAYS.convert(diffInMillis, TimeUnit.MILLISECONDS);
      boolean result = diffInDays > days;
      if (result) {
        log.info("当前时间{},{}间隔天数:{},不能超过:{},是否超过:{}", startDateStr, endDateStr,
            diffInDays, days, result);
      }
      return result;
    } catch (Exception e) {
      log.error("isExceedingDays,{},{}", startDateStr, endDateStr, e);
      // 默认返回为未超过
      return false;
    }
  }



  public static String formatYYMMDD(Date date) {
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    return sdf.format(date);
  }
  public static Date previousDate(Date currentDate, int daysBefore) {
    // 创建日历对象
    Calendar calendar = Calendar.getInstance();
    // 设置日历时间
    calendar.setTime(currentDate);
    // 往前推算指定天数
    calendar.add(Calendar.DAY_OF_MONTH, -daysBefore);
    // 返回计算后的日期,并格式化
    return calendar.getTime();
  }

  public static Date previousDateForMinute(Date currentDate, int minuteBefore) {
    // 创建日历对象
    Calendar calendar = Calendar.getInstance();
    // 设置日历时间
    calendar.setTime(currentDate);
    // 往前推算指定分钟数
    calendar.add(Calendar.MINUTE, -minuteBefore);
    // 返回计算后的日期,并格式化
    return calendar.getTime();
  }

  /**
   * 从给定时间减去指定分钟数并返回新的 Date 类型时间。
   *
   * @param date 初始时间 (Date 类型)
   * @return 减去指定分钟数后的时间 (Date 类型)
   */
  public static Date subtractMinutes(Date date,Integer minusMinutes) {
    // 将 Date 转换为 LocalDateTime
    LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.of("Asia/Shanghai")).toLocalDateTime();
    // 减去10秒
    localDateTime = localDateTime.minusMinutes(minusMinutes);
    // 将 LocalDateTime 转换回 Date
    return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
  }

  public static void main(String[] args) throws ParseException {
//    ZoneId zoneId = ZoneId.of("Asia/Shanghai");
//    System.out.println(zoneId);
//    Date date = new Date();
//    System.out.println(date);
//    System.out.println(subtractMinutes(date,4));
//    System.out.println(previousDateForMinute(date,6));
//    Date date = CommonDateUtils.convert2Date("2025-01-01 00:00:00");
//
//    System.out.println(isExpired(date,8));

//    System.out.println(timestamp());
    Date date = new Date();
    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    System.out.println("左闭测试：" + isEfficientDate(sdf.format(date), "2025-04-02 00:00:00", date));
    System.out.println("右闭测试：" + isEfficientDate("2024-04-02 00:00:00", sdf.format(date), date));
  }

  public  static Boolean isExpired(Date date,Integer expireDays){
    long abs = Math.abs(CommonDateUtils.calculateDaysBetween(date, new Date()));
    return abs >expireDays;
  }


  public static List<String> yyMMList(Integer startDateValue) {
    String startDate = String.valueOf(startDateValue);
    List<String> result = new ArrayList<>();

    // Parse the input (e.g., "2404")
    int year = 2000 + Integer.parseInt(startDate.substring(0, 2)); // 2000 ,system started
    int month = Integer.parseInt(startDate.substring(2));

    // Get current date
    LocalDate currentDate = LocalDate.now();

    // Create start date
    LocalDate date = LocalDate.of(year, month, 1);

    // Generate all dates until current date
    while (!date.isAfter(currentDate)) {
      // Format as YYMM
      String formattedDate = String.format("%02d%02d",
          date.getYear() % 100,
          date.getMonthValue()
      );
      result.add(formattedDate);

      // Move to next month
      date = date.plusMonths(1);
    }

    return result;
  }

  public static Long timestamp(){
    return Instant.now().getEpochSecond();
  }


  /**
   * 判断createTime是否在时间范围内
   *
   * @param startDateStr 开始时间
   * @param endDateStr 结束时间
   * @param createTime 判断时间
   * @return Boolean
   */
  public static Boolean isEfficientDate(String startDateStr, String endDateStr, Date createTime) {
    try {
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      Date startDate = sdf.parse(startDateStr);
      Date endDate = sdf.parse(endDateStr);
      Date parse = sdf.parse(sdf.format(createTime));
      return parse.compareTo(startDate) >= 0 && parse.compareTo(endDate) <= 0;
    }catch (Exception e){
      log.error("isEfficientDate,{},{}", startDateStr, endDateStr, e);
      return false;
    }
  }
}
