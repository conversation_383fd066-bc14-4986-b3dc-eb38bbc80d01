package com.yxt.order.atom.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yxt.order.atom.order.entity.OfflineOrderCouponDO;
import com.yxt.order.atom.order.entity.OfflineOrderPromotionDO;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 大数据需要的退款信息
 * @author: moatkon
 * @time: 2024/11/29 11:44
 */
@Data
public class BigDataRefundInfoDto {

  /**
   * 退单号
   */
  private String refundNo;

  /**
   * 正单号
   */
  private String orderNo;

  /**
   * 退单创建时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date refundCreated;

  /**
   * 正单创建时间
   */
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
  private Date orderCreated;

  /**
   * 促销信息
   */
  private List<OfflineOrderPromotionDO> offlineOrderPromotionList;

  /**
   * 券信息
   */
  private List<OfflineOrderCouponDO> offlineOrderCouponList;

}
