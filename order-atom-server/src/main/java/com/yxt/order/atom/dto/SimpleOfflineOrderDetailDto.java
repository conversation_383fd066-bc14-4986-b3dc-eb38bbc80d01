package com.yxt.order.atom.dto;

import com.yxt.order.atom.order.entity.OfflineOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailPickDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailTraceDO;
import com.yxt.order.atom.order.entity.OfflineOrderOrganizationDO;
import java.util.List;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2024/12/11 15:23
 */
@Data
public class SimpleOfflineOrderDetailDto {
  private OfflineOrderDO offlineOrder;
  private List<OfflineOrderDetailDO> offlineOrderDetailList;
  private List<OfflineOrderDetailPickDO> offlineOrderDetailPickList;
  private List<OfflineOrderDetailTraceDO> offlineOrderDetailTraceList;

  private OfflineOrderOrganizationDO offlineOrderOrganizationDO;
  private OfflineOrderCashierDeskDO offlineOrderCashierDeskDO;
  private Boolean hasRefundOrder;
}
