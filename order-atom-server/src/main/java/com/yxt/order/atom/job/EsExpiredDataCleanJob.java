package com.yxt.order.atom.job;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.order.atom.order.es.sync.AbstractClean;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2024/12/13 16:53
 */
@Component
@Slf4j
public class EsExpiredDataCleanJob {

  @Resource
  private List<AbstractClean> abstractCleanList;

  @XxlJob("esExpiredDataCleanJob")
  public void execute() {
    if (CollectionUtils.isEmpty(abstractCleanList)) {
      XxlJobHelper.log("无具体清理ES过期数据的实现");
      XxlJobHelper.handleSuccess();
      return;
    }

    // note: 不考虑时效性,慢慢跑,不使用线程池
    for (AbstractClean abstractClean : abstractCleanList) {
      abstractClean.execClean();
    }
    XxlJobHelper.handleSuccess();
  }

}
