package com.yxt.order.atom.job.compensate;

import com.google.common.collect.Lists;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.order.atom.common.utils.RedisStringUtil;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 补充退单数据
 *
 * @author: moatkon
 * @time: 2024/11/28 14:07
 */
@Component
@Slf4j
public class CompensateRefundDataHandler {

//  private final String Collection_HD = "ORDER_SYNC_TP_ORDER_OFFLINE_SYNC-HD";
//  private final String Collection_KC = "ORDER_SYNC_TP_ORDER_OFFLINE_SYNC-KC";
//
//  @Resource
//  private MongoTemplate mongoTemplate;

  @Resource
  private RocketMQTemplate template;

  // 设置每批次处理的数量

//  @Value("${completeRefundDataBatchSize:500}")
//  private Integer completeRefundDataBatchSize;
//
//
//  @Value("${mongoOfflineOrderMessageExpiredDays:15}") // mongo里面消息保留天数
//  private Integer expiredDays;
//
//
//  @Value("${mq.topic.producer.offlineOrderSyncKc}")
//  private String offlineOrderSyncKc;

  @Value("${mq.topic.producer.hdOfflineOrder}")
  private String hdOfflineOrder;


  @XxlJob("completeRefundDataHandler")
  public void execute() {
    String key = "completeRefundDataHandlerHitV2";
    String value = RedisStringUtil.getValue(key);
    XxlJobHelper.log("key:{}, value:{}",key, value);
    if (!StringUtils.isEmpty(value)) {
      XxlJobHelper.log("补偿任务v2已经触发," + value); // 只允许出发一次
      XxlJobHelper.handleSuccess();
      return;
    }
    RedisStringUtil.setValue(key, "hited," + new Date());

    String tag = "TAG_COMPENSATE_REFUND_DATA";
    Lists.newArrayList(
        "{\"retsaleno\":\"1024111900066355\",\"shiftid\":1,\"reason\":\"下错账\",\"orderType\":\"退货单\",\"codes\":[],\"member_tel\":\"\",\"membercardno\":\"\",\"posno\":\"101\",\"member_name\":\"\",\"saleDetails\":[{\"commonName\":\"纸板费\",\"rowno\":1,\"mdmBusno\":\"HA37\",\"wareid\":777747,\"netprice\":0,\"makeno\":\"None\",\"detachable\":\"否\",\"discount\":0,\"distype\":0,\"billAmount\":0,\"orgname\":\"[HA37]一心堂什邡蓥华山路北段店\",\"compid\":1006,\"ware_qty\":-3,\"stdprice\":0,\"discountShare\":0,\"promoplanno\":\"\",\"compname\":\"四川一心堂医药连锁有限公司\"}],\"saleno\":\"1024111900063191\",\"payee\":\"1123186\",\"netsum\":0,\"salePayDetails\":[{\"netsum\":0,\"paytypen\":\"现金\",\"paytype\":\"1\",\"cardno\":\"\",\"saleno\":\"1024111900063191\"}],\"shiftdate\":\"2024-11-19 17:09:17.721\",\"finaltime\":\"2024-11-19 17:09:17.721\",\"srcsaleno\":\"\"}",
        "{\"retsaleno\":\"1024111900070603\",\"shiftid\":1,\"reason\":\"123\",\"orderType\":\"退货单\",\"codes\":[],\"member_tel\":\"\",\"membercardno\":\"\",\"posno\":\"101\",\"member_name\":\"\",\"saleDetails\":[{\"commonName\":\"云南白药创可贴(轻巧护翼型)_1.5CM*2.3CM*20片_云南白药无锡\",\"rowno\":1,\"mdmBusno\":\"H075\",\"wareid\":131711,\"netprice\":8,\"makeno\":\"PFB2413\",\"detachable\":\"否\",\"discount\":0,\"distype\":0,\"billAmount\":-8,\"orgname\":\"[H075]一心堂自贡豪斯登堡小区店\",\"compid\":1006,\"ware_qty\":-1,\"stdprice\":8,\"discountShare\":0,\"promoplanno\":\"\",\"compname\":\"四川一心堂医药连锁有限公司\"}],\"saleno\":\"1024111900069426\",\"payee\":\"1048699\",\"netsum\":-8,\"salePayDetails\":[{\"netsum\":-8,\"paytypen\":\"收钱吧补充收款\",\"paytype\":\"103\",\"saleno\":\"1024111900069426\"}],\"shiftdate\":\"2024-11-19 17:13:38.822\",\"finaltime\":\"2024-11-19 17:13:38.822\",\"srcsaleno\":\"\"}",
        "{\"retsaleno\":\"1024111900064359\",\"shiftid\":1,\"reason\":\"卖错了\",\"orderType\":\"退货单\",\"codes\":[],\"member_tel\":\"\",\"membercardno\":\"\",\"posno\":\"101\",\"member_name\":\"\",\"saleDetails\":[{\"commonName\":\"红云锦华医用外科口罩(无菌型耳挂式)_175MM*95MM*10片\",\"rowno\":1,\"mdmBusno\":\"HI75\",\"wareid\":100914,\"netprice\":7,\"makeno\":\"20240903\",\"detachable\":\"否\",\"discount\":0,\"distype\":0,\"billAmount\":-7,\"orgname\":\"[HI75]一心堂会理顺城东路二店\",\"compid\":1006,\"ware_qty\":-1,\"stdprice\":7,\"discountShare\":0,\"promoplanno\":\"\",\"compname\":\"四川一心堂医药连锁有限公司\"}],\"saleno\":\"1024111900062596\",\"payee\":\"1161548\",\"netsum\":-7,\"salePayDetails\":[{\"netsum\":-7,\"paytypen\":\"收钱吧补充收款\",\"paytype\":\"103\",\"saleno\":\"1024111900062596\"}],\"shiftdate\":\"2024-11-19 17:09:10.490\",\"finaltime\":\"2024-11-19 17:09:10.490\",\"srcsaleno\":\"\"}"
    ).forEach(message -> {
      SendResult sendResult = template.syncSend(hdOfflineOrder + ":" + tag, message, 6000);
      if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
        log.error("补充海典退单数据,消息发送失败,{}", message);
      }
    });

    XxlJobHelper.handleSuccess();

  }
//  public void executeBaK() {
//    String completeRefundDataHandlerHitKey = "completeRefundDataHandlerHit";
//    String value = RedisStringUtil.getValue(completeRefundDataHandlerHitKey);
//    XxlJobHelper.log("completeRefundDataHandlerHitKey:{}, value:{}" ,completeRefundDataHandlerHitKey,value);
//    if (!StringUtils.isEmpty(value)) {
//      XxlJobHelper.log("补偿任务已经触发," + value); // 只允许出发一次
//      XxlJobHelper.handleSuccess();
//      return;
//    }
//
//    RedisStringUtil.setValue(completeRefundDataHandlerHitKey, "hited," + new Date());
//
//    for (String collectionName : Lists.newArrayList(Collection_HD, Collection_KC)) {
//      // 初始化查询条件
//      Query query = buildQuery();
//
//      // 记录最后一个处理的ID
//      String lastId = null;
//
//      while (true) {
//        // 如果不是第一次，则添加大于上一个ID的条件
//        if (lastId != null) {
//          query.addCriteria(Criteria.where("_id").gt(new ObjectId(lastId)));
//        }
//
//        // 查询数据
//        List<MqData> mqDataList = mongoTemplate.find(query, MqData.class, collectionName);
//
//        // 如果没有数据，表示已处理完全部数据
//        if (mqDataList.isEmpty()) {
//          break;
//        }
//
//        // 处理这一批数据
//        processBatch(mqDataList, collectionName);
//
//        // 更新最后一个ID
//        lastId = mqDataList.get(mqDataList.size() - 1).getId();
//        query = buildQuery();
//      }
//    }
//    XxlJobHelper.handleSuccess();
//  }

//  @NotNull
//  private Query buildQuery() {
//    Query query = new Query();
//    // 按 _id 升序排序
//    query.with(Sort.by(Sort.Direction.ASC, "_id"));
//    query.limit(completeRefundDataBatchSize);
//    return query;
//  }

//  private void processBatch(List<MqData> mqDataList, String collectionName) {
//    // 在这里编写处理每一批数据的逻辑
//    for (MqData mqData : mqDataList) {
//      if (collectionName.equals(Collection_HD)) {
//        handleHd(mqData);
//      } else if (collectionName.equals(Collection_KC)) {
//        handleKc(mqData);
//      } else {
//        log.info("未匹配到的Collection,{}", JsonUtils.toJson(mqData));
//      }
//      // 刷创建时间
//      if (Objects.isNull(mqData.getCreateDateTime())) {
//        Query query = new Query(Criteria.where("_id").is(mqData.getId()));
//        Update update = new Update();
//        Date createDate = new Date(mqData.getCreateTime() * 1000L);
//        update.set("createDateTime", createDate);
//        mongoTemplate.updateFirst(query, update, MqData.class, collectionName);
//
//        // 判断时间是否超过指定天数,趁着这次机会删除历史累计数据(不会删退单),后面设置ttl时间自动过期
//        if (Math.abs(CommonDateUtils.calculateDaysBetween(createDate, new Date())) > expiredDays
//        && (mqData.getMessage().contains("正向单") || mqData.getMessage().contains("baseOrderInfo"))
//        ) {
//          mongoTemplate.remove(query,MqData.class, collectionName);
//        }
//      }
//    }
//  }

//  private void handleKc(MqData mqData) {
//    String message = mqData.getMessage();
//    if (message.contains("baseOrderInfo")) {
//      return; // 正向单直接忽略
//    }
//
//    String tag = "TAG_COMPENSATE_REFUND_DATA";
//    SendResult sendResult = template.syncSend(offlineOrderSyncKc + ":" + tag, message, 6000);
//    if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
//      log.error("补充科传退单数据,消息发送失败,{}", message);
//    }
//  }

//  private void handleHd(MqData mqData) {
//    String message = mqData.getMessage();
//    if (message.contains("正向单")) {
//      return; // 正向单直接忽略
//    }
//
//    String tag = "TAG_COMPENSATE_REFUND_DATA";
//    SendResult sendResult = template.syncSend(hdOfflineOrder + ":" + tag, message, 6000);
//    if (!SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
//      log.error("补充海典退单数据,消息发送失败,{}", message);
//    }
//  }
}
