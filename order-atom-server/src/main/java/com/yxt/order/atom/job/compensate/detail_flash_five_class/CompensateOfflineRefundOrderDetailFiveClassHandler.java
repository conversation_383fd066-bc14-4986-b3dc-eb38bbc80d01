package com.yxt.order.atom.job.compensate.detail_flash_five_class;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.migration.service.MigrationCommonComponent;
import com.yxt.order.atom.migration.service.dto.GoodsInfo;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderDetailMapper;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 补偿订单明细五级分类-线下退单明细
 * @author: moatkon
 * @time: 2025/2/10 11:20
 */
@Component
public class CompensateOfflineRefundOrderDetailFiveClassHandler extends
    AbstractFlash<OfflineRefundOrderDetailDO, OfflineRefundOrderDetailDO, FlashDetailFiveClass> {

  @Resource
  private OfflineRefundOrderDetailMapper offlineRefundOrderDetailMapper;

  @Resource
  private MigrationCommonComponent migrationCommonComponent;

  @Value("${flashFiveClassLimit:500}")
  private Integer flashFiveClassLimit;

  @Override
  protected Long queryCursorStartId() {
    return offlineRefundOrderDetailMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return offlineRefundOrderDetailMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineRefundOrderDetailDO> getSourceList() {
    LambdaQueryWrapper<OfflineRefundOrderDetailDO> query = new LambdaQueryWrapper<>();
    query.ge(OfflineRefundOrderDetailDO::getId, getFlashParam().getCursorStartId());
    query.lt(OfflineRefundOrderDetailDO::getId, getFlashParam().getCursorStartId() + defaultLimit());
    return offlineRefundOrderDetailMapper.selectList(query);
  }

  @Override
  protected List<OfflineRefundOrderDetailDO> assembleTargetData(
      List<OfflineRefundOrderDetailDO> offlineRefundOrderDetailDOS) {
    return offlineRefundOrderDetailDOS;
  }

  @Override
  protected void flash(List<OfflineRefundOrderDetailDO> offlineOrderDetailDOS) {
    if (CollectionUtils.isEmpty(offlineOrderDetailDOS)) {
      return;
    }

    Set<String> erpCodeSet = offlineOrderDetailDOS.stream()
        .filter(CompensateOfflineRefundOrderDetailFiveClassHandler::missFiveClass)
        .map(OfflineRefundOrderDetailDO::getErpCode).collect(Collectors.toSet());
    if(CollectionUtils.isEmpty(erpCodeSet)){
      return;
    }

    Map<String, GoodsInfo> goodsInfoMap = migrationCommonComponent.getGoodsInfo(erpCodeSet);

    for (OfflineRefundOrderDetailDO offlineRefundOrderDetail : offlineOrderDetailDOS) {
      if (!missFiveClass(offlineRefundOrderDetail) || Objects.isNull(offlineRefundOrderDetail.getId())) {
        continue;
      }

      GoodsInfo goodsInfo = goodsInfoMap.get(offlineRefundOrderDetail.getErpCode());
      if(Objects.isNull(goodsInfo)){
        continue;
      }


      offlineRefundOrderDetail.setFiveClass(goodsInfo.getFiveClass());
      offlineRefundOrderDetail.setFiveClassName(goodsInfo.getFiveClassName());
      offlineRefundOrderDetailMapper.updateById(offlineRefundOrderDetail);
    }

  }

  private static boolean missFiveClass(OfflineRefundOrderDetailDO offlineRefundOrderDetailDO) {
    return StringUtils.isEmpty(offlineRefundOrderDetailDO.getFiveClass()) || StringUtils.isEmpty(
        offlineRefundOrderDetailDO.getFiveClassName());
  }


  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }


  @Override
  protected Integer defaultLimit() {
    return flashFiveClassLimit;
  }
}
