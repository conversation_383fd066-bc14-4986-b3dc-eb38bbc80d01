package com.yxt.order.atom.job.compensate.mongo;

import java.util.Date;
import lombok.Data;

/**
 * 补充结果
 *
 * @author: moatkon
 * @time: 2024/11/28 16:39
 */
@Data
public class CompensateResultV1 {

  /**
   * 数据Json
   */
  private String dataJson;

  /**
   * 补偿类型
   */
  private CompensateType compensateType;

  /**
   * 创建时间
   */
  private Date createTime;


  private CompensateRetry retry;

  private String reason;

  /**
   * 补偿结果
   */
  private Boolean result;

}
