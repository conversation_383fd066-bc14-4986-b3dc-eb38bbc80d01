package com.yxt.order.atom.migration;

import static com.yxt.order.atom.migration.config.ThreadPoolConfig.MIGRATION_THREAD_POOL;

import com.google.common.collect.Lists;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.utils.RedisStringUtil;
import com.yxt.order.atom.migration.dao.HanaMigrationDO;
import com.yxt.order.atom.migration.req.MigrationReq;
import com.yxt.order.atom.migration.req.ReHandleErrorData;
import com.yxt.order.atom.migration.req.SpecifyMigrationBatchReq;
import com.yxt.order.atom.migration.req.SpecifyMigrationId;
import com.yxt.order.atom.migration.res.MigrationRes;
import com.yxt.order.atom.migration.service.MigrationService;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年06月25日 13:48
 * @email: <EMAIL>
 */
@Slf4j
@RestController
public class MigrationHanaController extends AbstractController {

  @Resource
  private MigrationService migrationService;

  @Qualifier(MIGRATION_THREAD_POOL)
  @Resource
  private ThreadPoolExecutor migrationThreadPool;



  @ApiOperation("异步迁移hana数据")
  @PostMapping("/migration-hana")
  public ResponseBase<List<MigrationRes>> migration() {
    List<MigrationRes> migrationResList = Lists.newArrayList();

    List<HanaMigrationDO> enableMigrationList = migrationService.queryEnableMigrationConfigList(null);
    if (CollectionUtils.isEmpty(enableMigrationList)) {
      return ResponseBase.success(migrationResList);
    }
    log.info("本次启动迁移的所有迁移配置,条数: {} ,详情: {}",enableMigrationList.size(), JsonUtils.toJson(enableMigrationList));

    for (HanaMigrationDO hanaMigrationDO : enableMigrationList) {
      // redis分布是锁
      String taskKey = hanaMigrationDO.taskKey();
      String value = RedisStringUtil.getValue(taskKey);
      if (!StringUtils.isEmpty(value)) {
        log.info("{} 迁移已触发,请勿重新触发! {}", taskKey,
            JsonUtils.toJson(hanaMigrationDO));
//        migrationResList.add(MigrationRes.builder().taskKey(hanaMigrationDO.taskKey())
//            .configJson(JsonUtils.toJson(hanaMigrationDO)).triggerResult("迁移已触发,请勿重新触发")
//            .build()); // 不输出
        continue;
      }

      RedisStringUtil.setValue(taskKey, JsonUtils.toJson(hanaMigrationDO));

      // 触发迁移
      migrationService.migration(hanaMigrationDO);
      migrationResList.add(MigrationRes.builder().taskKey(taskKey)
          .configJson(JsonUtils.toJson(hanaMigrationDO)).triggerResult("触发成功").build());
      log.info("迁移hana数据脚本已经触发,{}", JsonUtils.toJson(hanaMigrationDO));
    }

    return ResponseBase.success(migrationResList);
  }

  @ApiOperation("同步迁移hana数据")
  @PostMapping("/migration-hana-sync")
  public ResponseBase<String> migrationSync(@RequestBody @Valid MigrationReq req) {
    List<HanaMigrationDO> enableMigrationList = migrationService.queryEnableMigrationConfigList(req);
    if (CollectionUtils.isEmpty(enableMigrationList)) {
      return ResponseBase.success("没有需要同步迁移的配置,无需处理");
    }
    log.info("本次启动同步迁移的所有迁移配置,条数: {} ,详情: {}",enableMigrationList.size(), JsonUtils.toJson(enableMigrationList));

    migrationThreadPool.submit(() -> {
      for (HanaMigrationDO hanaMigrationDO : enableMigrationList) {
        String syncKey = hanaMigrationDO.taskKeySync();
        String value = RedisStringUtil.getValue(syncKey);
        if (!StringUtils.isEmpty(value)) {
          log.info("{} 同步迁移已触发,请勿重新触发! {}", syncKey,
              JsonUtils.toJson(hanaMigrationDO));
          continue;
        }

        RedisStringUtil.setValue(syncKey, JsonUtils.toJson(hanaMigrationDO));

        // 触发迁移
        migrationService.migrationSync(hanaMigrationDO);

        log.info("同步迁移hana数据脚本已经触发,{}", JsonUtils.toJson(hanaMigrationDO));
      }
    });

    return ResponseBase.success("同步迁移触发成功,条数:"+enableMigrationList.size());
  }



  /**
   * {
   * 	"hanaMigrationId": 889,
   * 	"id": 1,
   * 	"schema": "ynhx_data01"
   * }
   * @param specifyMigrationId
   * @return
   */
  @PostMapping("/specify-archive-id")
  public ResponseBase<Boolean> specifyArchiveId(@RequestBody @Valid SpecifyMigrationId specifyMigrationId) {
    Boolean success = migrationService.specifyArchiveId(specifyMigrationId);
    return ResponseBase.success(success);
  }
  @PostMapping("/specify-archive-batch")
  public ResponseBase<Boolean> specifyArchiveBatch(@RequestBody @Valid SpecifyMigrationBatchReq specifyMigrationBatchReq) {
    Boolean success = migrationService.specifyArchiveBatch(specifyMigrationBatchReq);
    return ResponseBase.success(success);
  }


  @PostMapping("/reHandleError")
  public ResponseBase<Boolean> reHandleError(@RequestBody @Valid ReHandleErrorData reHandleErrorData) {
    Boolean success = migrationService.reHandleError(reHandleErrorData);
    return ResponseBase.success(success);
  }

}
