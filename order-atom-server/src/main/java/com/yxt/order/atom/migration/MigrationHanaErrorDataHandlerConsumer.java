package com.yxt.order.atom.migration;

import static com.yxt.order.atom.migration.MigrationHanaErrorDataHandler.MIGRATION_RE_HANDLE_TAG;

import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.AbstractRocketMQListenerEnhance;
import com.yxt.order.atom.migration.message.MigrationEventReHandleMessage;
import com.yxt.order.atom.migration.service.HanaMigrationService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年06月26日 11:32
 * @email: <EMAIL>
 */
@Slf4j
@Component
@RocketMQMessageListener(consumerGroup = "${mq.topic.producer.migrationHanaDataReHandle}_ORDER", topic = "${mq.topic.producer.migrationHanaDataReHandle}", selectorExpression = MIGRATION_RE_HANDLE_TAG, consumeMode = ConsumeMode.CONCURRENTLY)
public class MigrationHanaErrorDataHandlerConsumer extends AbstractRocketMQListenerEnhance {

  @Value("${migrationHanaDataMinConsumeNum:32}")
  private Integer migrationHanaDataMinConsumeNum;

  @Value("${migrationHanaDataMaxConsumeNum:32}")
  private Integer migrationHanaDataMaxConsumeNum;

  @Override
  public Integer minConsumeThreadNum() {
    return migrationHanaDataMinConsumeNum;
  }

  @Override
  public Integer maxConsumeThreadNum() {
    return migrationHanaDataMaxConsumeNum;
  }


  @Resource
  private HanaMigrationService hanaMigrationService;



  @Value("${migrationHanaErrorDataHandlerConsumerClose:true}")
  private Boolean migrationHanaErrorDataHandlerConsumerClose;


  @Override
  public void handleMsg(String messageStr) {

    if(migrationHanaErrorDataHandlerConsumerClose){
      return;
    }

    log.info("MigrationEventMessageReHandleConsumer msg: {}", messageStr);

    MigrationEventReHandleMessage message = JsonUtils.toObject(messageStr,
        MigrationEventReHandleMessage.class);
    hanaMigrationService.reHandleMigrationErrorData(message);
  }


}
