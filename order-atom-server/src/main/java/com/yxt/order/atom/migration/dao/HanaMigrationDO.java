package com.yxt.order.atom.migration.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * Hana迁移配置,做DB配置,不使用Apollo了
 *
 * <AUTHOR> (moatkon)
 * @date 2024年06月06日 17:15
 * @email: <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("hana_migration_stage_2_remigrate") // 第二阶段迁移
public class HanaMigrationDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  private String migrateSort; // 迁移顺序 ORDER\REFUND
  private String companyName; // 公司名
  private String targetSchema; // 数据库schema(小写)
  private String migrationStartTime; // 迁移的开始时间(发送MQ)
  private String migrationEndTime; // 迁移的结束时间(发送MQ)
//  private Long migrationTotalCount; // 迁移总数
//  private Long migrationTotalCountSuccess; // 成功迁移总数
//  private Long sendMqCount; // 成功发送到迁移MQ队列的数量
  private String onOff; // 启用状态 true,false
//  private Long cursorId; // 当前游标ID
  private String migrationResult;
  private String note;
  private String configJson;

  public String getTargetSchema() {
    return targetSchema.toLowerCase();
  }


  public String taskKey() {
    return String.format("hanaMigration-stage2-remigrate:%s:%s:%s:%s", companyName, migrateSort,
        getTargetSchema(), id);
  }


  public String taskKeySync() {
    return String.format("hanaMigration-stage2-remigrate-sync:%s:%s:%s:%s", companyName, migrateSort,
        getTargetSchema(), id);
  }
}
