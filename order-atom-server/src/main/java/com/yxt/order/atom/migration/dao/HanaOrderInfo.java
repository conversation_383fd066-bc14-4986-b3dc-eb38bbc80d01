package com.yxt.order.atom.migration.dao;

import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年06月06日 16:56
 * @email: <EMAIL>
 */
@Data
public class HanaOrderInfo {

  private Long id;
  private String thirdOrderNo;
  private String thirdRefundNo;
  private Date createTime;
  private String txDate;
  private String txTime;
  private Long orderId;
  private Long otherOrderId;
  private BigDecimal actualPayAmount;
  private BigDecimal actualCollectAmount;
  private BigDecimal sellingAmount; // 同步错误的订单金额
  private String posCashierDeskNo;
  private String cashier;
  private String picker;
  private String storeCode;
  // 关联查询会员卡号
  private String clientCode;

  private Integer migration;

  private String extendJson;

  public List<Long> orderIdList() {
    return Lists.newArrayList(this.orderId,this.otherOrderId);
  }
}
