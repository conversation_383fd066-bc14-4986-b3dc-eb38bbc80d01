package com.yxt.order.atom.migration.feign;


import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.migration.service.dto.CommoditySourceDTO;
import com.yxt.order.atom.migration.service.dto.CommoditySourceInfoReq;
import com.yxt.order.atom.migration.service.dto.CommoditySourceInfoResp;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: moatkon
 * @time: 2024/12/12 14:43
 */
@FeignClient(value = "hydee-middle-merchandise")
public interface GoodsApiFeign {

  @PostMapping("/api/commodity/queryCommoditySourceInfo")
  ResponseBase<List<CommoditySourceInfoResp>> getGoodsInfo(
      @RequestBody CommoditySourceInfoReq req);

  @PostMapping("/1.0/comm_source/getByErpCode/{erpCode}")
  ResponseBase<CommoditySourceDTO> getByErpCode(@PathVariable("erpCode") String erpCode);


}
