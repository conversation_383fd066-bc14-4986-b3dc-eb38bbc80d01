package com.yxt.order.atom.migration.fix.component;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.migration.constant.MigrationConstant;
import com.yxt.order.atom.migration.fix.RemoveEsOrderRepeatedDataScene;
import com.yxt.order.atom.migration.fix.component.es.EsDataOperateComponent;
import com.yxt.order.atom.migration.fix.dto.RepeatedEsDataOperate;
import com.yxt.order.atom.order.entity.DeletedDataDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.mapper.DeletedDataMapper;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2025/3/11 16:25
 */
@Component
@Slf4j
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
@Deprecated
public class RemoveEsOrderRepeatedData extends
    AbstractFlash<DeletedDataDO, DeletedDataDO, RemoveEsOrderRepeatedDataScene> {


  @Value("${keChuanTotalAmountDataGetLimit:2000}")
  private Integer keChuanTotalAmountDataGetLimit;

  @Resource
  private DeletedDataMapper deletedDataMapper;


  @Resource
  private EsDataOperateComponent esDataOperateComponent;



  @Override
  protected Long queryCursorStartId() {
    CustomData customData = getCustomData();
    String suffix = customData.getSuffix();
    Long startId = customData.getStartId();

    return Objects.nonNull(startId) ? startId : deletedDataMapper.selectMinId(suffix);
  }

  @Override
  protected Long queryCursorEndId() {
    CustomData customData = getCustomData();
    String suffix = customData.getSuffix();
    Long endId = customData.getEndId();

    return Objects.nonNull(endId) ? endId : deletedDataMapper.selectMaxId(suffix);
  }

  @Override
  protected List<DeletedDataDO> getSourceList() {
    FlashParam flashParam = getFlashParam();
    Long startId = flashParam.getCursorStartId();
    Long endId = startId + defaultLimit();

    CustomData customData = getCustomData();
    String suffix = customData.getSuffix();

    return deletedDataMapper.selectListByMinMaxId(suffix, startId, endId);
  }

  @Override
  protected List<DeletedDataDO> assembleTargetData(List<DeletedDataDO> deletedDataDOList) {
    return deletedDataDOList;
  }


  /**
   * 因为无输入逻辑,可以直接刷数
   *
   * @param deletedDataDOList
   */
  @Override
  protected void flash(List<DeletedDataDO> deletedDataDOList) {
    removeEsDataByDeletedDataDO(deletedDataDOList);
  }

  public void removeEsDataByDeletedDataDO(List<DeletedDataDO> deletedDataDOList) {
    for (DeletedDataDO deletedDataDO : deletedDataDOList) {
      if (StringUtils.isEmpty(deletedDataDO.getDeletedData()) || StringUtils.isEmpty(
          deletedDataDO.getBusinessNo()) || !MigrationConstant.DELETED_SET.contains(deletedDataDO.getReason())) {
        continue;
      }
      RepeatedEsDataOperate repeatedEsDataOperate = new RepeatedEsDataOperate(deletedDataDO);

      esDataOperateComponent.removeEs(repeatedEsDataOperate);
    }
  }



  @Override
  protected Integer defaultLimit() {
    return keChuanTotalAmountDataGetLimit;
  }
}