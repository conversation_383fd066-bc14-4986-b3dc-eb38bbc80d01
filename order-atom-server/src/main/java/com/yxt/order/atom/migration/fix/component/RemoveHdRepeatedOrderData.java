package com.yxt.order.atom.migration.fix.component;

import static com.yxt.order.atom.migration.constant.MigrationConstant.DELETED_HD_TXSERIAL_REPEATED;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.migration.dao.HanaMigrationMapper;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.atom.migration.req.RemoveHdRepeatedOrderReq;
import com.yxt.order.atom.migration.service.HdOrderExistCheckComponent;
import com.yxt.order.atom.migration.service.OrderDataRelationComponent;
import com.yxt.order.atom.migration.service.dto.MigrationExtend;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.repair.dto.StartEndId;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * @author: moatkon
 * @time: 2025/3/14 19:32
 */
@Component
@Slf4j
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
@Deprecated
public class RemoveHdRepeatedOrderData {

  @Value("${hanaMigrationServiceLogError:false}")
  private Boolean hanaMigrationServiceLogError;

  // sql里面没有根据金额来判断,所以会同时处理正单和退单
  private static final List<Integer> MIGRATION_SUCCESS_LIST = Lists.newArrayList(1,
      2); // 1	迁移成功 , 2-已存在 https://yxtcf.hxyxt.com/pages/viewpage.action?pageId=57799101

  @Resource
  private OrderDataRelationComponent orderDataRelationComponent;

  @Resource
  private HdOrderExistCheckComponent hdOrderExistCheckComponent;


  @Value("${keChuanTotalAmountDataGetLimit:2000}")
  private Integer keChuanTotalAmountDataGetLimit;

  @Resource
  private HanaMigrationMapper hanaMigrationMapper;

  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  @Resource
  private OfflineOrderRepository offlineOrderRepository;


  public void removeHdRepeatedOrder(RemoveHdRepeatedOrderReq req) {
    String targetSchema = req.getTargetSchema();

    StartEndId startEndId = new StartEndId();
    startEndId.setStartId(req.getStartId());
    startEndId.setEndId(req.getEndId());

    while (!startEndId.empty() && startEndId.getStartId() <= startEndId.getEndId()) {
      Long startId = startEndId.getStartId();
      Long endId = startEndId.getStartId() + keChuanTotalAmountDataGetLimit;

      List<HanaOrderInfo> hanaOrderInfoList = hanaMigrationMapper.queryNeedFixedHanaOrder(
          targetSchema, startId, endId, MIGRATION_SUCCESS_LIST/*, migrateSort*/);

      if (CollectionUtils.isEmpty(hanaOrderInfoList)) {
        refreshStartId(startEndId);
        continue;
      }

      for (HanaOrderInfo hanaOrderInfo : hanaOrderInfoList) {
        try {

          // 1. 先判断长度是否是16位,海典固定长度是16位
          String orderIdStr = String.valueOf(hanaOrderInfo.getOtherOrderId());
          if (orderIdStr.length() != 16) { // 海典的单号固定是16位
            continue;
          }

          // 2. 只处理指定状态的数据
          if (!MIGRATION_SUCCESS_LIST.contains(
              hanaOrderInfo.getMigration())) { // 再判断一遍,如果不是迁移成功的则不处理
            continue;
          }

          String extendJson = hanaOrderInfo.getExtendJson();
          if (StringUtils.isEmpty(extendJson)) {
            continue;
          }

          MigrationExtend extend = JsonUtils.toObject(extendJson,
              new TypeReference<MigrationExtend>() {
              });
          String orderNo = extend.getOrderNo();
          String refundNo = extend.getRefundNo();
          if (!StringUtils.isEmpty(orderNo)) {
            handleHanaOrder(hanaOrderInfo, orderNo, targetSchema);
          } else if (!StringUtils.isEmpty(refundNo)) {
            handleHanaRefundOrder(hanaOrderInfo, refundNo, targetSchema);
          }
        } catch (Exception e) {
          if (hanaMigrationServiceLogError) {
            log.warn("RemoveHdRepeatedOrderData warn message", e);
          }
        }
      }
      // 刷新起始Id
      refreshStartId(startEndId);
    }


  }


  private void refreshStartId(StartEndId startEndId) {
    long startId = startEndId.getStartId() + keChuanTotalAmountDataGetLimit;
    log.info("refreshStartId {}",startId);
    startEndId.setStartId(startId);
  }


  public void handleHanaOrder(HanaOrderInfo hanaOrderInfo, String orderNo, String targetSchema) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<OfflineOrderDO> query = new LambdaQueryWrapper<>();
      query.eq(OfflineOrderDO::getOrderNo, orderNo);
      OfflineOrderDO migrationOrder = offlineOrderMapper.selectOne(query);
      if (Objects.isNull(migrationOrder) || Objects.isNull(migrationOrder.getId())) {
        return; // 查不到,就是被清除了,不用处理
      }

      if (!ThirdPlatformCodeEnum.HAIDIAN.name().equals(migrationOrder.getThirdPlatformCode())) {
        return;
      }

      // 如果不是迁移订单,则不处理
      if (!Boolean.TRUE.toString().equals(migrationOrder.getMigration())) {
        return;
      }

      // 记录关系
      orderDataRelationComponent.orderRelation(orderNo, targetSchema, hanaOrderInfo);

      if (hdOrderExistCheckComponent.hdOfflineOrderIsExists(hanaOrderInfo, migrationOrder)) {
        // 删除
        offlineOrderRepository.deletedOfflineOrder(migrationOrder, DELETED_HD_TXSERIAL_REPEATED,
            Boolean.TRUE);
      }

    }
  }

  public void handleHanaRefundOrder(HanaOrderInfo hanaOrderInfo, String refundNo,
      String targetSchema) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<OfflineRefundOrderDO> query = new LambdaQueryWrapper<>();
      query.eq(OfflineRefundOrderDO::getRefundNo, refundNo);
      OfflineRefundOrderDO offlineRefundOrderDO = offlineRefundOrderMapper.selectOne(query);
      if (Objects.isNull(offlineRefundOrderDO) || Objects.isNull(offlineRefundOrderDO.getId())) {
        return; // 查不到,就是被清除了,不用处理
      }

      if (!ThirdPlatformCodeEnum.HAIDIAN.name()
          .equals(offlineRefundOrderDO.getThirdPlatformCode())) {
        return;
      }

      // 如果不是迁移订单,则不处理
      if (!Boolean.TRUE.toString().equals(offlineRefundOrderDO.getMigration())) {
        return;
      }

      // 记录关系
      orderDataRelationComponent.refundRelation(refundNo, targetSchema, hanaOrderInfo);

      // 检查是否重复
      if (hdOrderExistCheckComponent.hdOfflineRefundOrderIsExists(hanaOrderInfo,
          offlineRefundOrderDO)) { //如果有2条则删除
        // 删除
        offlineOrderRepository.deletedOfflineRefundOrder(offlineRefundOrderDO,
            DELETED_HD_TXSERIAL_REPEATED, Boolean.TRUE);
      }

    }
  }


}
