package com.yxt.order.atom.migration.fix.component;

import static com.yxt.order.atom.migration.constant.MigrationConstant.DELETED_MIGRATION_REPEATED;
import static com.yxt.order.atom.order.repository.impl.OfflineOrderRepositoryImpl.buildRefundOrderExistsQuery;
import static com.yxt.order.atom.order.repository.impl.OfflineOrderRepositoryImpl.buildRefundOrderExistsQueryOnlyForKeChuan;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.migration.fix.RemoveMigrationRefundOrderRepeatedData;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderExistsReqDto;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/13 10:21
 * <p>
 * 只修复正向订单; 退单科传没有传totalAmount字段
 */
@Component
@Slf4j
@Deprecated
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class RemoveRefundOrderRepeatedData extends
    AbstractFlash<OfflineRefundOrderDO, OfflineRefundOrderDO, RemoveMigrationRefundOrderRepeatedData> {

  @Value("${keChuanTotalAmountDataGetLimit:2000}")
  private Integer keChuanTotalAmountDataGetLimit;

  @Resource
  private OfflineOrderRepository offlineOrderRepository;


  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;


  @Override
  protected Long queryCursorStartId() {
    CustomData customData = getCustomData();
    Long startId = customData.getStartId();
    return Objects.nonNull(startId) ? startId : offlineRefundOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    CustomData customData = getCustomData();
    Long endId = customData.getEndId();
    return Objects.nonNull(endId) ? endId : offlineRefundOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineRefundOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineRefundOrderDO> query = FlashQueryWrapper.offlineRefundOrderFlashQuery(
        getFlashParam(), defaultLimit());
    return offlineRefundOrderMapper.selectList(query);
  }

  @Override
  protected List<OfflineRefundOrderDO> assembleTargetData(
      List<OfflineRefundOrderDO> offlineOrderDOList) {
    return offlineOrderDOList;
  }

  /**
   * 因为无输入逻辑,可以直接刷数
   *
   * @param offlineRefundOrderDOList
   */
  @Override
  protected void flash(List<OfflineRefundOrderDO> offlineRefundOrderDOList) {
    for (OfflineRefundOrderDO offlineRefundOrderDO : offlineRefundOrderDOList) {
      CustomData customData = getCustomData();
      Boolean onlyHandleKeChuan = customData.getOnlyHandleKeChuan();

      String migration = offlineRefundOrderDO.getMigration();
      if (StringUtils.isEmpty(migration)) {
        continue;
      }
      if (!Boolean.TRUE.toString().equals(migration)) {
        continue;
      }

      if(onlyHandleKeChuan && !ThirdPlatformCodeEnum.KE_CHUAN.name().equals(offlineRefundOrderDO.getThirdPlatformCode())){
        continue;
      }

      // 1. 检查是否有多条
      OfflineRefundOrderExistsReqDto check1 = new OfflineRefundOrderExistsReqDto();
      check1.setStoreCode(offlineRefundOrderDO.getStoreCode());
      check1.setThirdRefundNo(offlineRefundOrderDO.getThirdRefundNo());
      check1.setThirdPlatformCode(offlineRefundOrderDO.getThirdPlatformCode());
      check1.setDefineNo(offlineRefundOrderDO.getRefundNo());
//      reqDto.setThirdCreated();

      LambdaQueryWrapper<OfflineRefundOrderDO> check1Count = buildRefundOrderExistsQuery(check1);
      if (offlineRefundOrderMapper.selectCount(check1Count) <= 1) {
        continue;
      }

      // 2. 检查是否真的重复
      if(onlyHandleKeChuan){
        OfflineRefundOrderExistsReqDto check2 = new OfflineRefundOrderExistsReqDto();
        check2.setStoreCode(offlineRefundOrderDO.getStoreCode());
        check2.setThirdRefundNo(offlineRefundOrderDO.getThirdRefundNo());
        check2.setThirdPlatformCode(offlineRefundOrderDO.getThirdPlatformCode());
        check2.setDefineNo(offlineRefundOrderDO.getRefundNo());
        check2.setThirdCreated(offlineRefundOrderDO.getBillTime());
        LambdaQueryWrapper<OfflineRefundOrderDO> check2Count = buildRefundOrderExistsQueryOnlyForKeChuan(check2);
        if (offlineRefundOrderMapper.selectCount(check2Count)  != 2) { // 科传版本问题导致24年11月29号之前的订单时间和订单时间不一致。这查询需要有2条记录
          continue;
        }
      }else {
        OfflineRefundOrderExistsReqDto check2 = new OfflineRefundOrderExistsReqDto();
        check2.setStoreCode(offlineRefundOrderDO.getStoreCode());
        check2.setThirdRefundNo(offlineRefundOrderDO.getThirdRefundNo());
        check2.setThirdPlatformCode(offlineRefundOrderDO.getThirdPlatformCode());
        check2.setDefineNo(offlineRefundOrderDO.getRefundNo());
        check2.setThirdCreated(offlineRefundOrderDO.getBillTime());
        LambdaQueryWrapper<OfflineRefundOrderDO> check2Count = buildRefundOrderExistsQuery(check2);
        if (offlineRefundOrderMapper.selectCount(check2Count)  != 1) { // 必须为1条,如果不是,则不处理
          continue;
        }
      }


      if (Objects.isNull(offlineRefundOrderDO.getId())) {
        continue;
      }

      offlineOrderRepository.deletedOfflineRefundOrder(offlineRefundOrderDO,
          DELETED_MIGRATION_REPEATED, Boolean.TRUE);
    }

  }


  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }

  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }

  @Override
  protected Integer defaultLimit() {
    return keChuanTotalAmountDataGetLimit;
  }
}