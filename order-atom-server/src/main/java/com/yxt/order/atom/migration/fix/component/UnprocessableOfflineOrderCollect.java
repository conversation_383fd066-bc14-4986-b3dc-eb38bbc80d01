package com.yxt.order.atom.migration.fix.component;

import static com.yxt.order.atom.order.repository.impl.OfflineOrderRepositoryImpl.buildOrderExistsQuery;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.migration.dao.UnprocessableOrderDO;
import com.yxt.order.atom.migration.dao.UnprocessableOrderRepository;
import com.yxt.order.atom.migration.dao.enums.UnprocessableSceneEnum;
import com.yxt.order.atom.migration.dao.enums.UnprocessableStatusEnum;
import com.yxt.order.atom.migration.fix.UnprocessableOfflineOrderCollectScene;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderExistsReqDto;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import com.yxt.order.types.utils.ShardingHelper;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2025/3/13 18:38
 */
@Component
@Slf4j
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class UnprocessableOfflineOrderCollect extends
    AbstractFlash<OfflineOrderDO, OfflineOrderDO, UnprocessableOfflineOrderCollectScene> {

  @Value("${keChuanTotalAmountDataGetLimit:2000}")
  private Integer keChuanTotalAmountDataGetLimit;

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  @Resource
  private UnprocessableOrderRepository unprocessableOrderRepository;

  @Override
  protected Long queryCursorStartId() {
    CustomData customData = getCustomData();
    Long startId = customData.getStartId();
    return Objects.nonNull(startId) ? startId : offlineOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    CustomData customData = getCustomData();
    Long endId = customData.getEndId();
    return Objects.nonNull(endId) ? endId : offlineOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineOrderDO> query = FlashQueryWrapper.offlineOrderFlashQuery(
        getFlashParam(), defaultLimit());
    return offlineOrderMapper.selectList(query);
  }

  @Override
  protected List<OfflineOrderDO> assembleTargetData(List<OfflineOrderDO> offlineOrderDOList) {
    return offlineOrderDOList;
  }

  /**
   * 因为无输入逻辑,可以直接刷数
   *
   * @param offlineOrderList
   */
  @Override
  protected void flash(List<OfflineOrderDO> offlineOrderList) {
    CustomData customData = getCustomData();
    Boolean onlyHandleKeChuan = customData.getOnlyHandleKeChuan();

    for (OfflineOrderDO offlineOrderDO : offlineOrderList) {
      if (Objects.isNull(offlineOrderDO.getId())) {
        continue;
      }

      String migration = offlineOrderDO.getMigration();
      if (StringUtils.isEmpty(migration)) {
        continue;
      }
      if (!Boolean.TRUE.toString().equals(migration)) {
        continue;
      }

      if (onlyHandleKeChuan && !ThirdPlatformCodeEnum.KE_CHUAN.name()
          .equals(offlineOrderDO.getThirdPlatformCode())) {
        continue;
      }

      OfflineOrderExistsReqDto dto = new OfflineOrderExistsReqDto();
      dto.setStoreCode(offlineOrderDO.getStoreCode());
      dto.setThirdOrderNo(offlineOrderDO.getThirdOrderNo());
      dto.setThirdPlatformCode(offlineOrderDO.getThirdPlatformCode());
      dto.setDefineNo(offlineOrderDO.getOrderNo());
      LambdaQueryWrapper<OfflineOrderDO> query = buildOrderExistsQuery(dto);
      Integer count = offlineOrderMapper.selectCount(query);
      if (count >= 2) {
        List<OfflineOrderDO> offlineOrderDOS = offlineOrderMapper.selectList(query);


        String amountJson = offlineOrderDOS.stream()
            .map(OfflineOrderDO::getActualCollectAmount)
            .map(BigDecimal::toPlainString)
            .collect(Collectors.joining(","));

        List<UnprocessableOrderDO> collect = offlineOrderDOS.stream()
            .filter(item -> Boolean.TRUE.toString().equals(item.getMigration())) // 只记录迁移,减少数据量
            .map(item -> {
          UnprocessableOrderDO unprocessableOrderDO = new UnprocessableOrderDO();
          unprocessableOrderDO.setScene(UnprocessableSceneEnum.REPEATED_OFFLINE_ORDER.name());
          unprocessableOrderDO.setBusinessId(String.valueOf(item.getId()));
          unprocessableOrderDO.setBusinessNo(item.getOrderNo());
          unprocessableOrderDO.setThirdPlatformCode(item.getThirdPlatformCode());
          unprocessableOrderDO.setStoreCode(item.getStoreCode());
          unprocessableOrderDO.setThirdBusinessNo(item.getThirdOrderNo());
          unprocessableOrderDO.setCount(count);
          unprocessableOrderDO.setAmountJson(amountJson);
          unprocessableOrderDO.setShardingNo(ShardingHelper.getTableIndexByNo(item.getOrderNo()));
          unprocessableOrderDO.setAllowOperate(Boolean.FALSE.toString());
          unprocessableOrderDO.setStatus(UnprocessableStatusEnum.UN_HANDLE.name());
          unprocessableOrderDO.setNote(Strings.EMPTY);
          unprocessableOrderDO.setMigration(item.getMigration());
          return unprocessableOrderDO;
        }).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(collect)){
          continue;
        }

        unprocessableOrderRepository.saveBatch(collect);
      }
    }

  }


  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }

  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }

  @Override
  protected Integer defaultLimit() {
    return keChuanTotalAmountDataGetLimit;
  }
}
