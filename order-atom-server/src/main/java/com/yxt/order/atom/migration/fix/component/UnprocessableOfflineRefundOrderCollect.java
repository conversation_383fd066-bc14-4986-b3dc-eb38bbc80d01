package com.yxt.order.atom.migration.fix.component;

import static com.yxt.order.atom.order.repository.impl.OfflineOrderRepositoryImpl.buildRefundOrderExistsQuery;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.migration.dao.UnprocessableOrderDO;
import com.yxt.order.atom.migration.dao.UnprocessableOrderRepository;
import com.yxt.order.atom.migration.dao.enums.UnprocessableSceneEnum;
import com.yxt.order.atom.migration.dao.enums.UnprocessableStatusEnum;
import com.yxt.order.atom.migration.fix.UnprocessableOfflineRefundOrderCollectScene;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderExistsReqDto;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import com.yxt.order.types.utils.ShardingHelper;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2025/3/13 18:38
 */
@Component
@Slf4j
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class UnprocessableOfflineRefundOrderCollect extends
    AbstractFlash<OfflineRefundOrderDO, OfflineRefundOrderDO, UnprocessableOfflineRefundOrderCollectScene> {

  @Value("${keChuanTotalAmountDataGetLimit:2000}")
  private Integer keChuanTotalAmountDataGetLimit;


  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Resource
  private UnprocessableOrderRepository unprocessableOrderRepository;

  @Override
  protected Long queryCursorStartId() {
    CustomData customData = getCustomData();
    Long startId = customData.getStartId();
    return Objects.nonNull(startId) ? startId
        : offlineRefundOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    CustomData customData = getCustomData();
    Long endId = customData.getEndId();
    return Objects.nonNull(endId) ? endId : offlineRefundOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineRefundOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineRefundOrderDO> query = FlashQueryWrapper.offlineRefundOrderFlashQuery(
        getFlashParam(), defaultLimit());
    return offlineRefundOrderMapper.selectList(query);
  }

  @Override
  protected List<OfflineRefundOrderDO> assembleTargetData(
      List<OfflineRefundOrderDO> offlineOrderDOList) {
    return offlineOrderDOList;
  }

  /**
   * 因为无输入逻辑,可以直接刷数
   *
   * @param offlineRefundOrderDOList
   */
  @Override
  protected void flash(List<OfflineRefundOrderDO> offlineRefundOrderDOList) {
    for (OfflineRefundOrderDO offlineRefundOrderDO : offlineRefundOrderDOList) {
      if (Objects.isNull(offlineRefundOrderDO.getId())) {
        continue;
      }

      CustomData customData = getCustomData();
      Boolean onlyHandleKeChuan = customData.getOnlyHandleKeChuan();

      String migration = offlineRefundOrderDO.getMigration();
      if (StringUtils.isEmpty(migration)) {
        continue;
      }
      if (!Boolean.TRUE.toString().equals(migration)) {
        continue;
      }

      if (onlyHandleKeChuan && !ThirdPlatformCodeEnum.KE_CHUAN.name()
          .equals(offlineRefundOrderDO.getThirdPlatformCode())) {
        continue;
      }

      OfflineRefundOrderExistsReqDto dto = new OfflineRefundOrderExistsReqDto();
      dto.setStoreCode(offlineRefundOrderDO.getStoreCode());
      dto.setThirdRefundNo(offlineRefundOrderDO.getThirdRefundNo());
      dto.setThirdPlatformCode(offlineRefundOrderDO.getThirdPlatformCode());
      dto.setDefineNo(offlineRefundOrderDO.getRefundNo());
      LambdaQueryWrapper<OfflineRefundOrderDO> query = buildRefundOrderExistsQuery(dto);
      Integer count = offlineRefundOrderMapper.selectCount(query);
      if (count >= 2) {
        List<OfflineRefundOrderDO> offlineRefundOrderDOS = offlineRefundOrderMapper.selectList(query);

        String amountJson = offlineRefundOrderDOS.stream()
            .map(OfflineRefundOrderDO::getTotalAmount)
            .map(BigDecimal::toPlainString)
            .collect(Collectors.joining(","));

        List<UnprocessableOrderDO> collect = offlineRefundOrderDOS.stream()
            .filter(item -> Boolean.TRUE.toString().equals(item.getMigration())) // 只记录迁移,减少数据量
            .map(item -> {
          UnprocessableOrderDO unprocessableOrderDO = new UnprocessableOrderDO();
          unprocessableOrderDO.setScene(UnprocessableSceneEnum.REPEATED_OFFLINE_REFUND_ORDER.name());
          unprocessableOrderDO.setBusinessId(String.valueOf(item.getId()));
          unprocessableOrderDO.setBusinessNo(item.getRefundNo());
          unprocessableOrderDO.setThirdPlatformCode(item.getThirdPlatformCode());
          unprocessableOrderDO.setThirdBusinessNo(item.getThirdRefundNo());
          unprocessableOrderDO.setStoreCode(item.getStoreCode());
          unprocessableOrderDO.setShardingNo(ShardingHelper.getTableIndexByNo(item.getRefundNo()));
          unprocessableOrderDO.setCount(count);
          unprocessableOrderDO.setAmountJson(amountJson);
          unprocessableOrderDO.setAllowOperate(Boolean.FALSE.toString());
          unprocessableOrderDO.setStatus(UnprocessableStatusEnum.UN_HANDLE.name());
          unprocessableOrderDO.setNote(Strings.EMPTY);
          unprocessableOrderDO.setMigration(item.getMigration());
          return unprocessableOrderDO;
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(collect)){
          continue;
        }
        unprocessableOrderRepository.saveBatch(collect);
      }

    }

  }


  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }

  @Override
  protected Boolean isHandleNonVipData() {
    return Boolean.TRUE;
  }

  @Override
  protected Integer defaultLimit() {
    return keChuanTotalAmountDataGetLimit;
  }
}