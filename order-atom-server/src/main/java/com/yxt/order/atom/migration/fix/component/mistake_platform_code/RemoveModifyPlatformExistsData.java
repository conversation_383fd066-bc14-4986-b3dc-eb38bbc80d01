package com.yxt.order.atom.migration.fix.component.mistake_platform_code;

import static com.yxt.order.atom.migration.constant.MigrationConstant.DELETED_WRONG_PLATFORM_CODE;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.migration.dao.ModifyPlatformExistsDO;
import com.yxt.order.atom.migration.dao.ModifyPlatformExistsDOMapper;
import com.yxt.order.atom.migration.dao.enums.ModifyPlatformExistSceneEnum;
import com.yxt.order.atom.migration.dao.enums.ModifyPlatformExistStatusEnum;
import com.yxt.order.atom.migration.fix.dto.RemoveModifyPlatformExistsDataReq;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.repair.dto.StartEndId;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @note 20250517 这个已经确认就是重复的。挑选了正单和退单,检查都ok
 *
 * 移除修改平台已经存在的数据
 * <p>
 * ModifyPlatformExistsDO 这个表里面存的数据都是迁移的数据,可以直接删除
 */
@Component
@Slf4j
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
@Deprecated
public class RemoveModifyPlatformExistsData {

  @Value("${keChuanTotalAmountDataGetLimit:2000}")
  private Integer keChuanTotalAmountDataGetLimit;

  @Resource
  private ModifyPlatformExistsDOMapper modifyPlatformExistsDOMapper;

  @Resource
  private OfflineOrderRepository offlineOrderRepository;

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;


  public void handle(@Valid RemoveModifyPlatformExistsDataReq req) {

    StartEndId startEndId = new StartEndId();
    startEndId.setStartId(req.getStartId());
    startEndId.setEndId(req.getEndId());

    while (!startEndId.empty() && startEndId.getStartId() <= startEndId.getEndId()) {
      LambdaQueryWrapper<ModifyPlatformExistsDO> query = new LambdaQueryWrapper<>();

      query.ge(ModifyPlatformExistsDO::getId, startEndId.getStartId());
      query.lt(ModifyPlatformExistsDO::getId,
          startEndId.getStartId() + keChuanTotalAmountDataGetLimit);

      List<ModifyPlatformExistsDO> modifyPlatformExistsDOList = modifyPlatformExistsDOMapper.selectList(
          query);
      if (CollectionUtils.isEmpty(modifyPlatformExistsDOList)) {
        refreshStartId(startEndId);
        continue;
      }

      for (ModifyPlatformExistsDO modifyPlatformExistsDO : modifyPlatformExistsDOList) {
        if(ModifyPlatformExistStatusEnum.HANDLED.name().equals(modifyPlatformExistsDO.getStatus())){
          continue;
        }

        try {
          modifyPlatformExistsDO.setAllowOperate("true");

          String scene = modifyPlatformExistsDO.getScene();
          if (ModifyPlatformExistSceneEnum.ORDER.name().equals(scene)) {
            removeOrder(modifyPlatformExistsDO);
          } else if (ModifyPlatformExistSceneEnum.REFUND.name().equals(scene)) {
            removeRefund(modifyPlatformExistsDO);
          }

          modifyPlatformExistsDO.setStatus(ModifyPlatformExistStatusEnum.HANDLED.name());
        } catch (Exception e) {
          log.warn("清理重复数据异常", e);
          modifyPlatformExistsDO.setAllowOperate("false");
          modifyPlatformExistsDO.setNote(e.getMessage());
        }

        modifyPlatformExistsDOMapper.updateById(modifyPlatformExistsDO);
      }

      // 刷新起始Id
      refreshStartId(startEndId);
    }
  }


  private void refreshStartId(StartEndId startEndId) {
    startEndId.setStartId(startEndId.getStartId() + keChuanTotalAmountDataGetLimit);
  }


  private void removeOrder(ModifyPlatformExistsDO modifyPlatformExistsDO) {
    String orderNo = modifyPlatformExistsDO.getBusinessNo();
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<OfflineOrderDO> query = new LambdaQueryWrapper<>();
      query.eq(OfflineOrderDO::getOrderNo, orderNo);
      OfflineOrderDO offlineOrderDO = offlineOrderMapper.selectOne(query);
      if (Objects.isNull(offlineOrderDO)) {
        return; // 查不到,就是被清除了,不用处理
      }

      // 如果不是迁移订单,则不处理
      if (!Boolean.TRUE.toString().equals(offlineOrderDO.getMigration())) {
        return;
      }

      offlineOrderRepository.deletedOfflineOrder(offlineOrderDO, DELETED_WRONG_PLATFORM_CODE,
          Boolean.TRUE);
    }
  }


  private void removeRefund(ModifyPlatformExistsDO modifyPlatformExistsDO) {
    String refundNo = modifyPlatformExistsDO.getBusinessNo();

    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      LambdaQueryWrapper<OfflineRefundOrderDO> query = new LambdaQueryWrapper<>();
      query.eq(OfflineRefundOrderDO::getRefundNo, refundNo);
      OfflineRefundOrderDO offlineRefundOrderDO = offlineRefundOrderMapper.selectOne(query);
      if (Objects.isNull(offlineRefundOrderDO)) {
        return; // 查不到,就是被清除了,不用处理
      }

      // 如果不是迁移订单,则不处理
      if (!Boolean.TRUE.toString().equals(offlineRefundOrderDO.getMigration())) {
        return;
      }

      offlineOrderRepository.deletedOfflineRefundOrder(offlineRefundOrderDO,
          DELETED_WRONG_PLATFORM_CODE, Boolean.TRUE);
    }
  }

}
