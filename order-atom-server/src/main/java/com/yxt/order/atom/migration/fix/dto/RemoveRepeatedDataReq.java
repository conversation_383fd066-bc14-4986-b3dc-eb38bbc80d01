package com.yxt.order.atom.migration.fix.dto;

import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * 移除重复数据请求
 *
 * @author: moatkon
 * @time: 2025/3/11 9:54
 */
@Data
public class RemoveRepeatedDataReq {

  @NotEmpty(message = "shardingValueList can not empty")
  private List<String> shardingValueList;

  @NotEmpty(message = "请输入场景,便于监控")
  private String scene;

  private Long startId;
  private Long endId;

  private Boolean onlyHandleKeChuan = false;

}
