package com.yxt.order.atom.migration.req;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2025/3/14 19:24
 */
@Data
public class FlushAmtAmountReq {

  /**
   * 目标Schema
   */
  @NotEmpty(message = "targetSchema can not empty")
  private String targetSchema;

  /**
   * 起始ID
   */
  @NotNull(message = "startId can not empty")
  private Long startId;


  /**
   * 结束ID
   */
  @NotNull(message = "endId can not empty")
  private Long endId;


  /**
   * 场景
   */
  @NotEmpty(message = "scene can not empty")
  private String scene;

  public String monitorKey() {
    return String.format("flushAmtReq_%s_%s_%s_%s",scene, targetSchema, startId, endId);
  }

}
