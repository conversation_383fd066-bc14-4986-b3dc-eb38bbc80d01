package com.yxt.order.atom.migration.service;

import com.yxt.order.atom.migration.dao.HanaMigrationDO;
import com.yxt.order.atom.migration.dao.HanaMigrationErrorDO;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.atom.migration.message.MigrationEventReHandleMessage;

/**
 * 入库会去重,
 *
 * <AUTHOR> (moatkon)
 * @date 2024年06月24日 17:35
 * @email: <EMAIL>
 */
public interface HanaMigrationService {

  /**
   * 处理错误
   */
  void processingError(HanaMigrationDO hanaMigrationDO, HanaOrderInfo hanaOrderInfo, String message,
      String errorType);

  /**
   * 移除重试成功的数据
   *
   * @param hanaMigrationErrorDO
   */
  void deletedRetrySuccessData(HanaMigrationErrorDO hanaMigrationErrorDO);


  void refreshHanaMigration(HanaMigrationDO hanaMigrationDO);

  HanaMigrationDO getHanaMigrationById(Long id);


  void refreshHanaMigrationErrorDO(HanaMigrationErrorDO hanaMigrationErrorDO);

  void entryRetryQueue(HanaMigrationErrorDO hanaMigrationErrorDO);


  void reHandleMigrationErrorData(MigrationEventReHandleMessage message);

}
