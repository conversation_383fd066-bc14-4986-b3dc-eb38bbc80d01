package com.yxt.order.atom.migration.service;

import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.migration.MigrationEventMessageConsumer;
import com.yxt.order.atom.migration.dao.HanaMigrationDO;
import com.yxt.order.atom.migration.dao.HanaMigrationDOMapper;
import com.yxt.order.atom.migration.dao.HanaMigrationErrorDO;
import com.yxt.order.atom.migration.dao.HanaMigrationErrorDOMapper;
import com.yxt.order.atom.migration.dao.HanaMigrationMapper;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.atom.migration.message.MigrationEventReHandleMessage;
import com.yxt.order.common.utils.OrderJsonUtils;
import java.util.Date;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年06月24日 17:36
 * @email: <EMAIL>
 */
@Slf4j
@Service
public class HanaMigrationServiceImpl implements HanaMigrationService {


  @Value("${hanaMigrationServiceLogError:false}")
  private Boolean hanaMigrationServiceLogError;

  @Resource
  private HanaMigrationDOMapper hanaMigrationDOMapper;

  @Resource
  private HanaMigrationErrorDOMapper hanaMigrationErrorDOMapper;

  @Resource
  private MigrationEventMessageConsumer migrationEventMessageConsumer;

  @Resource
  private HanaMigrationMapper hanaMigrationMapper;
  public enum SubStateEnum {
    KEEP_NEXT_BATCH_TO_MIGRATION
  }

  public enum StateEnum {
    /**
     * 保持原样,不处理。以下场景会不处理:
     * 1. 迁移的业务时间是按照xf_txdate来的,数据范围扫描是按照xf_createtime来的。如果xf_createtime在范围内,但是xf_txdate不在范围内,则不处理。会添加备注
     */
    KEEP(0),


    /**
     * 消息已被正确处理并入库
     */
    SUCCESS(1),

    /**
     * 消息已被正常处理,但是数据存在
     */
    EXISTS(2),

    /**
     * 消息已被正常处理,只是是线上订单,不处理
     */
    ONLINE_ORDER(3),

    /**
     * 订单没有明细
     */
    WITHOUT_DETAIL(4),

    /**
     * 找不到会员,再存
     */
    STAGE_FOR_MEMBER_404(200),

    /**
     * 退单找不到正单
     */
    STAGE_FOR_ORDER_404(201);

    @Getter
    private final Integer value;

    StateEnum(Integer value) {
      this.value = value;
    }
  }


  public static String buildCountKey(String key, StateEnum state) {
    return String.format("%s_%s", key, state);
  }

  @Override
  public void processingError(HanaMigrationDO hanaMigrationDO, HanaOrderInfo hanaOrderInfo,
      String message, String errorType) {
    String targetSchema = hanaMigrationDO.getTargetSchema();

    HanaMigrationErrorDO hanaMigrationErrorDO = new HanaMigrationErrorDO();
    hanaMigrationErrorDO.setHanaMigrationId(hanaMigrationDO.getId());
    hanaMigrationErrorDO.setTargetSchema(targetSchema);
    hanaMigrationErrorDO.setConditionJson(OrderJsonUtils.toJson(hanaOrderInfo));// 记录当前失败的订单
    hanaMigrationErrorDO.setEntryRetryQueue(Boolean.FALSE.toString());
    hanaMigrationErrorDO.setErrorMessage(message);
    hanaMigrationErrorDO.setErrorType(errorType);
    hanaMigrationErrorDO.setCreatedTime(new Date());

    hanaMigrationErrorDOMapper.insert(hanaMigrationErrorDO);

  }



  @Override
  public void deletedRetrySuccessData(HanaMigrationErrorDO hanaMigrationErrorDO) {
    check(hanaMigrationErrorDO);
    // 删除掉处理成功的
    hanaMigrationErrorDOMapper.deleteById(hanaMigrationErrorDO.getId());
  }

  @Override
  public void refreshHanaMigration(HanaMigrationDO hanaMigrationDO) {
    check(hanaMigrationDO);
    hanaMigrationDOMapper.updateById(hanaMigrationDO);
  }

  private static void check(HanaMigrationDO hanaMigrationDO) {
    if (Objects.isNull(hanaMigrationDO.getId())) {
      throw new RuntimeException("id can not null");
    }
  }

  private static void check(HanaMigrationErrorDO hanaMigrationErrorDO) {
    if (Objects.isNull(hanaMigrationErrorDO.getId())) {
      throw new RuntimeException("id can not null");
    }
  }


  @Override
  public HanaMigrationDO getHanaMigrationById(Long id) {
    HanaMigrationDO hanaMigrationDO = hanaMigrationDOMapper.selectById(id);
    if (Objects.isNull(hanaMigrationDO)) {
      throw new RuntimeException(String.format("%s 对应的数据不存在", id));
    }
    return hanaMigrationDO;
  }



  @Override
  public void refreshHanaMigrationErrorDO(HanaMigrationErrorDO hanaMigrationErrorDO) {
    check(hanaMigrationErrorDO);
    hanaMigrationErrorDOMapper.updateById(hanaMigrationErrorDO);
  }

  @Override
  public void entryRetryQueue(HanaMigrationErrorDO hanaMigrationErrorDO) {
    check(hanaMigrationErrorDO);
    hanaMigrationErrorDO.setEntryRetryQueue(Boolean.TRUE.toString());
    refreshHanaMigrationErrorDO(hanaMigrationErrorDO);
  }

  @Override
  public void reHandleMigrationErrorData(MigrationEventReHandleMessage message) {
    HanaMigrationErrorDO hanaMigrationErrorDO = null;
    try {
      hanaMigrationErrorDO = message.getHanaMigrationErrorDO();

      String condition = hanaMigrationErrorDO.getConditionJson();
      HanaOrderInfo hanaOrderInfo = JsonUtils.toObject(condition, HanaOrderInfo.class);

      // 获取迁移任务
      HanaMigrationDO hanaMigrationDO = this.getHanaMigrationById(
          hanaMigrationErrorDO.getHanaMigrationId());

      HanaOrderInfo current = hanaMigrationMapper.queryMigrationOrderById(
          hanaOrderInfo.getId(), hanaMigrationErrorDO.getTargetSchema());
      if(Objects.isNull(current)){
        return;
      }

      // 不等于0就说明已经处理过了,可以删除
      if (current.getMigration() != 0) {
        this.deletedRetrySuccessData(hanaMigrationErrorDO);
        return;
      }

      hanaOrderInfo.setOrderId(current.getOrderId()); // 历史消息无orderId,需要这里覆盖
      hanaOrderInfo.setOtherOrderId(current.getOtherOrderId()); // 历史消息无orderId,需要这里覆盖

      migrationEventMessageConsumer.one2one(hanaOrderInfo, hanaMigrationErrorDO.getTargetSchema(),
          hanaMigrationDO.taskKey(), Boolean.TRUE,hanaMigrationDO);

      this.deletedRetrySuccessData(hanaMigrationErrorDO);

    } catch (Exception e) {
      if(hanaMigrationServiceLogError){
        log.error("补偿失败"+JsonUtils.toJson(message), e);
      }
      assert hanaMigrationErrorDO != null;
      hanaMigrationErrorDO.setErrorMessage(StringUtils.isEmpty(e.getMessage())?"异常信息为空,请打开开关看日志":e.getMessage());
      this.refreshHanaMigrationErrorDO(hanaMigrationErrorDO);
    }
  }
}
