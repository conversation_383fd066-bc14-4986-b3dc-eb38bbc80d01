package com.yxt.order.atom.migration.service;

import static com.yxt.order.atom.order.repository.impl.OfflineOrderRepositoryImpl.buildOrderExistsQuery;
import static com.yxt.order.atom.order.repository.impl.OfflineOrderRepositoryImpl.buildRefundOrderExistsQuery;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.YxtOrderSharding;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDTO;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderExistsReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderExistsReqDto;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 海典订单是否存在检查-主要是使用orderId检查
 *
 * @author: moatkon
 * @time: 2025/3/26 10:20
 */
@Component
public class HdOrderExistCheckComponent {

  @Resource
  private OfflineOrderMapper offlineOrderMapper;
  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  /**
   * 海典正单是否存在检查
   *
   * @param hanaOrderInfo
   * @param migrationOrder
   * @return
   */
  public Boolean hdOfflineOrderIsExists(HanaOrderInfo hanaOrderInfo, OfflineOrderDO migrationOrder) {
    if(!ThirdPlatformCodeEnum.HAIDIAN.name().equals(migrationOrder.getThirdPlatformCode())){
      return Boolean.FALSE;
    }

    String idStr = String.valueOf(hanaOrderInfo.getOtherOrderId());
    if (idStr.length() != 16) { // 海典的单号固定是16位
      return Boolean.FALSE;
    }

    String hdThirdOrderNo = String.valueOf(hanaOrderInfo.getOtherOrderId()); // 之前迁移的是XF_DOCNO,没有取XF_TXSERIAL
                                                                             // 所以拿正确的看看海典之前有无同步过来,如果有
                                                                             // 就说明钱的是重复的，需要删除

    // 1. 检查是否有多条
    OfflineOrderExistsReqDto existReq = new OfflineOrderExistsReqDto();
    existReq.setStoreCode(migrationOrder.getStoreCode());
    existReq.setThirdOrderNo(hdThirdOrderNo); // 通过这个单号看是否有重复
    existReq.setThirdPlatformCode(migrationOrder.getThirdPlatformCode());
    existReq.setThirdCreated(migrationOrder.getCreated());
    LambdaQueryWrapper<OfflineOrderDO> existCount = buildOrderExistsQuery(existReq); //
    return offlineOrderMapper.selectCount(existCount) == 1;//能查到,说明是重复的,需要删除
  }

  /**
   * 这个是给迁移脚本使用的,如果有就是重复的
   * @param hanaOrderInfo
   * @param offlineOrderDTO
   * @return
   */
  public OfflineOrderDO hdOfflineOrderDtoIsExists(HanaOrderInfo hanaOrderInfo, OfflineOrderDTO offlineOrderDTO) {
    if(!ThirdPlatformCodeEnum.HAIDIAN.name().equals(offlineOrderDTO.getThirdPlatformCode())){
      return null;
    }

    String idStr = String.valueOf(hanaOrderInfo.getOtherOrderId());
    if (idStr.length() != 16) { // 海典的单号固定是16位
      return null;
    }

    String hdThirdOrderNo = String.valueOf(hanaOrderInfo.getOtherOrderId());

    // 1. 检查是否有多条
    OfflineOrderExistsReqDto existReq = new OfflineOrderExistsReqDto();
    existReq.setStoreCode(offlineOrderDTO.getStoreCode());
    existReq.setThirdOrderNo(hdThirdOrderNo); // 通过这个单号看是否有重复
    existReq.setThirdPlatformCode(offlineOrderDTO.getThirdPlatformCode());
    existReq.setThirdCreated(offlineOrderDTO.getCreated());
    LambdaQueryWrapper<OfflineOrderDO> existCount = buildOrderExistsQuery(existReq);
    return offlineOrderMapper.selectOne(existCount);
  }


  /**
   * 海典退单是否存在检查
   *
   * @param hanaOrderInfo
   * @param offlineRefundOrderDO
   * @return
   */
  public Boolean hdOfflineRefundOrderIsExists(HanaOrderInfo hanaOrderInfo,
      OfflineRefundOrderDO offlineRefundOrderDO) {
    if(!ThirdPlatformCodeEnum.HAIDIAN.name().equals(offlineRefundOrderDO.getThirdPlatformCode())){
      return Boolean.FALSE;
    }

    String idStr = String.valueOf(hanaOrderInfo.getOtherOrderId());
    if (idStr.length() != 16) { // 海典的单号固定是16位
      return Boolean.FALSE;
    }


    String hdThirdRefundNo = String.valueOf(hanaOrderInfo.getOtherOrderId());

    OfflineRefundOrderExistsReqDto existsReq = new OfflineRefundOrderExistsReqDto();
    existsReq.setStoreCode(offlineRefundOrderDO.getStoreCode());
    existsReq.setThirdRefundNo(hdThirdRefundNo); // 使用这个单号来处理
    existsReq.setThirdPlatformCode(offlineRefundOrderDO.getThirdPlatformCode());
    existsReq.setThirdCreated(offlineRefundOrderDO.getCreated());
    LambdaQueryWrapper<OfflineRefundOrderDO> existsCount = buildRefundOrderExistsQuery(existsReq);
    return offlineRefundOrderMapper.selectCount(existsCount) == 1;
  }

  /**
   * 这个是给迁移脚本使用的,如果有就是重复的
   * @param hanaOrderInfo
   * @param offlineRefundOrderDTO
   * @return
   */
  public OfflineRefundOrderDO hdOfflineRefundOrderDtoIsExists(HanaOrderInfo hanaOrderInfo,
      OfflineRefundOrderDTO offlineRefundOrderDTO) {
    if(!ThirdPlatformCodeEnum.HAIDIAN.name().equals(offlineRefundOrderDTO.getThirdPlatformCode())){
      return null;
    }

    String idStr = String.valueOf(hanaOrderInfo.getOtherOrderId());
    if (idStr.length() != 16) { // 海典的单号固定是16位
      return null;
    }

    String hdThirdRefundNo = String.valueOf(hanaOrderInfo.getOtherOrderId());

    OfflineRefundOrderExistsReqDto existsReq = new OfflineRefundOrderExistsReqDto();
    existsReq.setStoreCode(offlineRefundOrderDTO.getStoreCode());
    existsReq.setThirdRefundNo(hdThirdRefundNo); // 使用这个单号来处理
    existsReq.setThirdPlatformCode(offlineRefundOrderDTO.getThirdPlatformCode());
    existsReq.setThirdCreated(offlineRefundOrderDTO.getCreated());
    LambdaQueryWrapper<OfflineRefundOrderDO> existsCount = buildRefundOrderExistsQuery(existsReq);
    return offlineRefundOrderMapper.selectOne(existsCount);
  }


  /**
   * warp
   * @param hanaOrderInfo
   * @param offlineOrderDTO
   * @return
   */
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  @YxtOrderSharding(shardingNo = "#offlineOrderDTO.orderNo")
  public OfflineOrderDO hdOfflineOrderDtoIsExistsWarp(HanaOrderInfo hanaOrderInfo, OfflineOrderDTO offlineOrderDTO) {
    return hdOfflineOrderDtoIsExists(hanaOrderInfo,offlineOrderDTO);
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  @YxtOrderSharding(shardingNo = "#offlineRefundOrderDTO.refundNo")
  public OfflineRefundOrderDO hdOfflineRefundOrderDtoIsExistsWarp(HanaOrderInfo hanaOrderInfo, OfflineRefundOrderDTO offlineRefundOrderDTO) {
    return hdOfflineRefundOrderDtoIsExists(hanaOrderInfo,offlineRefundOrderDTO);
  }
}
