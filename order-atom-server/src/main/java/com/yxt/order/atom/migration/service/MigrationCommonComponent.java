package com.yxt.order.atom.migration.service;

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.util.JsonUtils;
import com.yxt.middle.member.api.MemberInfoApi;
import com.yxt.middle.member.res.member.MemberInfoVo;
import com.yxt.order.atom.common.RedisConstant;
import com.yxt.order.atom.common.utils.RedisStringUtil;
import com.yxt.order.atom.migration.dao.HanaGoods;
import com.yxt.order.atom.migration.dao.HanaHrmResource;
import com.yxt.order.atom.migration.dao.HanaMigrationMapper;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.atom.migration.dao.HanaOrderItem;
import com.yxt.order.atom.migration.dao.HanaOrderPay;
import com.yxt.order.atom.migration.dao.HanaStore;
import com.yxt.order.atom.migration.dao.HanaUser;
import com.yxt.order.atom.migration.dao.dto.QueryErpGoodsDto;
import com.yxt.order.atom.migration.dao.dto.QueryHrcResourceDto;
import com.yxt.order.atom.migration.dao.dto.QueryOrderItem;
import com.yxt.order.atom.migration.dao.dto.QueryOrganizationInfo;
import com.yxt.order.atom.migration.dao.dto.QueryPayInfo;
import com.yxt.order.atom.migration.feign.GoodsApiFeign;
import com.yxt.order.atom.migration.feign.MiddleIdClient;
import com.yxt.order.atom.migration.feign.mdm.MdmService;
import com.yxt.order.atom.migration.service.dto.CommoditySourceDTO;
import com.yxt.order.atom.migration.service.dto.CommoditySourceInfoReq;
import com.yxt.order.atom.migration.service.dto.CommoditySourceInfoResp;
import com.yxt.order.atom.migration.service.dto.GoodsInfo;
import com.yxt.order.common.Utils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年06月12日 9:46
 * @email: <EMAIL>
 */
@Component
@Slf4j
public class MigrationCommonComponent {
  @Value("${migrationCacheDays:3}")
  private Long migrationCacheDays; // 迁移缓存天数

  @Value("${migration-split-company:YNHX_DATA01}")
  private String migrationSplitCompany;

  @Resource
  private MdmService mdmService;

  @Resource
  private GoodsApiFeign goodsApiFeign;


  @Resource
  private HanaMigrationMapper hanaMigrationMapper;
  @Resource
  private MiddleIdClient middleIdClient;

  @Resource
  private MemberInfoApi memberInfoApi;

  /**
   * 生成ID
   *
   * @return
   */
  public String generateId() {
    Long id = middleIdClient.getId(1).get(0);
    return String.valueOf(id);
  }

  public List<HanaOrderItem> getHanaOrderItems(HanaOrderInfo hanaOrderInfo,
      String schema) {
    QueryOrderItem queryOrderItem = new QueryOrderItem();
    queryOrderItem.setThirdOrderNo(hanaOrderInfo.getThirdOrderNo());
    queryOrderItem.setStoreCode(hanaOrderInfo.getStoreCode());
    queryOrderItem.setSchema(schema);

    queryOrderItem.setTxDate(hanaOrderInfo.getTxDate());
    queryOrderItem.setPosCashierDeskNo(hanaOrderInfo.getPosCashierDeskNo());
    List<HanaOrderItem> hanaOrderItemList = hanaMigrationMapper.queryOrderItem(queryOrderItem);
    if (CollectionUtils.isEmpty(hanaOrderItemList)) {
      log.warn("订单:{},storeCode:{}, 明细信息为空", hanaOrderInfo.getThirdOrderNo(),
          hanaOrderInfo.getStoreCode());
    }
    return hanaOrderItemList;
  }

  public List<HanaOrderPay> getHanaOrderPays(String thirdOrderNo, String storeCode, String schema,List<Long> orderIdList) {
    QueryPayInfo queryPayInfo = new QueryPayInfo();
    queryPayInfo.setThirdOrderNo(thirdOrderNo);
    queryPayInfo.setStoreCode(storeCode);
    queryPayInfo.setSchema(schema);
    // 1. 直接查支付表
    List<HanaOrderPay> hanaOrderPays = hanaMigrationMapper.queryPayInfo(queryPayInfo);
    if (!CollectionUtils.isEmpty(hanaOrderPays)) {
     return hanaOrderPays;
    }

    // 2. 支付表没有数据,再通过orderId查大数据同步过来的支付信息
    for (Long orderId : orderIdList) {
      hanaOrderPays = hanaMigrationMapper.queryHdPayInfo(String.valueOf(orderId), storeCode);
      if (!CollectionUtils.isEmpty(hanaOrderPays)) {
        return hanaOrderPays;
      }
    }

    // 3. 在判断单号是否S0开头的单号,S0开头的单号可以忽略.已经和赵兴堂确认
    if(thirdOrderNo.startsWith("S0")){
      return Lists.newArrayList();
    }

    throw new RuntimeException(
        String.format("支付信息为空,%s,%s,%s", thirdOrderNo, storeCode, schema));

  }


  public BigDecimal calcTotalDiscount(HanaOrderItem hanaOrderItem) {
    return hanaOrderItem.getMarkdownAmt()
        .add(hanaOrderItem.getDiscountAmt()).add(hanaOrderItem.getPromotionAmt());
  }


  public BigDecimal calcPrice(HanaOrderItem hanaOrderItem) {
    // RoundingMode.DOWN 的行为可以总结为“直接截掉多余的小数部分”，不进行四舍五入
    // 和产品沟通后结果
    try {

      // 和赵兴堂沟通,属于正常数据
      if(hanaOrderItem.getTotalAmount().compareTo(BigDecimal.ZERO) == 0
          && hanaOrderItem.getCommodityCount().compareTo(BigDecimal.ZERO) ==0){
        return BigDecimal.ZERO;
      }

      return hanaOrderItem.getTotalAmount()
          .divide(hanaOrderItem.getCommodityCount(), 6, RoundingMode.DOWN);
    } catch (Exception e) {
      throw new RuntimeException(
          String.format("calcPrice error,%s,data:%s", e.getMessage(),
              JsonUtils.toJson(hanaOrderItem)));
    }
  }

  public Date getTxDateAndTime(HanaOrderInfo hanaOrderInfo) {
    String date = hanaOrderInfo.getTxDate();
    String time = hanaOrderInfo.getTxTime();
    try {
      DateTimeFormatter dataFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
      LocalDate localDateTime = LocalDate.parse(date, dataFormatter);
      int year = localDateTime.getYear();
      int month = localDateTime.getMonthValue();
      int day = localDateTime.getDayOfMonth();

      String hour = time.substring(0, 2);
      String minute = time.substring(2, 4);
      String second = time.substring(4, 6);

      // yyyy-MM-dd'T'HH:mm:ss.SSS'Z'
      SimpleDateFormat df1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
      String dateStr = String.format("%s-%s-%s %s:%s:%s", year, month, day, hour, minute, second);

      return df1.parse(dateStr);
    } catch (Exception e) {
      throw new RuntimeException(String.format("时间解析失败 %s,%s", date, time));
    }
  }


  public HanaUser buildHanaUserInfo(String clientCode) {
    if (StringUtils.isEmpty(clientCode)) {
      return null;
    }
    String hanaUserJson = RedisStringUtil.getValue(hanaUserKey(clientCode));
    if(StringUtils.isNotEmpty(hanaUserJson)){
      HanaUser hanaUser = JsonUtils.toObject(hanaUserJson, HanaUser.class);
      if(Objects.nonNull(hanaUser)){
        return hanaUser;
      }
    }

    HanaUser hanaUser = new HanaUser();
    hanaUser.setUserCardNo(clientCode);
    // clientCode2HanaUser.put(clientCode, hanaUser); 不缓存

    ResponseBase<MemberInfoVo> responseBase = memberInfoApi.getMemberByCardNo(clientCode);
    if ("E2011".equals(responseBase.getCode()) || "会员不存在".equals(responseBase.getMsg())) {
      log.warn("调用会员接口查不到会员信息:{}", clientCode);
      return hanaUser; //在会员侧查不到则处理处理为非会员,逻辑保持一致
    }
    Utils.checkRespSuccess(responseBase);
    MemberInfoVo data = responseBase.getData();
    if (Objects.isNull(data)) {
      log.info("调用会员接口成功,但是返回数据为null,{},res:{}", clientCode,
          JsonUtils.toJson(responseBase));
      return hanaUser; // 和测试沟通后,确认在会员侧查不到会员,就当做非会员
    }

    hanaUser.setHanaUserId(data.getOldMemberId());
    hanaUser.setUserName(data.getMemberName());
    hanaUser.setUserMobile(data.getMemberPhone());
    hanaUser.setMemberUserId(data.getUserId());
//    RedisStringUtil.setValue(hanaUserKey(clientCode),JsonUtils.toJson(hanaUser),migrationCacheDays,TimeUnit.DAYS);
    return hanaUser;
  }

  /**
   * 单个的
   *
   * @param hanaUserId
   * @return
   */
  @Deprecated
  public Long getUserId(String hanaUserId) {
    Map<String, Long> hanaUserId2UserIdMap = memberInfoApi.batchGetUserIdByCrmIds(
        Lists.newArrayList(hanaUserId));

    if (Objects.isNull(hanaUserId2UserIdMap)) {
      return null; // 归为非会员单
    }

    return hanaUserId2UserIdMap.get(hanaUserId);
  }

  public String getErpNameByErpCode(String erpCode) {
    String erpNameCache = RedisStringUtil.getValue(erpCodeNameKey(erpCode));
    if (StringUtils.isNotEmpty(erpNameCache)) {
      return erpNameCache;
    }

    // 1. 先查hana库
    QueryErpGoodsDto queryErpGoodsDto = new QueryErpGoodsDto();
    queryErpGoodsDto.setErpCode(erpCode);
    HanaGoods hanaGoods = hanaMigrationMapper.queryErpName(queryErpGoodsDto);
    if (Objects.nonNull(hanaGoods)) {
      return hanaGoods.getErpName();
    }

    // 2. 再查商品接口
    ResponseBase<CommoditySourceDTO> res = goodsApiFeign.getByErpCode(erpCode);
    if (res.checkSuccess() && Objects.nonNull(res.getData())) {
      return res.getData().getName();
    }

    // 3. 查不到默认为空,已经和产品沟通确认
    return Strings.EMPTY;
  }

  private String buildMigrationCommonKey(MigrationKey migrationKey, String value) {
    return String.format("hanaMigration:migrationKey:%s:%s", migrationKey.name(), value);
  }

  private String loginIdKey(String value){
    return buildMigrationCommonKey(MigrationKey.LOGIN_ID,value);
  }

  private String erpCodeNameKey(String value){
    return buildMigrationCommonKey(MigrationKey.ERP_CODE_NAME,value);
  }
  private String hanaUserKey(String value){
    return buildMigrationCommonKey(MigrationKey.HANA_USER,value);
  }

  private String hanaStore(String value){
    return buildMigrationCommonKey(MigrationKey.HANA_STORE,value);
  }


  public enum MigrationKey {
    LOGIN_ID,ERP_CODE_NAME,HANA_USER,HANA_STORE
  }

  /**
   * 根据Id获取人名
   *
   * @param loginId
   * @return
   */
  public String getHrmResourceName(String loginId) {
    // 1000,或者不是7位工号的,咨询了赵老师是“门店销售的时候没有输入正确的销售人员工号”。我们这边的处理方式是,saler_id照常记录,只是saler_name因为不是标准的工号,直接置位空
    if (StringUtils.isEmpty(loginId)) {
      return Strings.EMPTY;
    }
    String nameCache = RedisStringUtil.getValue(loginIdKey(loginId));
    if (StringUtils.isNotEmpty(nameCache)) {
      return nameCache;
    }

    QueryHrcResourceDto dto = new QueryHrcResourceDto();
    dto.setLoginId(loginId);
    HanaHrmResource hanaHrmResource = hanaMigrationMapper.queryByLoginId(dto);
    if(Objects.nonNull(hanaHrmResource)){
      return hanaHrmResource.getName();
    }

    if(loginId.length() != 7){
      return Strings.EMPTY;
    }

    String empNameFromMdm = mdmService.queryByEmpId(loginId);
    if(StringUtils.isNotEmpty(empNameFromMdm)){
      return empNameFromMdm;
    }

    //          throw new RuntimeException("查不到人" + loginId);
    return Strings.EMPTY; // 找不到人产品说可以忽略
  }

  /**
   * XF_ZT_GIFTGIVING_MEMOMAP: XF_DOCNO是原单，XF_GEDOCNO是赠品单
   * <p>
   * xf_dbm_coupon: XF_USEDDOCNO是原单 XF_DOCNO 是返利订单
   * <p>
   * 规则: 1. 只有正单有拆单场景
   * <p>
   * 2. 主单下只有一个返利订单或一个赠品单，不会存在一对多的情况
   */
  public String fetchOutParentThirdOrderNo(String thirdOrderNo, String schema, String storeCode) {
    String parentOrderNo = Strings.EMPTY;
    if (StringUtils.isEmpty(migrationSplitCompany)) {
      return parentOrderNo;
    }

    String[] splitCompany = migrationSplitCompany.split(",");

    for (String configSchema : splitCompany) {
      if (configSchema.equalsIgnoreCase(schema)) {
        parentOrderNo = hanaMigrationMapper.queryParentOrderNoFromDbmCoupon(thirdOrderNo, schema,
            storeCode);
        if (!StringUtils.isEmpty(parentOrderNo)) {
          return parentOrderNo;
        }

        return hanaMigrationMapper.queryParentOrderNoFromXfZtGiftgivingMemoMap(thirdOrderNo,
            schema, storeCode);
      }
    }

    return parentOrderNo;
  }


  public Map<String, GoodsInfo> getGoodsInfo(Set<String> erpCodeSet) {
    Map<String, GoodsInfo> goodsInfoMap = Maps.newHashMap();
    try {
      if (CollectionUtils.isEmpty(erpCodeSet)) {
        return goodsInfoMap;
      }

      List<CommoditySourceInfoResp> listAll = Lists.newArrayList();

      // 对副本进行迭代
      HashSet<String> erpCodeSetCopy = Sets.newHashSet(erpCodeSet);
      Iterator<String> iterator = erpCodeSetCopy.iterator();
      while (iterator.hasNext()){
        String erpCode = iterator.next();
        String cache = RedisStringUtil.getValue(RedisConstant.getCacheGoodsInfoKey(erpCode));
        if(StringUtils.isNotEmpty(cache)){
          listAll.add(JsonUtils.toObject(cache,CommoditySourceInfoResp.class)); //从缓存中获取
          iterator.remove(); // 移除,不在请求商品服务
        }
      }

      // 商品接口限流个50个
      for (List<String> batchErpCodeList : Lists.partition(Lists.newArrayList(erpCodeSetCopy), 48)) {
        CommoditySourceInfoReq req = new CommoditySourceInfoReq();
        req.setErpCodes(batchErpCodeList);
        ResponseBase<List<CommoditySourceInfoResp>> res = goodsApiFeign.getGoodsInfo(req);
        if (res.checkSuccess()) {
          List<CommoditySourceInfoResp> list = res.getData();
          list.forEach(item -> {
            RedisStringUtil.setValue(RedisConstant.getCacheGoodsInfoKey(item.getErpCode()),JsonUtils.toJson(item),1L,
                TimeUnit.HOURS);
            listAll.add(item);
          });
        } else {
          throw new RuntimeException(String.format("商品返回失败,%s", JsonUtils.toJson(res)));
        }
      }

      // 统一转成map
      listAll.forEach(item -> goodsInfoMap.put(item.getErpCode(),
          BeanUtil.copyProperties(item, GoodsInfo.class)));

      return goodsInfoMap;
    } catch (Exception e) {
      log.warn("调用商品服务失败,不影响,主流程,入参:{}", erpCodeSet, e);
      return goodsInfoMap;
    }
  }

//  public static void main(String[] args) {
//    double number = 1.124100500027512e15;
//// 使用 BigDecimal 避免精度丢失
//    BigDecimal bigDecimal = new BigDecimal(number).setScale(0, RoundingMode.DOWN);
//    String result = bigDecimal.toString();  // 输出 "1124041900031246"
//    System.out.println(result);
//  }


  public HanaStore queryStoreInfo(String storeCode) {
    String key = hanaStore(storeCode);
    String redisHanaStoreJson = RedisStringUtil.getValue(key);
    if(StringUtils.isNotEmpty(redisHanaStoreJson)){
      return JsonUtils.toObject(redisHanaStoreJson,HanaStore.class);
    }

    QueryOrganizationInfo queryOrganizationInfo = new QueryOrganizationInfo();
    queryOrganizationInfo.setStoreCode(storeCode);
    HanaStore hanaStore = hanaMigrationMapper.queryOrganizationInfo(queryOrganizationInfo);
    if (Objects.isNull(hanaStore)) {
      hanaStore = mdmService.queryOrgInfo(storeCode);
      if (Objects.isNull(hanaStore)) {
        throw new RuntimeException(String.format("店铺信息为空,%s", storeCode));
      }
    }
//    RedisStringUtil.setValue(key,JsonUtils.toJson(hanaStore),migrationCacheDays,TimeUnit.DAYS);
    return hanaStore;
  }
}
