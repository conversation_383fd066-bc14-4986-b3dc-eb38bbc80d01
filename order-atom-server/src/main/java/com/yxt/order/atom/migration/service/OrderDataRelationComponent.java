package com.yxt.order.atom.migration.service;

import static com.yxt.order.atom.migration.constant.MigrationConstant.RELATION_TYPE_ORDER;
import static com.yxt.order.atom.migration.constant.MigrationConstant.RELATION_TYPE_REFUND_ORDER;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.migration.dao.HanaOrderInfo;
import com.yxt.order.atom.migration.dao.OrderDataRelationHanaDO;
import com.yxt.order.atom.migration.dao.OrderDataRelationHanaDOMapper;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2025/3/26 9:51
 */
@Component
public class OrderDataRelationComponent {

  @Resource
  private OrderDataRelationHanaDOMapper orderDataRelationHanaDOMapper;

  public void orderRelation(String orderNo,String targetSchema,HanaOrderInfo hanaOrderInfo ){
    LambdaQueryWrapper<OrderDataRelationHanaDO> count = new LambdaQueryWrapper<>();
    count.eq(OrderDataRelationHanaDO::getType, RELATION_TYPE_ORDER);
    count.eq(OrderDataRelationHanaDO::getBusinessNo, orderNo);
    saveOrderRelation(orderNo, targetSchema, hanaOrderInfo, count);
  }

  @DS(DATA_SOURCE.ORDER_OFFLINE)
  public void orderRelationForMigration(String orderNo,String targetSchema,HanaOrderInfo hanaOrderInfo ){
    LambdaQueryWrapper<OrderDataRelationHanaDO> count = new LambdaQueryWrapper<>();
    count.eq(OrderDataRelationHanaDO::getType, RELATION_TYPE_ORDER);
    count.eq(OrderDataRelationHanaDO::getTargetSchema, targetSchema);
    count.eq(OrderDataRelationHanaDO::getHanaId, String.valueOf(hanaOrderInfo.getId()));
    saveOrderRelation(orderNo, targetSchema, hanaOrderInfo, count);
  }


  public void refundRelation(String refundNo,String targetSchema, HanaOrderInfo hanaOrderInfo){
    LambdaQueryWrapper<OrderDataRelationHanaDO> count = new LambdaQueryWrapper<>();
    count.eq(OrderDataRelationHanaDO::getType, RELATION_TYPE_REFUND_ORDER);
    count.eq(OrderDataRelationHanaDO::getBusinessNo, refundNo);
    saveRefundRelation(refundNo, targetSchema, hanaOrderInfo, count);
  }

  @DS(DATA_SOURCE.ORDER_OFFLINE)
  public void refundRelationForMigration(String refundNo,String targetSchema, HanaOrderInfo hanaOrderInfo){
    LambdaQueryWrapper<OrderDataRelationHanaDO> count = new LambdaQueryWrapper<>();
    count.eq(OrderDataRelationHanaDO::getType, RELATION_TYPE_REFUND_ORDER);
    count.eq(OrderDataRelationHanaDO::getTargetSchema, targetSchema);
    count.eq(OrderDataRelationHanaDO::getHanaId, String.valueOf(hanaOrderInfo.getId()));
    saveRefundRelation(refundNo, targetSchema, hanaOrderInfo, count);
  }



  private void saveRefundRelation(String refundNo, String targetSchema, HanaOrderInfo hanaOrderInfo,
      LambdaQueryWrapper<OrderDataRelationHanaDO> count) {
    Integer countValue = orderDataRelationHanaDOMapper.selectCount(count);
    if (countValue < 1) {
      OrderDataRelationHanaDO orderDataRelationHanaDO = new OrderDataRelationHanaDO();
      orderDataRelationHanaDO.setType(RELATION_TYPE_REFUND_ORDER);
      orderDataRelationHanaDO.setBusinessNo(refundNo);
      orderDataRelationHanaDO.setTargetSchema(targetSchema);
      orderDataRelationHanaDO.setHanaId(String.valueOf(hanaOrderInfo.getId()));
      orderDataRelationHanaDOMapper.insert(orderDataRelationHanaDO);
    }
  }

  private void saveOrderRelation(String orderNo, String targetSchema, HanaOrderInfo hanaOrderInfo,
      LambdaQueryWrapper<OrderDataRelationHanaDO> count) {
    Integer countValue = orderDataRelationHanaDOMapper.selectCount(count);
    if (countValue < 1) {
      OrderDataRelationHanaDO orderDataRelationHanaDO = new OrderDataRelationHanaDO();
      orderDataRelationHanaDO.setType(RELATION_TYPE_ORDER);
      orderDataRelationHanaDO.setBusinessNo(orderNo);
      orderDataRelationHanaDO.setTargetSchema(targetSchema);
      orderDataRelationHanaDO.setHanaId(String.valueOf(hanaOrderInfo.getId()));
      orderDataRelationHanaDOMapper.insert(orderDataRelationHanaDO);
    }
  }

}
