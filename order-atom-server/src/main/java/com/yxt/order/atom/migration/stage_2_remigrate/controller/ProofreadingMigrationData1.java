package com.yxt.order.atom.migration.stage_2_remigrate.controller;

import static com.yxt.order.atom.migration.config.ThreadPoolConfig.MIGRATION_MQ_Consumer_POOL;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.common.utils.RedisStringUtil;
import com.yxt.order.atom.migration.req.ProofreadingThirdOrderNoPlatformCodeFixReq;
import com.yxt.order.atom.migration.req.ProofreadingThirdOrderNoPlatformCodeReq;
import com.yxt.order.atom.migration.stage_2_remigrate.component.ProofreadingMigrationDataOfThirdOrderNoWithPlatformCode;
import com.yxt.starter.controller.AbstractController;
import java.util.Date;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class ProofreadingMigrationData1 extends AbstractController {

  @Resource
  private ProofreadingMigrationDataOfThirdOrderNoWithPlatformCode proofreadingMigrationDataOfThirdOrderNoWithPlatformCode;


  @Qualifier(MIGRATION_MQ_Consumer_POOL)
  @Resource
  private ThreadPoolExecutor migrationMqConsumerPool;


  /**
   * 将OrderDataRelationHanaOldDO里面的数据对应的订单数据物理删除
   * <p>
   * 已经完成
   *
   * @return
   */
  @PostMapping("/proofreadingMigrationDataOfThirdOrderNoWithPlatformCode")
  public ResponseBase<Boolean> proofreadingMigrationData(
      @RequestBody @Valid ProofreadingThirdOrderNoPlatformCodeReq req) {
    if (true) {
      return null;
    }

    migrationMqConsumerPool.submit(() -> {
      RedisStringUtil.setValue(
          "proofreadingMigrationDataOfThirdOrderNoWithPlatformCode" + req.getScene(),
          "刷数中" + new Date(), 30L, TimeUnit.DAYS);
      proofreadingMigrationDataOfThirdOrderNoWithPlatformCode.run(req);
      RedisStringUtil.setValue(
          "proofreadingMigrationDataOfThirdOrderNoWithPlatformCode" + req.getScene(),
          "刷数结束" + new Date(), 30L, TimeUnit.DAYS);
    });

    return generateSuccess(Boolean.TRUE);
  }

  @PostMapping("/proofreadingMigrationDataOfThirdOrderNoWithPlatformCode2Fix")
  public ResponseBase<Boolean> proofreadingMigrationDataFix(
      @RequestBody @Valid ProofreadingThirdOrderNoPlatformCodeFixReq req) {
    if (true) {
      return null;
    }

    migrationMqConsumerPool.submit(() -> {
      RedisStringUtil.setValue("proofreadingFix" + req.getScene(), "刷数中" + new Date(), 30L,
          TimeUnit.DAYS);
      proofreadingMigrationDataOfThirdOrderNoWithPlatformCode.fix(req);
      RedisStringUtil.setValue("proofreadingFix" + req.getScene(), "刷数结束" + new Date(), 30L,
          TimeUnit.DAYS);
    });

    return generateSuccess(Boolean.TRUE);
  }

  @PostMapping("/proofreadingMigrationDataOfThirdOrderNoWithPlatformCode2FixExistsOrder")
  public ResponseBase<Boolean> proofreadingMigrationDataFixExistsOrder() {
    if(true){return null;}
    migrationMqConsumerPool.submit(() -> {
      RedisStringUtil.setValue("proofreadingFixExists" , "刷数中" + new Date(), 30L,
          TimeUnit.DAYS);
      proofreadingMigrationDataOfThirdOrderNoWithPlatformCode.fixExistsOrder();
      RedisStringUtil.setValue("proofreadingFixExists", "刷数结束" + new Date(), 30L,
          TimeUnit.DAYS);
    });

    return generateSuccess(Boolean.TRUE);
  }


}
