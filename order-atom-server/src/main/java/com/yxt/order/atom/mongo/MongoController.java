package com.yxt.order.atom.mongo;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.mongo.MongoMqData.MqData;
import com.yxt.order.atom.sdk.mongo.MongoApi;
import com.yxt.order.atom.sdk.mongo.req.MqDataReqDto;
import com.yxt.starter.controller.AbstractController;
import javax.annotation.Resource;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月15日 16:34
 * @email: <EMAIL>
 */
@RestController
public class MongoController extends AbstractController implements MongoApi {

  @Resource
  private MongoTemplate mongoTemplate;

  @Override
  public ResponseBase<Boolean> insert(MqDataReqDto mqDataReqDto) {

    MongoMqData mongoMqData = buildData(mqDataReqDto);

    mongoTemplate.insert(mongoMqData.getMqData(),
        mongoMqData.getCollectionName());
    return generateSuccess(Boolean.TRUE);
  }

  private MongoMqData buildData(MqDataReqDto mqDataReqDto) {

    MongoMqData mongoMqData = new MongoMqData();
    mongoMqData.setMqData(buildMqData(mqDataReqDto.getMqData()));
    mongoMqData.setCollectionName(mqDataReqDto.getCollectionName());
    return mongoMqData;
  }

  private MqData buildMqData(MqDataReqDto.MqData mqData) {
    MqData data = new MqData();
    data.setId(mqData.getId());
    data.setMessage(mqData.getMessage());
    data.setCreateTime(mqData.getCreateTime());
    data.setCreateDateTime(mqData.getCreateDateTime());
    return data;
  }
}
