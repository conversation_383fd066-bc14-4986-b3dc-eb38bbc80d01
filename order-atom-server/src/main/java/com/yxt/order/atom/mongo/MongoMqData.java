package com.yxt.order.atom.mongo;

import java.util.Date;
import lombok.Data;
import org.springframework.data.annotation.Id;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年06月05日 11:00
 * @email: <EMAIL>
 */
@Data
public class MongoMqData {

  private MqData mqData;

  private String collectionName;

  @Data
  public static class MqData {
    @Id
    private String id;  // 使用 ObjectId 类型

    private String message;
    // 存储消息自身的创建时间
    private Long createTime = System.currentTimeMillis() / 1000;

    private Date createDateTime;
  }

}
