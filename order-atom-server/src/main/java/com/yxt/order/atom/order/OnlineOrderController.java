package com.yxt.order.atom.order;


import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.repository.OrderRepository;
import com.yxt.order.atom.sdk.common.data.OrderBusinessConsumerMessageDTO;
import com.yxt.order.atom.sdk.online_order.order_info.OrderAtomCmdApi;
import com.yxt.order.atom.sdk.online_order.order_info.OrderAtomQryApi;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.OrderInfoQryByScaleBatchReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.OrderInfoQryByScaleReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.OrderInfoQryReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QueryOrderBusinessConsumerMessageReq;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QueryOrderDetailByThirdReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QueryOrderDetailReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QuerySimpleOrderReqByThirdDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.QuerySimpleOrderReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.SaveOrderOptionalReq;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.UpdateOrderOptionalReq;
import com.yxt.order.atom.sdk.online_order.order_info.dto.res.FullOrderDtoResDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.res.SimpleOrderInfoResDto;
import com.yxt.starter.controller.AbstractController;
import java.util.List;
import javax.annotation.Resource;
import lombok.EqualsAndHashCode;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年03月01日 14:44
 * @email: <EMAIL>
 */
@EqualsAndHashCode(callSuper = true)
@RestController
public class OnlineOrderController extends AbstractController implements OrderAtomCmdApi,
    OrderAtomQryApi {

  @Resource
  private OrderRepository orderRepository;

  @Override
  public ResponseBase<FullOrderDtoResDto> queryFullOrder(QueryOrderDetailReqDto dto) {
    return generateSuccess(orderRepository.queryFullOrder(dto));
  }

  @Override
  public ResponseBase<FullOrderDtoResDto> queryFullOrderByThirdReq(
      QueryOrderDetailByThirdReqDto dto) {
    return generateSuccess(orderRepository.queryFullOrderByThirdReq(dto));
  }

  @Override
  public ResponseBase<OrderInfoResDto> getOrderInfo(OrderInfoQryReqDto qryReqDto) {
    return generateSuccess(orderRepository.getOrderInfoByOrderNo(qryReqDto));
  }

  @Override
  public ResponseBase<SimpleOrderInfoResDto> queryOrderInfo(QuerySimpleOrderReqByThirdDto dto) {
    return generateSuccess(orderRepository.querySimpleOrderInfo(dto));
  }


  @Override
  public ResponseBase<List<OrderBusinessConsumerMessageDTO>> queryOrderBusinessConsumerMessageList(
      QueryOrderBusinessConsumerMessageReq dto) {
    return generateSuccess(orderRepository.queryOrderBusinessConsumerMessageList(dto));
  }

  @Override
  public ResponseBase<SimpleOrderInfoResDto> queryOrderInfo(QuerySimpleOrderReqDto dto) {
    return generateSuccess(orderRepository.querySimpleOrderInfo(dto));
  }

  @Override
  public ResponseBase<Boolean> saveOptional(SaveOrderOptionalReq req) {
    return generateSuccess(orderRepository.saveOptional(req));
  }

  @Override
  public ResponseBase<Boolean> updateOptional(UpdateOrderOptionalReq req) {
    return generateSuccess(orderRepository.updateOptional(req));
  }

  @Override
  public ResponseBase<FullOrderDtoResDto> getOrderInfoByScale(OrderInfoQryByScaleReqDto request) {
    return generateSuccess(orderRepository.getOrderInfoByScale(request));
  }

  @Override
  public ResponseBase<List<FullOrderDtoResDto>> getOrderInfoBatchByScale(OrderInfoQryByScaleBatchReqDto request) {
    return generateSuccess(orderRepository.getOrderInfoBatchByScale(request));
  }
}
