package com.yxt.order.atom.order;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.repair.problem_data_get.ChaiLingGoodsAmountErrorDataGet;
import com.yxt.order.atom.repair.problem_data_get.ChaiLingGoodsAmountRefundErrorDataGet;
import com.yxt.order.atom.repair.problem_data_get.HaiDianOnlineOrderAsOfflineOrderGet;
import com.yxt.order.atom.repair.problem_data_get.HaiDianOnlineRefundOrderAsOfflineOrderGet;
import com.yxt.order.atom.sdk.offline_order.RepairApi;
import com.yxt.order.atom.sdk.offline_order.dto.repair.RepairOfflineOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.dto.repair.RepairOfflineRefundOrderReqDto;
import com.yxt.order.common.utils.OrderJsonUtils;
import com.yxt.starter.controller.AbstractController;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年04月01日 14:35
 * @email: <EMAIL>
 */
@RestController
@Slf4j
public class RepairController extends AbstractController implements RepairApi {

  @Resource
  private HaiDianOnlineOrderAsOfflineOrderGet haiDianOnlineOrderAsOfflineOrderGet;

  @Resource
  private HaiDianOnlineRefundOrderAsOfflineOrderGet haiDianOnlineRefundOrderAsOfflineOrderGet;


  @Resource
  private ChaiLingGoodsAmountErrorDataGet chaiLingGoodsAmountErrorDataGet;

  @Resource
  private ChaiLingGoodsAmountRefundErrorDataGet chaiLingGoodsAmountRefundErrorDataGet;


  @Override
  @DS(DATA_SOURCE.ORDER_OFFLINE)
  public ResponseBase<Boolean> haiDianOnlineOrderAsOffline(RepairOfflineOrderReqDto req) {
    haiDianOnlineOrderAsOfflineOrderGet.directEntry(OrderJsonUtils.toJson(req));
    return generateSuccess(Boolean.TRUE);
  }

  @Override
  @DS(DATA_SOURCE.ORDER_OFFLINE)
  public ResponseBase<Boolean> haiDianOnlineRefundOrderAsOffline(
      RepairOfflineRefundOrderReqDto req) {
    haiDianOnlineRefundOrderAsOfflineOrderGet.directEntry(OrderJsonUtils.toJson(req));
    return generateSuccess(Boolean.TRUE);
  }

  @Override
  @DS(DATA_SOURCE.ORDER_OFFLINE)
  public ResponseBase<Boolean> haiDianChaiLingOrder(RepairOfflineOrderReqDto req) {
    chaiLingGoodsAmountErrorDataGet.directEntry(OrderJsonUtils.toJson(req));
    return generateSuccess(Boolean.TRUE);
  }

  @Override
  @DS(DATA_SOURCE.ORDER_OFFLINE)
  public ResponseBase<Boolean> haiDianChaiLingRefundOrder(RepairOfflineRefundOrderReqDto req) {
    chaiLingGoodsAmountRefundErrorDataGet.directEntry(OrderJsonUtils.toJson(req));
    return generateSuccess(Boolean.TRUE);
  }
}
