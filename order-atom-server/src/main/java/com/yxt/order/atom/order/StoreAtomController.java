package com.yxt.order.atom.order;


import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.repository.StoreRepository;
import com.yxt.order.atom.sdk.common.data.StoreBillConfigDTO;
import com.yxt.order.atom.sdk.online_order.store.StoreAtomQueryApi;
import com.yxt.order.atom.sdk.online_order.store.req.GetOnlineStoreByPlatformShopIdReq;
import com.yxt.order.atom.sdk.online_order.store.req.InnerStoreDictionaryListReq;
import com.yxt.order.atom.sdk.online_order.store.req.InnerStoreDictionaryReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryBillConfigByIdReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryBillConfigReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryClientReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryDsOnlineStoreConfigReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryDsOnlineStoreReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryStoreAccessReq;
import com.yxt.order.atom.sdk.online_order.store.req.QueryStorePageReq;
import com.yxt.order.atom.sdk.online_order.store.req.QuerySysStoreInfoReq;
import com.yxt.order.atom.sdk.online_order.store.req.StoreQueryByScaleReq;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineClientResDto;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineStoreConfigResDto;
import com.yxt.order.atom.sdk.online_order.store.res.DsOnlineStoreResDto;
import com.yxt.order.atom.sdk.online_order.store.res.InnerStoreDictionaryResDto;
import com.yxt.order.atom.sdk.online_order.store.res.OnlineStoreInfoResDto;
import com.yxt.order.atom.sdk.online_order.store.res.StoreQueryByScaleResDto;
import com.yxt.order.atom.sdk.online_order.store.res.The3DsOnlineClientResDto;
import com.yxt.order.atom.sdk.online_order.store.res.The3DsStoreResDto;
import com.yxt.starter.controller.AbstractController;
import java.util.List;
import javax.annotation.Resource;
import lombok.EqualsAndHashCode;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年03月01日 14:44
 * @email: <EMAIL>
 */
@EqualsAndHashCode(callSuper = true)
@RestController
public class StoreAtomController extends AbstractController implements StoreAtomQueryApi {

  @Resource
  private StoreRepository storeRepository;

  @Override
  public ResponseBase<DsOnlineClientResDto> queryClient(QueryClientReq req) {
    return generateSuccess(
        storeRepository.selectSupplierClient(req.getPlatformCode(), req.getOnlineStoreCode()));
  }

  @Override
  public ResponseBase<OnlineStoreInfoResDto> querySysStoreInfo(QuerySysStoreInfoReq req) {
    return generateSuccess(storeRepository.querySysStoreInfo(req));
  }

  @Override
  public ResponseBase<The3DsStoreResDto> getStoreAccess(QueryStoreAccessReq req) {
    return generateSuccess(storeRepository.getStoreAccess(req));
  }

  @Override
  public ResponseBase<DsOnlineStoreResDto> queryDsOnlineStore(QueryDsOnlineStoreReq req) {
    return generateSuccess(storeRepository.queryDsOnlineStore(req));
  }

  @Override
  public ResponseBase<DsOnlineStoreConfigResDto> queryDsOnlineStoreConfig(
      QueryDsOnlineStoreConfigReq req) {
    return generateSuccess(storeRepository.queryDsOnlineStoreConfig(req));
  }

  @Override
  public ResponseBase<InnerStoreDictionaryResDto> queryInnerStoreDictionary(
      InnerStoreDictionaryReq req) {
    return generateSuccess(storeRepository.queryInnerStoreDictionary(req));
  }


  @Override
  public ResponseBase<List<InnerStoreDictionaryResDto>> listInnerStoreDictionary(
      InnerStoreDictionaryListReq req) {
    return generateSuccess(storeRepository.queryInnerStoreDictionaryList(req));
  }

  @Override
  public ResponseBase<The3DsOnlineClientResDto> getOnlineStoreByPlatformShopId(
      GetOnlineStoreByPlatformShopIdReq req) {
    return generateSuccess(storeRepository.getOnlineStoreByPlatformShopId(req));
  }

  @Override
  public ResponseBase<StoreBillConfigDTO> getBillConfigById(QueryBillConfigByIdReq req) {
    return generateSuccess(storeRepository.getBillConfigById(req));
  }

  @Override
  public ResponseBase<StoreBillConfigDTO> getBillConfig(QueryBillConfigReq req) {
    return generateSuccess(storeRepository.getBillConfig(req));
  }

  @Override
  public ResponseBase<StoreQueryByScaleResDto> queryStoreInfoByScale(StoreQueryByScaleReq req) {
    return generateSuccess(storeRepository.queryStoreInfoByScale(req));
  }

  @Override
  public ResponseBase<PageDTO<DsOnlineStoreResDto>> queryStorePage(QueryStorePageReq req) {
    return generateSuccess(storeRepository.queryStorePage(req));
  }
}
