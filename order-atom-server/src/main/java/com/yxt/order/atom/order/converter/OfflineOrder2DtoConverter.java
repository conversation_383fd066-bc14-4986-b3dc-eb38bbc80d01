package com.yxt.order.atom.order.converter;


import com.yxt.order.atom.order.entity.OfflineOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineOrderCouponDO;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailPickDO;
import com.yxt.order.atom.order.entity.OfflineOrderMedInsSettleDO;
import com.yxt.order.atom.order.entity.OfflineOrderOrganizationDO;
import com.yxt.order.atom.order.entity.OfflineOrderPayDO;
import com.yxt.order.atom.order.entity.OfflineOrderPrescriptionDO;
import com.yxt.order.atom.order.entity.OfflineOrderPromotionDO;
import com.yxt.order.atom.order.entity.OfflineOrderUserDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderCashierDeskDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderMedInsSettleDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderOrganizationDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderPayDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderUserDO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderCashierDeskDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderCouponDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDetailDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDetailPickDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderMedInsSettleDto;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderOrganizationDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPayDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPrescriptionDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderPromotionDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderUserDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderCashierDeskDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDetailDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderMedInsSettleDto;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderOrganizationDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderPayDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderUserDTO;
import com.yxt.order.atom.sdk.offline_order.res.OfflineOrderInfoResDto;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 线下单转换
 *
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年04月07日 16:15
 * @email: <EMAIL>
 */
@Mapper
public interface OfflineOrder2DtoConverter {

  OfflineOrder2DtoConverter INSTANCE = Mappers.getMapper(OfflineOrder2DtoConverter.class);

  OfflineOrderCashierDeskDTO toDto(OfflineOrderCashierDeskDO obj);
  OfflineOrderDetailDTO toDto(OfflineOrderDetailDO obj);

  OfflineOrderDetailPickDTO toDto(OfflineOrderDetailPickDO obj);
  OfflineOrderOrganizationDTO toDto(OfflineOrderOrganizationDO obj);


  List<OfflineOrderPayDTO> toDto(List<OfflineOrderPayDO> objList);

  OfflineOrderPrescriptionDTO toDto(OfflineOrderPrescriptionDO obj);

  OfflineOrderUserDTO toDto(OfflineOrderUserDO obj);

  OfflineRefundOrderUserDTO toDto(OfflineRefundOrderUserDO obj);

  OfflineRefundOrderDetailDTO toDto(OfflineRefundOrderDetailDO obj);

  OfflineRefundOrderDTO toDto(OfflineRefundOrderDO obj);


  OfflineOrderDTO toDto(OfflineOrderDO obj);

  OfflineOrderInfoResDto toDTO(OfflineOrderDO obj);

  OfflineOrderMedInsSettleDto toDTO(OfflineOrderMedInsSettleDO obj);

  OfflineRefundOrderMedInsSettleDto toDTO(OfflineRefundOrderMedInsSettleDO obj);

  OfflineRefundOrderOrganizationDTO toDTO(OfflineRefundOrderOrganizationDO offlineRefundOrderOrganizationDO);

  OfflineRefundOrderCashierDeskDTO toDTO(OfflineRefundOrderCashierDeskDO offlineRefundOrderCashierDeskDO);

  OfflineRefundOrderPayDTO toDTO(OfflineRefundOrderPayDO offlineRefundOrderPayDO);

  List<OfflineRefundOrderPayDTO> toDTOList(List<OfflineRefundOrderPayDO> offlineRefundOrderPayDOList);

  List<OfflineOrderPromotionDTO> toPromotionDTOList(List<OfflineOrderPromotionDO> offlineOrderPromotionDOS);

  List<OfflineOrderCouponDTO> toCouponDTOList(List<OfflineOrderCouponDO> offlineOrderCouponDOList);
}
