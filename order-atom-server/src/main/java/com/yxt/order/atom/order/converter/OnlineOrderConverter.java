package com.yxt.order.atom.order.converter;

import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.sdk.online_order.order_info.dto.res.SimpleOrderInfoResDto;
import java.util.Objects;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月11日 16:25
 * @email: <EMAIL>
 */
public class OnlineOrderConverter {

  public static SimpleOrderInfoResDto toSimpleOrderInfoResDto(OrderInfoDO orderInfoDO) {
    if (Objects.isNull(orderInfoDO)) {
      return null;
    }

    SimpleOrderInfoResDto simpleOrderInfoResDto = new SimpleOrderInfoResDto();
    simpleOrderInfoResDto.setId(orderInfoDO.getId());
    simpleOrderInfoResDto.setOrderNo(orderInfoDO.getOrderNo());
    simpleOrderInfoResDto.setOrderState(orderInfoDO.getOrderState());
    simpleOrderInfoResDto.setErpState(orderInfoDO.getErpState());
    simpleOrderInfoResDto.setThirdPlatformCode(orderInfoDO.getThirdPlatformCode());
    simpleOrderInfoResDto.setThirdOrderNo(orderInfoDO.getThirdOrderNo());
    simpleOrderInfoResDto.setThirdOrderId(orderInfoDO.getThirdOrderId());
    simpleOrderInfoResDto.setThirdOrderState(orderInfoDO.getThirdOrderState());
    simpleOrderInfoResDto.setOffState(orderInfoDO.getOffState());
    simpleOrderInfoResDto.setFreightOrderNo(orderInfoDO.getFreightOrderNo());
    simpleOrderInfoResDto.setMerCode(orderInfoDO.getMerCode());
    simpleOrderInfoResDto.setClientCode(orderInfoDO.getClientCode());
    simpleOrderInfoResDto.setOnlineStoreCode(orderInfoDO.getOnlineStoreCode());
    simpleOrderInfoResDto.setOnlineStoreName(orderInfoDO.getOnlineStoreName());
    simpleOrderInfoResDto.setOrganizationCode(orderInfoDO.getOrganizationCode());
    simpleOrderInfoResDto.setOrganizationName(orderInfoDO.getOrganizationName());
    simpleOrderInfoResDto.setDeliveryTimeType(orderInfoDO.getDeliveryTimeType());
    simpleOrderInfoResDto.setDeliveryTimeDesc(orderInfoDO.getDeliveryTimeDesc());
    simpleOrderInfoResDto.setBuyerRemark(orderInfoDO.getBuyerRemark());
    simpleOrderInfoResDto.setBuyerMessage(orderInfoDO.getBuyerMessage());
    simpleOrderInfoResDto.setSellerRemark(orderInfoDO.getSellerRemark());
    simpleOrderInfoResDto.setLockFlag(orderInfoDO.getLockFlag());
    simpleOrderInfoResDto.setLockMsg(orderInfoDO.getLockMsg());
    simpleOrderInfoResDto.setLockerId(orderInfoDO.getLockerId());
    simpleOrderInfoResDto.setRemindFlag(orderInfoDO.getRemindFlag());
    simpleOrderInfoResDto.setBuyerName(orderInfoDO.getBuyerName());
    simpleOrderInfoResDto.setReceiverLat(orderInfoDO.getReceiverLat());
    simpleOrderInfoResDto.setReceiverLng(orderInfoDO.getReceiverLng());
    simpleOrderInfoResDto.setAcceptorId(orderInfoDO.getAcceptorId());
    simpleOrderInfoResDto.setAcceptorName(orderInfoDO.getAcceptorName());
    simpleOrderInfoResDto.setAcceptTime(orderInfoDO.getAcceptTime());
    simpleOrderInfoResDto.setPickerId(orderInfoDO.getPickerId());
    simpleOrderInfoResDto.setPickerName(orderInfoDO.getPickerName());
    simpleOrderInfoResDto.setPickOperatorId(orderInfoDO.getPickOperatorId());
    simpleOrderInfoResDto.setPickOperatorName(orderInfoDO.getPickOperatorName());
    simpleOrderInfoResDto.setPickTime(orderInfoDO.getPickTime());
    simpleOrderInfoResDto.setCancellerId(orderInfoDO.getCancellerId());
    simpleOrderInfoResDto.setCancellerName(orderInfoDO.getCancellerName());
    simpleOrderInfoResDto.setCancelReason(orderInfoDO.getCancelReason());
    simpleOrderInfoResDto.setCancelTime(orderInfoDO.getCancelTime());
    simpleOrderInfoResDto.setExOperatorId(orderInfoDO.getExOperatorId());
    simpleOrderInfoResDto.setExOperatorName(orderInfoDO.getExOperatorName());
    simpleOrderInfoResDto.setExOperatorTime(orderInfoDO.getExOperatorTime());
    simpleOrderInfoResDto.setCompleteTime(orderInfoDO.getCompleteTime());
    simpleOrderInfoResDto.setCreated(orderInfoDO.getCreated());
    simpleOrderInfoResDto.setDayNum(orderInfoDO.getDayNum());
    simpleOrderInfoResDto.setModified(orderInfoDO.getModified());
    simpleOrderInfoResDto.setErpAdjustNo(orderInfoDO.getErpAdjustNo());
    simpleOrderInfoResDto.setErpSaleNo(orderInfoDO.getErpSaleNo());
    simpleOrderInfoResDto.setSelfVerifyCode(orderInfoDO.getSelfVerifyCode());
    simpleOrderInfoResDto.setPrescriptionFlag(orderInfoDO.getPrescriptionFlag());
    simpleOrderInfoResDto.setBillTime(orderInfoDO.getBillTime());
    simpleOrderInfoResDto.setCreateTime(orderInfoDO.getCreateTime());
    simpleOrderInfoResDto.setModifyTime(orderInfoDO.getModifyTime());
    simpleOrderInfoResDto.setCallErpFlag(orderInfoDO.getCallErpFlag());
    simpleOrderInfoResDto.setMemberNo(orderInfoDO.getMemberNo());
    simpleOrderInfoResDto.setTransferDelivery(orderInfoDO.getTransferDelivery());
    simpleOrderInfoResDto.setClientConfId(orderInfoDO.getClientConfId());
    simpleOrderInfoResDto.setBillOperator(orderInfoDO.getBillOperator());
    simpleOrderInfoResDto.setAppointment(orderInfoDO.getAppointment());
    simpleOrderInfoResDto.setAppointmentBusinessFlag(orderInfoDO.getAppointmentBusinessFlag());
    simpleOrderInfoResDto.setAppointmentBusinessType(orderInfoDO.getAppointmentBusinessType());
    simpleOrderInfoResDto.setRequestDeliverGoodsResult(orderInfoDO.getRequestDeliverGoodsResult());
    simpleOrderInfoResDto.setDeliverGoodsRefuseReason(orderInfoDO.getDeliverGoodsRefuseReason());
    simpleOrderInfoResDto.setIsPrescription(orderInfoDO.getIsPrescription());
    simpleOrderInfoResDto.setPrescriptionStatus(orderInfoDO.getPrescriptionStatus());
    simpleOrderInfoResDto.setIsPushCheck(orderInfoDO.getIsPushCheck());
    simpleOrderInfoResDto.setNewCustomerFlag(orderInfoDO.getNewCustomerFlag());
    simpleOrderInfoResDto.setIntegralFlag(orderInfoDO.getIntegralFlag());
    simpleOrderInfoResDto.setNeedInvoice(orderInfoDO.getNeedInvoice());
    simpleOrderInfoResDto.setInvoiceTitle(orderInfoDO.getInvoiceTitle());
    simpleOrderInfoResDto.setInvoiceType(orderInfoDO.getInvoiceType());
    simpleOrderInfoResDto.setInvoiceContent(orderInfoDO.getInvoiceContent());
    simpleOrderInfoResDto.setTaxerId(orderInfoDO.getTaxerId());
    simpleOrderInfoResDto.setSourceOnlineStoreCode(orderInfoDO.getSourceOnlineStoreCode());
    simpleOrderInfoResDto.setSourceOnlineStoreName(orderInfoDO.getSourceOnlineStoreName());
    simpleOrderInfoResDto.setSourceOrganizationCode(orderInfoDO.getSourceOrganizationCode());
    simpleOrderInfoResDto.setSourceOrganizationName(orderInfoDO.getSourceOrganizationName());
    simpleOrderInfoResDto.setComplexModifyFlag(orderInfoDO.getComplexModifyFlag());
    simpleOrderInfoResDto.setMedicalInsurance(orderInfoDO.getMedicalInsurance());
    simpleOrderInfoResDto.setServiceMode(orderInfoDO.getServiceMode());
    simpleOrderInfoResDto.setCancelBillTimes(orderInfoDO.getCancelBillTimes());
    simpleOrderInfoResDto.setWscExtJson(orderInfoDO.getWscExtJson());
    simpleOrderInfoResDto.setTopHold(orderInfoDO.getTopHold());
    simpleOrderInfoResDto.setOrderType(orderInfoDO.getOrderType());
    simpleOrderInfoResDto.setOrderIsNew(orderInfoDO.getOrderIsNew());
    simpleOrderInfoResDto.setOrderPickType(orderInfoDO.getOrderPickType());
    simpleOrderInfoResDto.setSourceChannelType(orderInfoDO.getSourceChannelType());
    simpleOrderInfoResDto.setMigrationOrderNo(orderInfoDO.getMigrationOrderNo());
    simpleOrderInfoResDto.setExtendInfo(orderInfoDO.getExtendInfo());
    simpleOrderInfoResDto.setDataVersion(orderInfoDO.getDataVersion());
    simpleOrderInfoResDto.setRemark(orderInfoDO.getRemark());
    simpleOrderInfoResDto.setDeleted(orderInfoDO.getDeleted());
    simpleOrderInfoResDto.setPayTime(orderInfoDO.getPayTime());
    return simpleOrderInfoResDto;
  }
}
