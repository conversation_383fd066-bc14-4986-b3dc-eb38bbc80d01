package com.yxt.order.atom.order.converter;


import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.entity.OrderPickInfoDO;
import com.yxt.order.atom.sdk.common.data.OrderPickInfoDTO;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.OrderDetail;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.OrderDetailAmount;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.OrderDetailChaiLing;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.OrderDetailDescription;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.OrderDetailInfo;
import com.yxt.order.atom.sdk.online_order.order_info.dto.OrderInfoResDto.OrderDetailJoint;
import com.yxt.order.types.order.OrderNo;
import com.yxt.order.types.order.enums.OrderDetailStatusEnum;
import com.yxt.order.types.order.enums.OrderDetailStorageTypeEnum;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年03月01日 16:24
 * @email: <EMAIL>
 */

public class OrderDetailConverter {


  private OrderDetailConverter() {
    throw new UnsupportedOperationException(
        "OrderDetailConverter is a utility class and cannot be instantiated.");
  }


  public static OrderDetailInfo toOrderDetailInfoList(List<OrderDetailDO> orderDetailDOList, List<OrderPickInfoDTO> orderPickInfoDtoList) {
    if (orderDetailDOList == null) {
      return null;
    }
    OrderDetailInfo detailInfoList = new OrderDetailInfo();
    List<OrderDetail> collect = orderDetailDOList.stream()
        .map(OrderDetailConverter::toOrderDetailInfo).collect(Collectors.toList());
    if(null!=orderPickInfoDtoList){

      Map<Long, List<OrderPickInfoDTO>> mapListTemp = orderPickInfoDtoList.stream()
          .collect(Collectors.groupingBy(OrderPickInfoDTO::getOrderDetailId));

      for (OrderDetail orderDetail : collect) {
        orderDetail.setOrderPickInfoList(mapListTemp.get(orderDetail.getId()));
      }

    }

    detailInfoList.setOrderDetailList(collect);
    return detailInfoList;
  }

  private static OrderDetail toOrderDetailInfo(OrderDetailDO orderDetailDO) {
    if (orderDetailDO == null) {
      return null;
    }

    OrderDetail detailInfo = new OrderDetail();
    detailInfo.setId(orderDetailDO.getId());
    detailInfo.setOrderNo(OrderNo.orderNo(orderDetailDO.getOrderNo()));
    detailInfo.setThirdDetailId(orderDetailDO.getThirdDetailId());
    detailInfo.setPlatformSkuId(orderDetailDO.getPlatformSkuId());
    detailInfo.setErpCode(orderDetailDO.getErpCode());
    detailInfo.setOrderDetailStatus(OrderDetailStatusEnum.getEnumByCode(orderDetailDO.getStatus()));
    detailInfo.setCommodityCount(orderDetailDO.getCommodityCount());
    detailInfo.setRefundCount(orderDetailDO.getRefundCount());
    detailInfo.setSwapId(orderDetailDO.getSwapId());
    detailInfo.setOldErpCode(orderDetailDO.getOldErpCode());
    detailInfo.setOrderDetailAmount(toOrderDetailAmount(orderDetailDO));
    detailInfo.setOrderDetailJoint(toOrderDetailJoint(orderDetailDO));
    detailInfo.setOrderDetailChaiLing(toOrderDetailChaiLing(orderDetailDO));
    detailInfo.setOrderDetailDescription(toOrderDetailDescription(orderDetailDO));

    return detailInfo;
  }

  private static OrderDetailDescription toOrderDetailDescription(OrderDetailDO orderDetailDO) {
    OrderDetailDescription description = new OrderDetailDescription();
    description.setCommodityName(orderDetailDO.getCommodityName());
    description.setIsGift(orderDetailDO.getIsGift());
    description.setGoodsType(orderDetailDO.getGoodsType());
    description.setBarCode(orderDetailDO.getBarCode());
    description.setMainPic(orderDetailDO.getMainPic());
    description.setCommoditySpec(orderDetailDO.getCommoditySpec());
    description.setManufacture(orderDetailDO.getManufacture());
    description.setStorageType(
        OrderDetailStorageTypeEnum.getByCode(orderDetailDO.getStorageType()));
    description.setFirstTypeName(orderDetailDO.getFirstTypeName());
    description.setSecondTypeName(orderDetailDO.getSecondTypeName());
    description.setTypeName(orderDetailDO.getTypeName());
    description.setDrugType(orderDetailDO.getDrugType());
    description.setIsMedicareItem(orderDetailDO.getIsMedicareItem());
    description.setSerialNumber(orderDetailDO.getSerialNumber());
    description.setOriginType(orderDetailDO.getOriginType());
    description.setStCode(orderDetailDO.getStCode());
    description.setExpectDeliveryTime(orderDetailDO.getExpectDeliveryTime());
    description.setDirectDeliveryType(orderDetailDO.getDirectDeliveryType());

    return description;


  }

  private static OrderDetailChaiLing toOrderDetailChaiLing(OrderDetailDO orderDetailDO) {
    OrderDetailChaiLing chaiLing = new OrderDetailChaiLing();
    chaiLing.setChaiLing(orderDetailDO.getChailing());
    chaiLing.setChaiLingOriginalErpCode(orderDetailDO.getChaiLingOriginalErpCode());
    chaiLing.setChaiLingNum(orderDetailDO.getChaiLingNum());
    return chaiLing;

  }

  private static OrderDetailJoint toOrderDetailJoint(OrderDetailDO orderDetailDO) {
    OrderDetailJoint joint = new OrderDetailJoint();
    joint.setIsJoint(orderDetailDO.getIsJoint());
    joint.setOriginalErpCode(orderDetailDO.getOriginalErpCode());
    joint.setOriginalErpCodeNum(orderDetailDO.getOriginalErpCodeNum());
    return joint;
  }

  private static OrderDetailAmount toOrderDetailAmount(OrderDetailDO orderDetailDO) {
    OrderDetailAmount amount = new OrderDetailAmount();
    amount.setOriginalPrice(orderDetailDO.getOriginalPrice());
    amount.setPrice(orderDetailDO.getPrice());
    amount.setTotalAmount(orderDetailDO.getTotalAmount());
    amount.setDiscountAmount(orderDetailDO.getDiscountAmount());
    amount.setActualAmount(orderDetailDO.getActualAmount());
    amount.setAdjustAmount(orderDetailDO.getAdjustAmount());
    amount.setDiscountShare(orderDetailDO.getDiscountShare());
    amount.setBillPrice(orderDetailDO.getBillPrice());
    amount.setActualNetAmount(orderDetailDO.getActualNetAmount());
    amount.setDifferentShare(orderDetailDO.getDifferentShare());
    amount.setModifyPriceDiff(orderDetailDO.getModifyPriceDiff());
    amount.setHealthValue(orderDetailDO.getHealthValue());
    amount.setPayment(orderDetailDO.getPayment());
    amount.setDetailDiscount(orderDetailDO.getDetailDiscount());
    return amount;
  }
}
