package com.yxt.order.atom.order.converter;


import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.sdk.common.data.OrderInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 线下单转换
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 16:15
 * @email: <EMAIL>
 */
@Mapper
public interface OrderInfo2DOConverter {

  OrderInfo2DOConverter INSTANCE = Mappers.getMapper(OrderInfo2DOConverter.class);

  OrderInfoDO toDO(OrderInfoDTO obj);

}
