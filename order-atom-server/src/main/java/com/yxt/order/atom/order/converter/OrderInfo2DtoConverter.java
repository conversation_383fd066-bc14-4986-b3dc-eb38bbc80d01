package com.yxt.order.atom.order.converter;


import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.sdk.common.data.OrderInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 线下单转换
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 16:15
 * @email: <EMAIL>
 */
@Mapper
public interface OrderInfo2DtoConverter {

  OrderInfo2DtoConverter INSTANCE = Mappers.getMapper(OrderInfo2DtoConverter.class);

  OrderInfoDTO toDto(OrderInfoDO obj);

}
