package com.yxt.order.atom.order.converter;


import com.yxt.order.atom.order.entity.OrderPayInfoDO;
import com.yxt.order.common.base_order_dto.OrderPayInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;


/**
 * 线下单转换
 *
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 16:15
 * @email: <EMAIL>
 */
@Mapper
public interface OrderPayInfo2DtoConverter {

  OrderPayInfo2DtoConverter INSTANCE = Mappers.getMapper(OrderPayInfo2DtoConverter.class);

  OrderPayInfo toDto(OrderPayInfoDO obj);

}
