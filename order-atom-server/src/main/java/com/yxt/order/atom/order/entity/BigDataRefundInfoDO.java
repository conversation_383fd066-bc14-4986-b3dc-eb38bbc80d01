package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

@Data
@TableName("big_data_refund_info")
public class BigDataRefundInfoDO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String refundNo;

    private String data;

    private String createdBy;

    private String updatedBy;

    private Date createdTime;

    private Date updatedTime;

    private Long version;
}