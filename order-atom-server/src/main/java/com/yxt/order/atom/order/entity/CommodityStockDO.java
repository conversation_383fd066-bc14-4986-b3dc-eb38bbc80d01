package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @author: xin.tu
 * @date: 2023/1/6 10:06
 * @menu:
 */
@Data
@TableName("commodity_stock")
public class CommodityStockDO implements Serializable {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  private String merCode;

  private String orderNo;

  private String erpCode;

  private String onlineStoreCode;

  private Long orderDetailId;

  private Integer stockQty;

  private Integer type;

  private Date createTime;

  private String organizationCode;

  /**
   * 流水号
   */
  private Long serialNumber;

  private String storeId;

  /**
   * 第三方详情ID
   */
  private String thirdDetailId;
}
