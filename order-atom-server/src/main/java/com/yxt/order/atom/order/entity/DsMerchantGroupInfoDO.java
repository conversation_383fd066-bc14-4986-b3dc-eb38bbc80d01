package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 商户分组信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ds_merchant_group_info")
public class DsMerchantGroupInfoDO implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 商户名称
   */
  private String merName;

  /**
   * 秘钥
   */
  private String sessionKey;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 末次修改时间
   */
  private Date modifyTime;


}
