package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * o2o平台网店信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ds_online_client")
public class DsOnlineClientDO implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * o2o平台标记
   */
  private String platformCode;

  /**
   * o2o平台名
   */
  private String platformName;

  /**
   * appid，服务商模式下对应商家账号
   */
  private String appid;

  /**
   * app_secret
   */
  private String appSecret;

  /**
   * 网店编码
   */
  private String onlineClientCode;

  /**
   * 网店名称
   */
  private String onlineClientName;

  /**
   * 授权到期时间
   */
  private Date authDeadline;

  /**
   * 授权时间
   */
  private Date authTime;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 末次修改时间
   */
  private Date modifyTime;

  /**
   * 商家ID(京东到家，微商城需要)
   */
  private String sellerId;

  /**
   * 阿里健康access_token
   */
  private String accessToken;

  /**
   * 阿里健康refresh_token
   */
  private String refreshToken;

  /**
   * 通用O2O平台地址
   */
  private String serverUrl;

  /**
   * 服务编码 o2o b2c
   */
  private String serviceMode;

  /**
   * 合作伙伴id
   */
  private String partnerId;

  /**
   * 外部门店编码storeId
   */
  private String onlineClientOutCode;

  /**
   * B2C 专用，0都没选、1只选了B2C、2只选了O2O、99 都有
   */
  private Integer serviceMask;

  /**
   * 是否异步创建，异步创建的不同步平台不展示不操作
   */
  private Integer syncCreate;

  /**
   * b2c专用  是否拉单
   */
  private Integer pullOrder;
  /**
   * 接口中台返回的秘钥，用于展示
   */
  private String secretKey;

  /**
   * 授权类型 1-商家应用授权，2-服务商应用授权
   */
  private Integer accessType;

  /**
   * 三方平台侧的门店ID
   */
  @TableField(exist = false)
  private String platformShopId;
}