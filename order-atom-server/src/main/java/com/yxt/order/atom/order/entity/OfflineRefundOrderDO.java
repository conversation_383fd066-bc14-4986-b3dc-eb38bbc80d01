package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("offline_refund_order")
public class OfflineRefundOrderDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  private String orderNo;

  private String storeCode;

  private String userId;

  private String refundNo;
  private String parentRefundNo;

  private String thirdPlatformCode;

  private String thirdRefundNo;
  private String parentThirdRefundNo;

  private String thirdOrderNo;

  private String refundType;

  private String afterSaleType;

  private String refundState;

  private String reason;

  private Date created;

  private Date applyTime;

  private Date completeTime;

  private Date billTime;

  private BigDecimal totalAmount;

  private BigDecimal shopRefund;

  private BigDecimal consumerRefund;

  private String createdBy;

  private String updatedBy;

  private Date createdTime;

  private Date updatedTime;

  private Long version;

  // 优惠券核销流水号
  private String serialNo;

  @TableField(exist = false)
  private List<OfflineRefundOrderDetailDO> refundOrderDetailDOList;
  @TableField(exist = false)
  private List<OfflineRefundOrderPayDO> refundOrderPayDOList;
  @TableField(exist = false)
  private OfflineRefundOrderMedInsSettleDO offlineRefundOrderMedInsSettleDO;
  @TableField(exist = false)
  private OfflineRefundOrderCashierDeskDO offlineRefundOrderCashierDeskDO;
  @TableField(exist = false)
  private OfflineRefundOrderOrganizationDO offlineRefundOrderOrganizationDO;

  @TableField(exist = false)
  private OfflineRefundOrderUserDO offlineRefundOrderUserDO;

  private String migration;

  // 是否参加促销的标识, true,false
  private String isOnPromotion;

  public void increaseVersion() {
    this.version = this.version + 1;
  }
  public void markUpdateBy(String updatedBy){
    if(StringUtils.isEmpty(this.updatedBy)){
      this.updatedBy = updatedBy;
    }else {
      this.updatedBy = String.format("%s,%s",this.updatedBy,updatedBy);
    }
  }
}