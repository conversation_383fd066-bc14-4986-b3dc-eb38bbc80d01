package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("order_commodity_detail_cost_price")
@ApiModel(description = "订单商品明细成本价")
public class OrderCommodityDetailCostPriceDO implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  @ApiModelProperty(value = "服务模式(O2O、B2C)")
  private String serviceMode;

  @ApiModelProperty(value = "订单号")
  private Long orderNo;

  @ApiModelProperty(value = "商品erp编码")
  private String erpCode;

  @ApiModelProperty(value = "商品批号")
  private String makeNo;

  @ApiModelProperty(value = "商品批次")
  private String batchNo;

  @ApiModelProperty(value = "商品成本单价")
  private BigDecimal costPrice;

  @ApiModelProperty(value = "商品不含税加权成本价")
  private BigDecimal averagePrice;

  @ApiModelProperty(value = "商品含税加权成本价")
  private BigDecimal taxPrice;

  @ApiModelProperty(value = "税率")
  private String taxRate;

  @ApiModelProperty(value = "创建时间")
  private Date createTime;

  @ApiModelProperty(value = "修改时间")
  private Date modifyTime;

  public String uniqueKey() {
    return this.orderNo + "_" + this.erpCode;
  }

}
