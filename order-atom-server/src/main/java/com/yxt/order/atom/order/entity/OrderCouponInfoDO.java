package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 订单优惠信息
 *
 * @Author: chuf<PERSON>(2910)
 * @Date: 2021/7/21 14:56
 */
@Data
@TableName("order_coupon_info")
public class OrderCouponInfoDO {
  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  //系统单号,退款时是退款单号
  private Long orderNo;
  //1-订单，2-退款单
  private Integer type;
  //优惠券id
  private String couponId;
  //优惠券内容
  private String couponContent;
  //优惠券码
  private String couponCode;
  //优惠名称
  private String couponName;
  //优惠金额
  private String couponPrice;
  //使用场景
  private String couponScene;

  private Date createTime;

  private Date modifyTime;

}
