package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 订单赠品信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-04
 */
@Data
@TableName("order_gift_info")
@EqualsAndHashCode(callSuper = false)
public class OrderGiftInfoDO implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 订单号
   */
  private Long orderNo;

  /**
   * 赠品的ERP商品编码
   */
  private String giftErpCode;

  /**
   * 赠品关联的主商品的ERP商品编码
   */
  private String mainErpCode;

  /**
   * 赠品数量
   */
  private Integer giftNum;

  /**
   * 赠品名称
   */
  private String giftName;

  /**
   * 主商品 skuid
   */
  private String mainSkuId;

  /**
   * 赠品 skuid
   */
  private String giftSkuId;

  /**
   * 商品条形编码
   */
  private String barCode;

  /**
   * 生产商
   */
  private String manufacture;

  /**
   * 商品图片
   */
  private String mainPic;

  /**
   * 商品规格
   */
  private String commoditySpec;

  /**
   * 存储条件
   * <p>
   * cn.hydee.middle.business.order.Enums.CommodityStorageTypeEnum
   */
  private Integer storageType;

  private Date createTime;

  /**
   * 末次修改时间
   */
  private Date modifyTime;

  /**
   * 数据库有该值
   */
  private Integer orderChildNo = 0;

}
