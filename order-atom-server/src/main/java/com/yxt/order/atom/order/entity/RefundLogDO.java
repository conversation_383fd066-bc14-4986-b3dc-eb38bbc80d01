package com.yxt.order.atom.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("refund_log")
public class RefundLogDO implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 退款单号
     */
    private Long refundNo;

    /**
     * 订单号
     */
    private Long orderNo;

    /**
     * 操作员id
     */
    private String operatorId;

    /**
     * 操作描述
     */
    private String operateDesc;

    /**
     * 操作后状态
     */
    private Integer state;

    /**
     * 下账状态
     */
    private Integer erpState;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 末次修改时间
     */
    private Date modifyTime;

    /**
     * 操作前的状态
     */
    private Integer stateBefore;


}
