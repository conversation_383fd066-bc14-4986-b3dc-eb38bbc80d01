package com.yxt.order.atom.order.es;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.common.utils.ESSearchUtils;
import com.yxt.order.atom.order.es.doc.EsOrgOrder;
import com.yxt.order.atom.order.es.doc.EsOrgRefund;
import com.yxt.order.atom.order.es.mapper.EsOrgOrderMapper;
import com.yxt.order.atom.order.es.mapper.EsOrgRefundMapper;
import com.yxt.order.atom.order.es.wrapper.EsQueryBuilder;
import com.yxt.order.atom.sdk.org_order.EsOrgOrderAtomQueryApi;
import com.yxt.order.atom.sdk.org_order.EsOrgRefundAtomQueryApi;
import com.yxt.order.atom.sdk.org_order.req.EsOrderBaseReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgOrderAmountStaticReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgOrderCountStatisticReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgOrderPageQueryReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgRefundAmountStaticReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsOrgRefundPageQueryReqDTO;
import com.yxt.order.atom.sdk.org_order.req.EsRefundBaseReqDTO;
import com.yxt.order.atom.sdk.org_order.res.EsOrgOrderAmountStaticResDTO;
import com.yxt.order.atom.sdk.org_order.res.EsOrgOrderCountStatisticResDTO;
import com.yxt.order.atom.sdk.org_order.res.EsOrgOrderResDTO;
import com.yxt.order.atom.sdk.org_order.res.EsOrgRefundAmountStaticResDTO;
import com.yxt.order.atom.sdk.org_order.res.EsOrgRefundResDTO;
import com.yxt.order.common.es.EsPageDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.dromara.easyes.core.biz.SAPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class EsOrgOrderQueryController implements EsOrgOrderAtomQueryApi, EsOrgRefundAtomQueryApi {

  @Resource
  private EsOrgOrderMapper esOrgOrderMapper;

  @Resource
  private EsOrgRefundMapper esOrgRefundMapper;

  @Override
  public ResponseBase<EsOrgOrderAmountStaticResDTO> orgOrderAmountStatic(EsOrgOrderAmountStaticReqDTO reqDto) {
    LambdaEsQueryWrapper<EsOrgOrder> query = EsQueryBuilder.buildEsQueryForOrgOrderAmountStatic(reqDto);
    SearchResponse response = esOrgOrderMapper.search(query);
    ParsedSum sum = (ParsedSum) response.getAggregations().asList().get(0);
    return ResponseBase.success(new EsOrgOrderAmountStaticResDTO(NumberUtil.toBigDecimal(sum.getValue())));
  }

  @Override
  public ResponseBase<EsOrgOrderCountStatisticResDTO> orgOrderCountStatistic(EsOrgOrderCountStatisticReqDTO reqDto) {
    LambdaEsQueryWrapper<EsOrgOrder> query = EsQueryBuilder.buildEsQueryForOrgOrderAmountStatic(reqDto);
    SearchResponse response = esOrgOrderMapper.search(query);
    //获取聚合结果
    List<EsOrgOrderCountStatisticResDTO.EsOrgOrderCountResult> countList = new ArrayList<>();
    if (response.getAggregations() != null) {

      Terms aggregationResult = (Terms) response.getAggregations().asList().get(0);
      countList = aggregationResult.getBuckets().stream()
          .map(bucket -> new EsOrgOrderCountStatisticResDTO.EsOrgOrderCountResult(bucket.getKey().toString(), String.valueOf(bucket.getDocCount())))
          .collect(Collectors.toList());
    }
    return ResponseBase.success(new EsOrgOrderCountStatisticResDTO(countList));
  }

  @Override
  public ResponseBase<EsPageDTO<EsOrgOrderResDTO>> orgOrderPageQuery(EsOrgOrderPageQueryReqDTO reqDto) {
    LambdaEsQueryWrapper<EsOrgOrder> wrapper = EsQueryBuilder.buildEsQueryForOrgOrderPageQuery(reqDto);
    List<Object> searchAfter = ESSearchUtils.parseSearchAfter(reqDto.getSearchAfter());
    SAPageInfo<EsOrgOrder> page = esOrgOrderMapper.searchAfterPage(wrapper, searchAfter, reqDto.getPageSize().intValue());
    EsPageDTO<EsOrgOrderResDTO> resultPage = new EsPageDTO<>();
    resultPage.setTotalPage((long) page.getPages());
    resultPage.setPageSize((long) page.getPageSize());
    resultPage.setTotalCount(page.getTotal());
    if(CollUtil.isNotEmpty(page.getNextSearchAfter()) && page.getList().size() >= reqDto.getPageSize()){
      resultPage.setSearchAfter(JSON.toJSONString(page.getNextSearchAfter()));
    }
    if (page.getTotal() <= 0) {
      resultPage.setData(new ArrayList<>(0));
      return ResponseBase.success(resultPage);
    }
    List<EsOrgOrderResDTO> resultList =  page.getList().stream().map(data->{
      EsOrgOrderResDTO orderResDTO = BeanUtil.toBean(data, EsOrgOrderResDTO.class);
      if(StrUtil.isNotBlank(data.getOrderFlags())){
        orderResDTO.setOrderFlags(StrUtil.split(data.getOrderFlags()," "));
      }
      return orderResDTO;
    }).collect(Collectors.toList());
    resultPage.setData(resultList);
    return ResponseBase.success(resultPage);
  }

  @Override
  public ResponseBase<EsOrgOrderResDTO> orgOrderDetailQuery(EsOrderBaseReqDTO reqDto) {
    LambdaEsQueryWrapper<EsOrgOrder> wrapper = EsQueryBuilder.buildEsQueryForOrgOrderDetailQuery(reqDto);
    EsOrgOrder esOrgOrder = esOrgOrderMapper.selectOne(wrapper);
    return ResponseBase.success(BeanUtil.toBean(esOrgOrder, EsOrgOrderResDTO.class));
  }

  @Override
  public ResponseBase<EsOrgRefundAmountStaticResDTO> orgRefundAmountStatic(EsOrgRefundAmountStaticReqDTO reqDto) {
    LambdaEsQueryWrapper<EsOrgRefund> query = EsQueryBuilder.buildEsQueryForOrgRefundAmountStatic(reqDto);
    SearchResponse response = esOrgRefundMapper.search(query);
    ParsedSum sum = (ParsedSum) response.getAggregations().asList().get(0);
    return ResponseBase.success(new EsOrgRefundAmountStaticResDTO(NumberUtil.toBigDecimal(sum.getValue())));
  }

  @Override
  public ResponseBase<EsPageDTO<EsOrgRefundResDTO>> orgRefundPageQuery(EsOrgRefundPageQueryReqDTO reqDto) {
    LambdaEsQueryWrapper<EsOrgRefund> wrapper = EsQueryBuilder.buildEsQueryForOrgRefundPageQuery(reqDto);
    List<Object> searchAfter = ESSearchUtils.parseSearchAfter(reqDto.getSearchAfter());
    SAPageInfo<EsOrgRefund> page = esOrgRefundMapper.searchAfterPage(wrapper, searchAfter, reqDto.getPageSize().intValue());
    EsPageDTO<EsOrgRefundResDTO> resultPage = new EsPageDTO<>();
    resultPage.setTotalPage((long) page.getPages());
    resultPage.setPageSize((long) page.getPageSize());
    resultPage.setTotalCount(page.getTotal());
    if(CollUtil.isNotEmpty(page.getNextSearchAfter()) && page.getList().size() >= reqDto.getPageSize()){
      resultPage.setSearchAfter(JSON.toJSONString(page.getNextSearchAfter()));
    }
    if (page.getTotal() <= 0) {
      resultPage.setData(new ArrayList<>(0));
      return ResponseBase.success(resultPage);
    }
    List<EsOrgRefundResDTO> resultList = page.getList().stream().map(data->{
      EsOrgRefundResDTO resp = BeanUtil.toBean(data, EsOrgRefundResDTO.class);
      if(StrUtil.isNotBlank(data.getRefundFlags())){
        resp.setRefundFlags(StrUtil.split(data.getRefundFlags()," "));
      }
      return resp;
    }).collect(Collectors.toList());
    resultPage.setData(resultList);
    return ResponseBase.success(resultPage);
  }

  @Override
  public ResponseBase<EsOrgRefundResDTO> orgRefundDetailQuery(EsRefundBaseReqDTO reqDto) {
    LambdaEsQueryWrapper<EsOrgRefund> wrapper = EsQueryBuilder.buildEsQueryForOrgRefundDetailQuery(reqDto);
    EsOrgRefund esOrgRefund = esOrgRefundMapper.selectOne(wrapper);
    return ResponseBase.success(BeanUtil.toBean(esOrgRefund, EsOrgRefundResDTO.class));
  }
}
