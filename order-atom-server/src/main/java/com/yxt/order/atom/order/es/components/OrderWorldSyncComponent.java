package com.yxt.order.atom.order.es.components;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.order_world.entity.OrderAmountDO;
import com.yxt.order.atom.order_world.entity.OrderDetailDO;
import com.yxt.order.atom.order_world.entity.OrderInfoDO;
import com.yxt.order.atom.order_world.entity.OrderPayDO;
import com.yxt.order.atom.order_world.entity.OrderUserInfoDO;
import com.yxt.order.atom.order_world.entity.RefundOrderAmountDO;
import com.yxt.order.atom.order_world.entity.RefundOrderDO;
import com.yxt.order.atom.order_world.entity.RefundOrderDetailDO;
import com.yxt.order.atom.order_world.entity.RefundOrderUserDO;
import com.yxt.order.atom.order_world.mapper.NewOrderAmountMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderInfoMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderUserInfoMapper;
import com.yxt.order.atom.order_world.mapper.NewRefundOrderAmountMapper;
import com.yxt.order.atom.order_world.mapper.NewRefundOrderDetailMapper;
import com.yxt.order.atom.order_world.mapper.NewRefundOrderMapper;
import com.yxt.order.atom.order_world.mapper.NewRefundOrderUserMapper;
import com.yxt.order.atom.order_world.repository.NewOrderDetailBatchRepository;
import com.yxt.order.atom.order_world.repository.NewOrderPayBatchRepository;
import java.util.List;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class OrderWorldSyncComponent {

  @Autowired
  private NewOrderInfoMapper orderInfoMapper;
  @Autowired
  private NewOrderAmountMapper orderAmountMapper;
  @Autowired
  private NewOrderDetailBatchRepository orderDetailBatchRepository;
  @Autowired
  private NewOrderPayBatchRepository orderPayBatchRepository;
  @Autowired
  private NewOrderUserInfoMapper orderUserInfoMapper;
  @Autowired
  private NewRefundOrderAmountMapper refundOrderAmountMapper;
  @Autowired
  private NewRefundOrderDetailMapper refundOrderDetailMapper;
  @Autowired
  private NewRefundOrderMapper refundOrderMapper;
  @Autowired
  private NewRefundOrderUserMapper refundOrderUserMapper;

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public OrderInfoDO getOrderInfoByOrderNo(String orderNo, Boolean needRoute) {
    LambdaQueryWrapper<OrderInfoDO> wrapper = Wrappers.<OrderInfoDO>lambdaQuery()
        .eq(OrderInfoDO::getOrderNo, orderNo).last(" limit 1 ");
    if (!needRoute) {
      return orderInfoMapper.selectOne(wrapper);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return orderInfoMapper.selectOne(wrapper);
    }

  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public List<OrderDetailDO> getOrderDetailByOrderNo(String orderNo, Boolean needRoute) {
    LambdaQueryWrapper<OrderDetailDO> wrapper = Wrappers.<OrderDetailDO>lambdaQuery()
        .eq(OrderDetailDO::getOrderNo, orderNo);
    if (!needRoute) {
      return orderDetailBatchRepository.list(wrapper);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return orderDetailBatchRepository.list(wrapper);
    }
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public OrderUserInfoDO getOrderUserInfoByOrderNo(String orderNo, Boolean needRoute) {
    LambdaQueryWrapper<OrderUserInfoDO> wrapper = Wrappers.<OrderUserInfoDO>lambdaQuery()
        .eq(OrderUserInfoDO::getOrderNo, orderNo).last(" limit 1 ");
    if (!needRoute) {
      return orderUserInfoMapper.selectOne(wrapper);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return orderUserInfoMapper.selectOne(wrapper);
    }
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public List<OrderPayDO> getOrderPayTypeByOrderNo(String orderNo, Boolean needRoute) {
    LambdaQueryWrapper<OrderPayDO> wrapper = Wrappers.<OrderPayDO>lambdaQuery()
        .eq(OrderPayDO::getOrderNo, orderNo);
    if (!needRoute) {
      return orderPayBatchRepository.list(wrapper);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return orderPayBatchRepository.list(wrapper);
    }
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public OrderAmountDO getOrderAmountByOrderNo(String orderNo, Boolean needRoute) {
    LambdaQueryWrapper<OrderAmountDO> wrapper = Wrappers.<OrderAmountDO>lambdaQuery()
        .eq(OrderAmountDO::getOrderNo, orderNo).last(" limit 1 ");
    if (!needRoute) {
      return orderAmountMapper.selectOne(wrapper);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return orderAmountMapper.selectOne(wrapper);
    }
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public RefundOrderDO getRefundInfoByRefundNo(String refundNo, Boolean needRoute) {
    LambdaQueryWrapper<RefundOrderDO> wrapper = Wrappers.<RefundOrderDO>lambdaQuery()
        .eq(RefundOrderDO::getRefundNo, refundNo).last(" limit 1 ");
    if (!needRoute) {
      return refundOrderMapper.selectOne(wrapper);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return refundOrderMapper.selectOne(wrapper);
    }

  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public List<RefundOrderDetailDO> getRefundDetailByRefundNo(String refundNo, Boolean needRoute) {
    LambdaQueryWrapper<RefundOrderDetailDO> wrapper = Wrappers.<RefundOrderDetailDO>lambdaQuery()
        .eq(RefundOrderDetailDO::getRefundNo, refundNo);
    if (!needRoute) {
      return refundOrderDetailMapper.selectList(wrapper);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return refundOrderDetailMapper.selectList(wrapper);
    }
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public RefundOrderUserDO getRefundUserInfoByRefundNo(String refundNo, Boolean needRoute) {
    LambdaQueryWrapper<RefundOrderUserDO> wrapper = Wrappers.<RefundOrderUserDO>lambdaQuery()
        .eq(RefundOrderUserDO::getRefundNo, refundNo).last(" limit 1 ");
    if (!needRoute) {
      return refundOrderUserMapper.selectOne(wrapper);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return refundOrderUserMapper.selectOne(wrapper);
    }
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public RefundOrderAmountDO getRefundAmountByOrderNo(String refundNo, Boolean needRoute) {
    LambdaQueryWrapper<RefundOrderAmountDO> wrapper = Wrappers.<RefundOrderAmountDO>lambdaQuery()
        .eq(RefundOrderAmountDO::getRefundNo, refundNo).last(" limit 1 ");
    if (!needRoute) {
      return refundOrderAmountMapper.selectOne(wrapper);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return refundOrderAmountMapper.selectOne(wrapper);
    }
  }
}
