package com.yxt.order.atom.order.es.components;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.middle.member.api.MemberInfoApi;
import com.yxt.middle.member.res.member.MemberInfoVo;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.order.entity.ErpBillInfoDO;
import com.yxt.order.atom.order.entity.ErpRefundInfoDO;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.entity.OrderDeliveryRecordDO;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.entity.RefundDetailDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.entity.RouteAllotDO;
import com.yxt.order.atom.order.mapper.ErpBillInfoMapper;
import com.yxt.order.atom.order.mapper.ErpRefundInfoMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderDetailMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderDetailMapper;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderMapper;
import com.yxt.order.atom.order.mapper.OrderDeliveryRecordMapper;
import com.yxt.order.atom.order.mapper.OrderDetailMapper;
import com.yxt.order.atom.order.mapper.OrderInfoMapper;
import com.yxt.order.atom.order.mapper.RefundDetailMapper;
import com.yxt.order.atom.order.mapper.RefundOrderMapper;
import com.yxt.order.atom.order.mapper.RouteAllotMapper;
import com.yxt.order.atom.order.mapper.SyncEsMapper;
import com.yxt.order.atom.order.mapper.dto.OrganizationInfoDto;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.es_order.EsServiceMode;
import com.yxt.order.types.order.enums.AllotStatusEnum;
import com.yxt.order.types.order.enums.AllotTypeEnum;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年08月20日 17:48
 * @email: <EMAIL>
 */
@Component
@Slf4j
public class SyncComponent {


  @Resource
  private MemberInfoApi memberInfoApi;

  @Resource
  private SyncEsMapper syncEsMapper;

  @Resource
  private OrderInfoMapper orderInfoMapper;

  @Resource
  private OrderDetailMapper orderDetailMapper;

  @Resource
  private ErpBillInfoMapper erpBillInfoMapper;

  @Resource
  private RefundOrderMapper refundOrderMapper;

  @Resource
  private RefundDetailMapper refundDetailMapper;

  @Resource
  private ErpRefundInfoMapper erpRefundInfoMapper;

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  @Resource
  private OfflineOrderDetailMapper offlineOrderDetailMapper;

  @Resource
  private OfflineRefundOrderMapper offlineRefundOrderMapper;

  @Resource
  private OfflineRefundOrderDetailMapper offlineRefundOrderDetailMapper;

  @Resource
  private RouteAllotMapper routeAllotMapper;

  @Resource
  private OrderDeliveryRecordMapper orderDeliveryRecordMapper;

  @Resource
  private StringRedisTemplate stringRedisTemplate;

  private static final String memberCardCacheKey = "order-atom:member:{0}";

  public String queryUserId(String memberCardNo) {
    if (StringUtils.isEmpty(memberCardNo)) {
      return Strings.EMPTY;
    }
    String memberCacheKey = StrUtil.indexedFormat(memberCardCacheKey, memberCardNo);
    if(stringRedisTemplate.hasKey(memberCacheKey)){
      return stringRedisTemplate.opsForValue().get(memberCacheKey);
    }
    String userId = Strings.EMPTY;
    ResponseBase<MemberInfoVo> responseBase = memberInfoApi.getMemberByCardNo(memberCardNo);
    if ("E2011".equals(responseBase.getCode()) || "会员不存在".equals(responseBase.getMsg())) {
      log.warn("调用会员接口查不到会员信息:{}", memberCardNo);
      userId = Strings.EMPTY; // 返回空,不中断上游流程
    }else {
      userId = String.valueOf(responseBase.getData().getUserId());
    }
    //缓存30-120分钟的随机值
    stringRedisTemplate.opsForValue().set(memberCacheKey, userId, RandomUtil.randomLong(30L, 120L), TimeUnit.MINUTES);
    return userId;
  }


  public EsServiceMode mappingServiceMode(String serviceMode) {
    if (DsConstants.B2C.equals(serviceMode)) {
      return EsServiceMode.B2C;
    } else if (DsConstants.O2O.equals(serviceMode)) {
      return EsServiceMode.O2O;
    }
    return null;
  }


  public String queryRefundOrderNo(String orderNo) {
    String memberCardNo = syncEsMapper.selectMemberCardNo(Long.valueOf(orderNo));
    if (StringUtils.isEmpty(memberCardNo)) {
      return Strings.EMPTY;
    }
    return this.queryUserId(memberCardNo);
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public String getUserCardNoByOrderNo(String orderNo,Boolean needRoute) {
    if(!needRoute){
      return syncEsMapper.getUserCardNoByOrderNo(orderNo);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return syncEsMapper.getUserCardNoByOrderNo(orderNo);
    }
  }
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public OrganizationInfoDto getOrganizationByOrderNo(String orderNo,Boolean needRoute) {
    if(!needRoute){
      return syncEsMapper.getOrganizationByOrderNo(orderNo);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return syncEsMapper.getOrganizationByOrderNo(orderNo);
    }
  }
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public String getUserCardNoByRefundNo(String refundNo,Boolean needRoute) {
    if(!needRoute){
      return syncEsMapper.getUserCardNoByRefundNo(refundNo);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return syncEsMapper.getUserCardNoByRefundNo(refundNo);
    }
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public OrganizationInfoDto getOrganizationByRefundNo(String refundNo,Boolean needRoute) {
    if(!needRoute){
      return syncEsMapper.getOrganizationByRefundNo(refundNo);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return syncEsMapper.getOrganizationByRefundNo(refundNo);
    }
  }

  @DS(DATA_SOURCE.ORDER_MASTER)
  public List<OrderInfoDO> getOrderListByOrderNo(List<String> orderNoList) {
    return orderInfoMapper.selectList(Wrappers.<OrderInfoDO>lambdaQuery().in(OrderInfoDO::getOrderNo,orderNoList));
  }

  @DS(DATA_SOURCE.ORDER_MASTER)
  public List<OrderDetailDO> getOrderDetailListByOrderNo(List<String> orderNoList) {
    return orderDetailMapper.selectList(Wrappers.<OrderDetailDO>lambdaQuery().in(OrderDetailDO::getOrderNo,orderNoList));
  }

  @DS(DATA_SOURCE.ORDER_MASTER)
  public List<ErpBillInfoDO> getOrderErpInfoListByOrderNo(List<String> orderNoList) {
    return erpBillInfoMapper.selectList(Wrappers.<ErpBillInfoDO>lambdaQuery().in(ErpBillInfoDO::getOrderNo,orderNoList));
  }

  @DS(DATA_SOURCE.ORDER_MASTER)
  public List<RefundOrderDO> getRefundListByOrderNo(List<String> orderNoList) {
    return refundOrderMapper.selectList(Wrappers.<RefundOrderDO>lambdaQuery().in(RefundOrderDO::getOrderNo, orderNoList));
  }
  @DS(DATA_SOURCE.ORDER_MASTER)
  public List<RefundOrderDO> getRefundListByRefundNo(List<String> refundNoList) {
    return refundOrderMapper.selectList(Wrappers.<RefundOrderDO>lambdaQuery().in(RefundOrderDO::getRefundNo, refundNoList));
  }

  @DS(DATA_SOURCE.ORDER_MASTER)
  public List<RefundDetailDO> getRefundDetailListByRefundNo(List<Long> refundNoList) {
    return refundDetailMapper.selectList(Wrappers.<RefundDetailDO>lambdaQuery().in(RefundDetailDO::getRefundNo, refundNoList));
  }

  @DS(DATA_SOURCE.ORDER_MASTER)
  public List<ErpRefundInfoDO> getRefundErpInfoListByRefundNo(List<Long> refundNoList) {
    return erpRefundInfoMapper.selectList(Wrappers.<ErpRefundInfoDO>lambdaQuery().in(ErpRefundInfoDO::getRefundNo, refundNoList));
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public OfflineOrderDO getOfflineOrderByOrderNo(String orderNo, Boolean needRoute) {
    LambdaQueryWrapper<OfflineOrderDO> wrapper = Wrappers.<OfflineOrderDO>lambdaQuery()
        .eq(OfflineOrderDO::getOrderNo, orderNo).last(" limit 1 ");
    if (!needRoute) {
      return offlineOrderMapper.selectOne(wrapper);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineOrderMapper.selectOne(wrapper);
    }
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public List<OfflineOrderDetailDO> getOfflineOrderDetailByOrderNo(String orderNo, Boolean needRoute) {
    LambdaQueryWrapper<OfflineOrderDetailDO> wrapper = Wrappers.<OfflineOrderDetailDO>lambdaQuery()
        .eq(OfflineOrderDetailDO::getOrderNo, orderNo);
    if (!needRoute) {
      return offlineOrderDetailMapper.selectList(wrapper);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineOrderDetailMapper.selectList(wrapper);
    }
  }
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public OfflineRefundOrderDO getOfflineRefundByRefundNo(String refundNo, Boolean needRoute) {
    LambdaQueryWrapper<OfflineRefundOrderDO> wrapper = Wrappers.<OfflineRefundOrderDO>lambdaQuery()
        .eq(OfflineRefundOrderDO::getRefundNo, refundNo).last(" limit 1 ");
    if (!needRoute) {
      return offlineRefundOrderMapper.selectOne(wrapper);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineRefundOrderMapper.selectOne(wrapper);
    }
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public List<OfflineRefundOrderDetailDO> getOfflineRefundDetailByRefundNo(String refundNo, Boolean needRoute) {
    LambdaQueryWrapper<OfflineRefundOrderDetailDO> wrapper = Wrappers.<OfflineRefundOrderDetailDO>lambdaQuery()
        .eq(OfflineRefundOrderDetailDO::getRefundNo, refundNo);
    if (!needRoute) {
      return offlineRefundOrderDetailMapper.selectList(wrapper);
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(refundNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      return offlineRefundOrderDetailMapper.selectList(wrapper);
    }
  }

  @DS(DATA_SOURCE.ORDER_MASTER)
  public List<RouteAllotDO> getOrderRouteAllot(List<String> orderNoList) {
    return routeAllotMapper.selectList(Wrappers.<RouteAllotDO>lambdaQuery()
        .in(RouteAllotDO::getOmsNo, orderNoList)
        .eq(RouteAllotDO::getAllotStatus, AllotStatusEnum.SUCCESS.name())
        .eq(RouteAllotDO::getOrderType, AllotTypeEnum.ORDER.name())
    );
  }

  @DS(DATA_SOURCE.ORDER_MASTER)
  public List<OrderDeliveryRecordDO> getOrderDeliveryRecordInfo(List<String> orderNoList) {
    return orderDeliveryRecordMapper.selectList(Wrappers.<OrderDeliveryRecordDO>lambdaQuery().in(OrderDeliveryRecordDO::getOrderNo, orderNoList));
  }
}
