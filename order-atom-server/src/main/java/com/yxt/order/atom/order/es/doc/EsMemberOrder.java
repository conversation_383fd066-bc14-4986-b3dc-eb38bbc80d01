package com.yxt.order.atom.order.es.doc;

import java.util.Date;
import java.util.List;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

/**
 * 会员订单
 *
 * @author: moatkon
 * @time: 2024/12/9 10:29
 */
@Data
@Settings(shardsNum = 36) //会员侧:8000万会员,12个分片,每个分配4GB; 订单一个月1000万会员订单,2年有24000万订单. 24000/8000=3,【粗略】估算: 3*12=36个分片
@IndexName(value = "es_member_order",keepGlobalPrefix = true,aliasName = "alias_es_member_order")
public class EsMemberOrder {

  @IndexId(type = IdType.CUSTOMIZE)
  private String id;

  /**
   * 系统单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orderNo;

  /**
   * 会员ID (心云)
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String userId;

  /**
   * 下单时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date created;

  /**
   * 创建时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;

  /**
   * 门店
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String storeCode;

  /**
   * 会员编码(唯一值)
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String userCardNo;

  /**
   * 订单来源 ONLINE-线上订单 POS-线下订单
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orderSource;

  /**
   * HAIDIAN-海典 、 KE_CHUAN-科传 、 JD_DAOJIA-京东到家 、 MEITUAN-美团 、 E_BAI-饿百 、 YD_JIA-微商城 、 PING_AN_CENTRAL-平安中心仓 、 PA_COMMON_O2O-平安O2O 、 PA_CITY-平安城市仓 、 ALI_HEALTH-阿里健康 、 JD_HEALTH-京东健康 、 TY_O2O-电商第三方标准平台 、 INHERIT_TM_TCG-淘宝 、 DOUDIAN-抖店 、 ZHIFUBAO-支付宝小程序 、 PDD_B2C-拼多多B2C 、 JD_B2C-京东B2C 、 KUAI_SHOU-快手 、 OTHER-其他渠道 、 POS_HD_H1-海典H1 、 POS_HD_H2-海典H2 、 POS_KC-科传
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String platformCode;

  /**
   * 订单状态 5待处理,10待接单,20待拣货,30待配送,
   * 40待收货,100已完成,102已取消,101已关闭
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private Integer orderStatus;

  /**
   * 三方订单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdOrderNo;

  /**
   * 分公司Code
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String companyCode;

  /**
   * 门店类型 DIRECT_SALES-直营 JOIN-加盟
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String storeType;

  // 模型自由字段,其他表适配该字段
  // 默认0-未删除
  @IndexField(fieldType = FieldType.KEYWORD)
  private Long deleted;

  @IndexField(fieldType = FieldType.NESTED, nestedClass = EsMemberOrderDetail.class)
  private List<EsMemberOrderDetail> esMemberOrderDetailList;


}
