package com.yxt.order.atom.order.es.doc;

import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

/**
 * @author: moatkon
 * @time: 2025/3/27 18:26
 */
@Data
@IndexName(value = "es_offline_order_manage", keepGlobalPrefix = true, aliasName = "alias_es_offline_order_manage")
public class EsOfflineOrderManage {

  @IndexId(type = IdType.CUSTOMIZE)
  private String id;

  /**
   * 系统单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String orderNo;

  /**
   * POS
   *
   * @see ThirdPlatformCodeEnum
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdPlatformCode;

  /**
   * 平台单号
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String thirdOrderNo;

  /**
   * 门店
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String storeCode;

  /**
   * 下单时间
   */
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date created;


  @IndexField(fieldType = FieldType.NESTED, nestedClass = EsOfflineOrderManageDetail.class)
  private List<EsOfflineOrderManageDetail> offlineOrderManageDetailList;

  /**
   * 公司编码
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String companyCode;


  @IndexField(fieldType = FieldType.SCALED_FLOAT)
  private BigDecimal actualPayAmount;

}
