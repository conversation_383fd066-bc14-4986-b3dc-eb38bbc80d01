package com.yxt.order.atom.order.es.doc;

import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.rely.FieldType;

/**
 * @author: moatkon
 * @time: 2024/11/4 16:33
 */
@Data
public class EsOmsOrderItem {

  @IndexField(fieldType = FieldType.KEYWORD)
  private Long omsOrderNo;

  /**
   * 明细状态，0正常显示商品，1已退款，2被换货的商品
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private Integer status;

  /**
   * 商品erp编码
   *
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String erpCode;

  /**
   * 商品名称
   */
  @IndexField(fieldType = FieldType.KEYWORD)
  private String commodityName;


}
