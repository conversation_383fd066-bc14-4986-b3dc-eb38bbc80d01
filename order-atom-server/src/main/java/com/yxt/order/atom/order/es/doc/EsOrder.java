package com.yxt.order.atom.order.es.doc;


import com.yxt.order.types.es_order.EsOrderStatus;
import com.yxt.order.types.es_order.EsOrderType;
import com.yxt.order.types.es_order.EsServiceMode;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.dromara.easyes.annotation.IndexField;
import org.dromara.easyes.annotation.IndexId;
import org.dromara.easyes.annotation.IndexName;
import org.dromara.easyes.annotation.Settings;
import org.dromara.easyes.annotation.rely.FieldType;
import org.dromara.easyes.annotation.rely.IdType;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月07日 15:03
 * @email: <EMAIL>
 */
@Data
@Settings(shardsNum = 3)
@IndexName(value = "es_order_chronic_disease",keepGlobalPrefix = true)
public class EsOrder {

  @IndexId(type = IdType.CUSTOMIZE)
  private String orderNumber;  //对应的订单号或退单号,作为Es唯一Id,这个可以

  private EsOrderType esOrderType;  //订单类型，正单/退单
  private String orderNo;//正单号 EsOrderType=ORDER时有
  private String refundNo;//退单号 EsOrderType=REFUND时有


  // note: 所有source开头的点最终都会转化到online,所以取online
  private String onlineStoreCode; // 网点code
  private String organizationCode; // 网点单对应的履约单

  private String platformCode;
  private String thirdOrderNo;
  private String thirdRefundNo;
  @IndexField(fieldType = FieldType.KEYWORD)
  private String userId;  //会员id 可以查会员卡号

  private EsServiceMode serviceMode; // O2O,B2C,POS
  private EsOrderStatus esOrderStatus;

  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date payTime;  //支付时间

  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date completeTime;  //完成时间,如果是退款就是退款完成时间

  // 交易日期==订单的创建时间
  @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
  private Date createTime;


  @IndexField(fieldType = FieldType.NESTED, nestedClass = EsOrderItem.class)
  private List<EsOrderItem> esOrderItemList;
}
