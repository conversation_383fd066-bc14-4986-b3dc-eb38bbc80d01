package com.yxt.order.atom.order.es.sync;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.entity.OmsOrderInfoDO;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2024/12/20 18:28
 */
public class FlashQueryWrapper {

  public static LambdaQueryWrapper<OfflineOrderDO> offlineOrderFlashQuery(FlashParam flashParam,
      Integer limit) {
    LambdaQueryWrapper<OfflineOrderDO> query = new LambdaQueryWrapper<>();
    if (!CollectionUtils.isEmpty(flashParam.getNoList())) {
      query.in(OfflineOrderDO::getOrderNo, flashParam.getNoList());
    } else {
      query.ge(OfflineOrderDO::getId, flashParam.getCursorStartId());
      query.le(OfflineOrderDO::getId, flashParam.getCursorStartId() + limit);
    }
    return query;
  }

  public static LambdaQueryWrapper<OfflineRefundOrderDO> offlineRefundOrderFlashQuery(
      FlashParam flashParam, Integer limit) {
    LambdaQueryWrapper<OfflineRefundOrderDO> query = new LambdaQueryWrapper<>();
    if (!CollectionUtils.isEmpty(flashParam.getNoList())) {
      query.in(OfflineRefundOrderDO::getRefundNo, flashParam.getNoList());
    }else{
      query.ge(OfflineRefundOrderDO::getId, flashParam.getCursorStartId());
      query.le(OfflineRefundOrderDO::getId, flashParam.getCursorStartId() + limit);
    }
    return query;
  }


  public static LambdaQueryWrapper<OrderInfoDO> orderInfoFlashQuery(FlashParam flashParam,
      Integer limit) {
    LambdaQueryWrapper<OrderInfoDO> query = new LambdaQueryWrapper<>();
    if (!CollectionUtils.isEmpty(flashParam.getNoList())) {
      query.in(OrderInfoDO::getOrderNo, flashParam.getNoList());
    }else {
      query.ge(OrderInfoDO::getId, flashParam.getCursorStartId());
      query.le(OrderInfoDO::getId, flashParam.getCursorStartId() + limit);
    }
    return query;
  }


  public static LambdaQueryWrapper<RefundOrderDO> refundOrderFlashQuery(FlashParam flashParam,
      Integer limit) {
    LambdaQueryWrapper<RefundOrderDO> query = new LambdaQueryWrapper<>();
    if (!CollectionUtils.isEmpty(flashParam.getNoList())) {
      query.in(RefundOrderDO::getRefundNo, flashParam.getNoList());
    }else {
      query.ge(RefundOrderDO::getId, flashParam.getCursorStartId());
      query.le(RefundOrderDO::getId, flashParam.getCursorStartId() + limit);
    }
    return query;
  }

  public static LambdaQueryWrapper<OmsOrderInfoDO> omsOrderInfoFlashQuery(FlashParam flashParam,
      Integer limit) {
    LambdaQueryWrapper<OmsOrderInfoDO> query = new LambdaQueryWrapper<>();
    if (!CollectionUtils.isEmpty(flashParam.getNoList())) {
      query.in(OmsOrderInfoDO::getOmsOrderNo, flashParam.getNoList());
    }else {
      query.ge(OmsOrderInfoDO::getId, flashParam.getCursorStartId());
      query.le(OmsOrderInfoDO::getId, flashParam.getCursorStartId() + limit);
    }
    return query;
  }

  public static LambdaQueryWrapper<com.yxt.order.atom.order_world.entity.OrderInfoDO> newOrderFlashQuery(FlashParam flashParam, Integer limit) {
    LambdaQueryWrapper<com.yxt.order.atom.order_world.entity.OrderInfoDO> query = new LambdaQueryWrapper<>();
    if (!CollectionUtils.isEmpty(flashParam.getNoList())) {
      query.in(com.yxt.order.atom.order_world.entity.OrderInfoDO::getOrderNo, flashParam.getNoList());
    } else {
      query.ge(com.yxt.order.atom.order_world.entity.OrderInfoDO::getId, flashParam.getCursorStartId());
      query.le(com.yxt.order.atom.order_world.entity.OrderInfoDO::getId, flashParam.getCursorStartId() + limit);
    }
    return query;
  }
}
