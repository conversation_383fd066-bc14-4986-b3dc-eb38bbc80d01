package com.yxt.order.atom.order.es.sync.b2c_account_info.handler;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.yxt.order.atom.order.entity.AccountInfoDO;
import com.yxt.order.atom.order.es.doc.EsAccountItem;
import com.yxt.order.atom.order.es.dto.OrderDetailDto;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.order.es.sync.b2c_account_info.EsB2cRefundAccountInfoModel;
import com.yxt.order.atom.order.es.sync.data.CanalB2cRefundAccountInfo;
import com.yxt.order.atom.order.es.sync.data.CanalB2cRefundAccountInfo.RefundAccountInfo;
import com.yxt.order.atom.order.mapper.AccountInfoMapper;
import com.yxt.order.atom.order.mapper.SyncEsMapper;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: yang jun feng
 * @time: 2024/11/12 17:50
 */
@Component
@Slf4j
public class B2cRefundAccountCanalHandler extends
    AbstractCanalHandler<CanalB2cRefundAccountInfo, EsB2cRefundAccountInfoModel> {
  @Resource
  private SyncEsMapper syncEsMapper;
  @Resource
  private AccountInfoMapper accountInfoMapper;
  public B2cRefundAccountCanalHandler() {
    super(CanalB2cRefundAccountInfo.class);
  }


  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD.equals(database) && table.equals(Table.REFUND_ACCOUNT_INFO);
  }

  @Override
  protected List<EsB2cRefundAccountInfoModel> assemble() {
    List<RefundAccountInfo> accountInfos = getData().getData();
    if (CollectionUtils.isEmpty(accountInfos)) {
      return Lists.newArrayList();
    }

    // 获取数据详情
    return accountInfos.stream().map(
        this::buildEsAccountInfo
    ).collect(Collectors.toList());
  }

  private EsB2cRefundAccountInfoModel buildEsAccountInfo(RefundAccountInfo accountInfoBO) {
    EsB2cRefundAccountInfoModel esB2cAccountInfoModel = new EsB2cRefundAccountInfoModel();
    esB2cAccountInfoModel.setId(accountInfoBO.getId());
    esB2cAccountInfoModel.setOrderNo(accountInfoBO.getOrderNo());
    esB2cAccountInfoModel.setRefundNo(accountInfoBO.getRefundNo());
    esB2cAccountInfoModel.setThirdRefundNo(accountInfoBO.getThirdRefundNo());
    esB2cAccountInfoModel.setThirdPlatCode(accountInfoBO.getThirdPlatCode());
    esB2cAccountInfoModel.setServiceMode(accountInfoBO.getServiceMode());
    esB2cAccountInfoModel.setPosMode(accountInfoBO.getPosMode());
    esB2cAccountInfoModel.setPickType(accountInfoBO.getPickType());
    esB2cAccountInfoModel.setSubCompanyCode(accountInfoBO.getSubCompanyCode());
    esB2cAccountInfoModel.setOrganizationCode(accountInfoBO.getOrganizationCode());
    esB2cAccountInfoModel.setOrgParentPath(accountInfoBO.getOrgParentPath());
    esB2cAccountInfoModel.setAccOrganizationCode(accountInfoBO.getAccOrganizationCode());
    esB2cAccountInfoModel.setAccOrgParentPath(accountInfoBO.getAccOrgParentPath());
    esB2cAccountInfoModel.setAccOnlineStoreId(accountInfoBO.getAccOnlineStoreId());
    esB2cAccountInfoModel.setRefundType(accountInfoBO.getRefundType());
    esB2cAccountInfoModel.setType(accountInfoBO.getType());
    esB2cAccountInfoModel.setRefundAmount(accountInfoBO.getRefundAmount());
    esB2cAccountInfoModel.setRefundGoodsTotal(accountInfoBO.getRefundGoodsTotal());
    esB2cAccountInfoModel.setRefundPostFee(accountInfoBO.getRefundPostFee());
    esB2cAccountInfoModel.setDiscountAmount(accountInfoBO.getDiscountAmount());
    esB2cAccountInfoModel.setPackageFee(accountInfoBO.getPackageFee());
    esB2cAccountInfoModel.setRefundAcceptTime(accountInfoBO.getRefundAcceptTime());
    esB2cAccountInfoModel.setRefundReason(accountInfoBO.getRefundReason());
    esB2cAccountInfoModel.setAccountTime(accountInfoBO.getAccountTime());
    esB2cAccountInfoModel.setAccountErrMsg(accountInfoBO.getAccountErrMsg());
    esB2cAccountInfoModel.setCreateTime(accountInfoBO.getCreateTime());
    esB2cAccountInfoModel.setUpdateTime(accountInfoBO.getUpdateTime());
    esB2cAccountInfoModel.setDeleted(accountInfoBO.getDeleted());
    esB2cAccountInfoModel.setVersion(accountInfoBO.getVersion());
    esB2cAccountInfoModel.setState(accountInfoBO.getState());
    esB2cAccountInfoModel.setSaleNo(accountInfoBO.getSaleNo());
    List<OrderDetailDto> orderDetailList = null;
    if (accountInfoBO.getServiceMode().equals("B2C")) {
      // 商品信息
      orderDetailList = syncEsMapper.selectRefundDetailByRefundNo(esB2cAccountInfoModel.getRefundNo());
    }

    // 获取正向单 第三方平台订单号
    AccountInfoDO accountInfoDO= accountInfoMapper.selectOne(new LambdaQueryWrapper<AccountInfoDO>().eq(AccountInfoDO::getOrderNo, accountInfoBO.getOrderNo()).eq(AccountInfoDO::getDeleted, 0L));
    if(ObjectUtil.isNotNull(accountInfoDO)){
      esB2cAccountInfoModel.setThirdOrderNo(accountInfoDO.getThirdOrderNo());
    }
    List<EsAccountItem> items=new ArrayList<>();
    if (ObjectUtil.isNotNull(orderDetailList)) {
      orderDetailList.forEach(item ->{
        EsAccountItem orderItem=new EsAccountItem();
        orderItem.setStatus(item.getStatus());
        orderItem.setErpCode(item.getCommodityCode());
        items.add(orderItem);
      });
    }
    esB2cAccountInfoModel.setItems(items);
    return esB2cAccountInfoModel;
  }
}
