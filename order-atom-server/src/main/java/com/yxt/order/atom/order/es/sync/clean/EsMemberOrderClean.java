package com.yxt.order.atom.order.es.sync.clean;

import static com.yxt.order.atom.common.utils.OrderDateUtils.formatYYMMDD;

import com.yxt.order.atom.order.es.doc.EsMemberOrder;
import com.yxt.order.atom.order.es.mapper.EsMemberOrderMapper;
import com.yxt.order.atom.order.es.sync.AbstractClean;
import java.util.Date;
import javax.annotation.Resource;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2024/12/13 15:40
 */
@Component
public class EsMemberOrderClean extends AbstractClean {

  @Resource
  private EsMemberOrderMapper esMemberOrderMapper;

  @Override
  protected Integer expireDays() {
    return ExpireDaysConstant.EsMemberOrderEfficientDays;
  }

  @Override
  protected void clean(String startDate, String endDate) {
    LambdaEsQueryWrapper<EsMemberOrder> query = new LambdaEsQueryWrapper<>();
    query.gt(EsMemberOrder::getCreateTime, startDate);
    query.le(EsMemberOrder::getCreateTime, endDate);
    esMemberOrderMapper.delete(query);
  }

  @Override
  protected Boolean checkHasData(String endDate) {
    LambdaEsQueryWrapper<EsMemberOrder> query = new LambdaEsQueryWrapper<>();
    query.le(EsMemberOrder::getCreateTime, endDate);
    Long count = esMemberOrderMapper.selectCount(query);
    return count > 0;
  }

  @Override
  protected Date getLatestdEndDate(Date endDate) {
    LambdaEsQueryWrapper<EsMemberOrder> query = new LambdaEsQueryWrapper<>();
    query.le(EsMemberOrder::getCreateTime, formatYYMMDD(endDate));
    query.orderByDesc(EsMemberOrder::getCreateTime);
    EsPageInfo<EsMemberOrder> esPageInfo = esMemberOrderMapper.pageQuery(query, 1, 1);
    if(CollectionUtils.isEmpty(esPageInfo.getList())){
      return endDate;
    }
    return esPageInfo.getList().get(0).getCreateTime();
  }
}
