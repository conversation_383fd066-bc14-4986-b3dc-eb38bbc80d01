package com.yxt.order.atom.order.es.sync.consistency_check;

import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.es.doc.EsMemberRefundOrder;
import com.yxt.order.atom.order.es.mapper.EsMemberRefundOrderMapper;
import com.yxt.order.atom.order.es.sync.AbstractOrderConsistencyCheck;
import com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count.EsMemberOfflineRefundOrderEfficientCount;
import com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count.EsMemberRefundOrderEfficientCount;
import com.yxt.order.atom.order.es.sync.member_transaction.flash.MemberOfflineRefundOrderFlash;
import com.yxt.order.atom.order.es.sync.member_transaction.flash.MemberRefundOrderFlash;
import javax.annotation.Resource;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/24 15:53
 */
@Component
public class EsMemberRefundOrderCheck extends AbstractOrderConsistencyCheck {

  @Resource
  private EsMemberRefundOrderMapper esMemberRefundOrderMapper;

  @Resource
  private EsMemberRefundOrderEfficientCount esMemberRefundOrderEfficientCount;

  @Resource
  private EsMemberOfflineRefundOrderEfficientCount esMemberOfflineRefundOrderEfficientCount;

  @Resource
  private MemberOfflineRefundOrderFlash memberOfflineRefundOrderFlash;

  @Resource
  private MemberRefundOrderFlash memberRefundOrderFlash;


  @Override
  protected Long dbDscloudCount() {
    return esMemberRefundOrderEfficientCount.fetchEfficientCount(getStartDate(),getEndDate());
  }

  @Override
  protected Long dbDscloudOfflineCount() {
    return esMemberOfflineRefundOrderEfficientCount.fetchEfficientCount(getStartDate(),getEndDate());
  }
  @Override
  protected Long esCount() {
    LambdaEsQueryWrapper<EsMemberRefundOrder> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.ge(EsMemberRefundOrder::getCreateTime, OrderDateUtils.formatYYMMDD(getStartDate()));
    queryWrapper.le(EsMemberRefundOrder::getCreateTime,OrderDateUtils.formatYYMMDD(getEndDate()));
    return esMemberRefundOrderMapper.selectCount(queryWrapper);
  }

  @Override
  protected ConsistencyNotify consistencyNotify() {
    return ConsistencyNotify.MEMBER_REFUND_ORDER;
  }

  @Override
  protected void compensate() {
    FlashParam flashParam = getFlashParam();
    memberOfflineRefundOrderFlash.startFlush(flashParam);
    memberRefundOrderFlash.startFlush(flashParam);
  }
}
