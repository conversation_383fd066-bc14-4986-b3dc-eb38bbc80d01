package com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.common.logic.consistency.AbstractConsistencyCheckEfficientCount;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.member_transaction.handler.MemberOrderTransactionHandler;
import com.yxt.order.atom.order.mapper.OrderInfoMapper;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/25 15:30
 */
@Component
public class EsMemberOrderEfficientCount extends
    AbstractConsistencyCheckEfficientCount<OrderInfoDO> {

  @Resource
  private OrderInfoMapper orderInfoMapper;

  @Resource
  private MemberOrderTransactionHandler memberOrderTransactionHandler;

  @Override
  protected Long queryCursorStartId() {
    return orderInfoMapper.selectEfficientCountMinId(getParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return orderInfoMapper.selectEfficientCountMaxId(getParam());
  }

  @Override
  protected List<OrderInfoDO> dataList() {
    LambdaQueryWrapper<OrderInfoDO> query = new LambdaQueryWrapper<>();
    query.ge(OrderInfoDO::getId, getParam().getCursorStartId());
    query.lt(OrderInfoDO::getId, getParam().currentCursorEndId(defaultLimit()));
    query.eq(OrderInfoDO::getDeleted, 0);
    return orderInfoMapper.selectList(query);
  }


  /**
   * @param list
   * @return
   */
  @Override
  protected Long efficientCount(List<OrderInfoDO> list, Long maximumId) {
    return list.stream().filter(s -> s.getId() <= maximumId)
        .filter(s -> OrderDateUtils.isEfficientDate(getParam().getStartDate(), getParam().getEndDate(), s.getCreateTime()))
        .map(DoToCanalDtoWrapper::getOrder)
        .filter(order -> memberOrderTransactionHandler.efficientData(order))
        .count();
  }
}
