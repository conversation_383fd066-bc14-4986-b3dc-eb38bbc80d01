package com.yxt.order.atom.order.es.sync.consistency_check.consistency_check_efficient_count;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.common.logic.consistency.AbstractConsistencyCheckEfficientCount;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.org_order.handler.OrgOnlineRefundHandler;
import com.yxt.order.atom.order.mapper.RefundOrderMapper;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class EsOrgOnlineRefundEfficientCount extends
    AbstractConsistencyCheckEfficientCount<RefundOrderDO> {

  @Resource
  private RefundOrderMapper refundOrderMapper;

  @Resource
  private OrgOnlineRefundHandler orgOnlineRefundHandler;


  @Override
  protected Long queryCursorStartId() {
    return refundOrderMapper.selectEfficientCountMinId(getParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return refundOrderMapper.selectEfficientCountMaxId(getParam());
  }

  @Override
  protected List<RefundOrderDO> dataList() {
    LambdaQueryWrapper<RefundOrderDO> query = new LambdaQueryWrapper<>();
    query.ge(RefundOrderDO::getId, getParam().getCursorStartId());
    query.lt(RefundOrderDO::getId, getParam().currentCursorEndId(defaultLimit()));
    return refundOrderMapper.selectList(query);
  }


  /**
   * @param list
   * @return
   */
  @Override
  protected Long efficientCount(List<RefundOrderDO> list, Long maximumId) {
    return list.stream().filter(s -> s.getId() <= maximumId)
        .filter(s -> OrderDateUtils.isEfficientDate(getParam().getStartDate(), getParam().getEndDate(), s.getCreateTime()))
        .map(DoToCanalDtoWrapper::getRefundOrder)
        .filter(refundOrder -> orgOnlineRefundHandler.efficientData(refundOrder))
        .count();
  }
}
