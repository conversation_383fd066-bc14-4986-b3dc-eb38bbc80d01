package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalB2cAccountInfo.AccountInfo;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> junfeng
 * @date 2024年11月12日 14:06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CanalB2cAccountInfo extends BaseCanalData<AccountInfo> {

  @Data
  public static class AccountInfo {

    /**
     *
     */
    @JsonProperty("id")
    private Long id;

    /**
     * 三方平台订单号
     */
    @JsonProperty("third_order_no")
    private String thirdOrderNo;

    /**
     * 系统订单号
     */
    @JsonProperty("order_no")
    private Long orderNo;

    /**
     * O2O / B2C
     */
    @JsonProperty("service_mode")
    private String serviceMode;

    /**
     * 订单类型 NORMAL-普通订单  POST_FEE-邮费单
     */
    @JsonProperty("order_type")
    private String orderType;

    /**
     * HD_H1-海典H1  HD_H2-海典H2  KC-科传
     */
    @JsonProperty("pos_mode")
    private String posMode;

    /**
     * OMS-心云作业   WMS-erp作业  O2O统一默认为 OMS
     */
    @JsonProperty("pick_type")
    private String pickType;

    /**
     * 三方平台编码：27 美团、24 饿百、11 京东到家、43 微商城、48 阿里健康、44 平安中心仓、46 平安城市仓、45 平安O2O、1001 京东健康
     */
    @JsonProperty("third_plat_code")
    private String thirdPlatCode;

    /**
     * 所属机构编码
     */
    @JsonProperty("organization_code")
    private String organizationCode;

    /**
     * 组织机构父路径id链路 1000-1100-1110-
     */
    @JsonProperty("org_parent_path")
    private String orgParentPath;

    /**
     * 子公司编码
     * */
    @JsonProperty("sub_company_code")
    private String subCompanyCode;

    /**
     * 下账机构编码 传入到pos下账的机构编码
     */
    @JsonProperty("acc_organization_code")
    private String accOrganizationCode;

    /**
     * 下账机构父路径链路
     */
    @JsonProperty("acc_org_parent_path")
    private String accOrgParentPath;

    /**
     * 下账机构店铺id
     */
    @JsonProperty("acc_online_store_id")
    private Long accOnlineStoreId;

    /**
     * 买家实付金额
     */
    @JsonProperty("buyer_actual_amount")
    private BigDecimal buyerActualAmount;

    /**
     * 商家实收金额 = 商品明细的下账金额汇总+商家配送+平台配送费+商家包装费+平台包装费+商家优惠金额+平台优惠金额+商品明细优惠+平台收取佣金
     */
    @JsonProperty("merchant_actual_receive")
    private BigDecimal merchantActualReceive;

    /**
     * 商品总额 = 商品明细的商品金额汇总
     */
    @JsonProperty("goods_total_amount")
    private BigDecimal goodsTotalAmount;

    /**
     * 下账商品总金额
     */
    @JsonProperty("bill_commodity_amount")
    private BigDecimal billCommodityAmount;

    /**
     * 商家配送费，金额大于等于0
     */
    @JsonProperty("delivery_fee")
    private BigDecimal deliveryFee;

    /**
     * 商家包装费，金额大于等于0
     */
    @JsonProperty("package_fee")
    private BigDecimal packageFee;

    /**
     * 商家优惠金额
     */
    @JsonProperty("merchant_discount")
    private BigDecimal merchantDiscount;

    /**
     * 医保金额
     */
    @JsonProperty("medicare_amount")
    private BigDecimal medicareAmount;

    /**
     * 支付方式编码
     */
    @JsonProperty("pay_code")
    private String payCode;

    /**
     * 支付渠道 如微信支付
     */
    @JsonProperty("pay_channel")
    private String payChannel;

    /**
     * 订单接单时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonProperty("order_accept_time")
    private Date orderAcceptTime;

    /**
     * 订单操作人  O2O-拣货人 id  B2C-发货人id
     */
    @JsonProperty("order_operator_id")
    private String orderOperatorId;

    /**
     * 微商城会员编号
     */
    @JsonProperty("member_no")
    private String memberNo;

    /**
     * B2C订单下账专用字段 成本中心编码
     */
    @JsonProperty("cost_center_code")
    private String costCenterCode;

    /**
     * 下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败
     */
    @JsonProperty("state")
    private String state;

    /**
     * 下账时间
     */
    @JsonProperty("account_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date accountTime;

    /**
     * erp零售流水号 下账成功返回
     */
    @JsonProperty("sale_no")
    private String saleNo;

    /**
     * 下账失败原因 下账失败返回
     */
    @JsonProperty("account_err_msg")
    private String accountErrMsg;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonProperty("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 是否删除 0-未删除 时间戳-已删除
     */
    @JsonProperty("deleted")
    private Long deleted;

    /**
     * 数据版本，每次update+1
     */
    private Long version;
  }
}


