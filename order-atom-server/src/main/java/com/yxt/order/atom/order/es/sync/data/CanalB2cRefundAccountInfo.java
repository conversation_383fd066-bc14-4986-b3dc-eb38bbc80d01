package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalB2cRefundAccountInfo.RefundAccountInfo;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> junfeng
 * @date 2024年11月12日 14:06
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CanalB2cRefundAccountInfo extends BaseCanalData<RefundAccountInfo> {

  @Data
  public static class RefundAccountInfo {

    /**
     *
     */
    @JsonProperty("id")
    private Long id;

    /**
     * 系统退款单号
     */
    @JsonProperty("refund_no")
    private Long refundNo;

    /**
     * 系统订单号
     */
    @JsonProperty("order_no")
    private Long orderNo;

    /**
     * 平台退款单号
     */
    @JsonProperty("third_refund_no")
    private String thirdRefundNo;

    /**
     * 三方平台编码：27 美团、24 饿百、11 京东到家、43 微商城、48 阿里健康、44 平安中心仓、46 平安城市仓、45 平安O2O、1001 京东健康
     */
    @JsonProperty("third_plat_code")
    private String thirdPlatCode;

    /**
     * O2O / B2C
     */
    @JsonProperty("service_mode")
    private String serviceMode;

    /**
     * HD_H1-海典H1  HD_H2-海典H2  KC-科传
     */
    @JsonProperty("pos_mode")
    private String posMode;

    /**
     * OMS-心云作业   WMS-erp作业  O2O统一默认为 OMS
     */
    @JsonProperty("pick_type")
    private String pickType;

    /**
     * 所属机构编码
     */
    @JsonProperty("organization_code")
    private String organizationCode;


    /**
     * 子公司编码
     */
    @JsonProperty("sub_company_code")
    private String subCompanyCode;


    /**
     * 组织机构父路径id链路 1000-1100-1110-
     */
    @JsonProperty("org_parent_path")
    private String orgParentPath;

    /**
     * 下账机构编码 传入到pos下账的机构编码
     */
    @JsonProperty("acc_organization_code")
    private String accOrganizationCode;

    /**
     * 下账机构父路径链路
     */
    @JsonProperty("acc_org_parent_path")
    private String accOrgParentPath;

    /**
     * 下账机构店铺id
     */
    @JsonProperty("acc_online_store_id")
    private Long accOnlineStoreId;

    /**
     * 退货类型 ：仅退款-ONLY_REFUND 退货退款- ALL_REFUND
     */
    @JsonProperty("refund_type")
    private String refundType;

    /**
     * 退款类型 PART-部分退款 ALL-全额退款
     */
    @JsonProperty("type")
    private String type;

    /**
     * 退款总金额 = 退款商品明细的退款金额汇总+商家配送费退款金额+平台配送费退款金额+商家包装费退款金额+平台包装费退款金额+佣金退款金额+商家优惠退款金额+平台优惠退款金额+商品明细优惠退款金额
     */
    @JsonProperty("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 下账退款商品总金额
     */
    @JsonProperty("refund_goods_total")
    private BigDecimal refundGoodsTotal;

    /**
     * 商家配送费，金额大于等于0
     */
    @JsonProperty("refund_post_fee")
    private BigDecimal refundPostFee;

    /**
     * 商品明细优惠
     */
    @JsonProperty("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 订单总额 = 商品明细的商品金额汇总+商家配送+平台配送费+商家包装费+平台包装费
     */
    @JsonProperty("goods_total_amount")
    private BigDecimal packageFee;

    /**
     * 退款单接收时间
     */
    @JsonProperty("refund_accept_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date refundAcceptTime;

    /**
     * 退款理由
     */
    @JsonProperty("refund_reason")
    private String refundReason;


    /**
     * 下账状态 WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败
     */
    @JsonProperty("state")
    private String state;

    /**
     * erp零售流水号 下账成功返回
     */
    @JsonProperty("sale_no")
    private String saleNo;

    /**
     * 下账时间
     */
    @JsonProperty("account_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date accountTime;

    /**
     * 下账失败原因 下账失败返回
     */
    @JsonProperty("account_err_msg")
    private String accountErrMsg;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonProperty("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 是否删除 0-未删除 时间戳-已删除
     */
    @JsonProperty("deleted")
    private Long deleted;

    /**
     * 数据版本，每次update+1
     */
    @JsonProperty("version")
    private Long version;
  }
}


