package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrder.OfflineOrder;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月20日 14:06
 * @email: <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CanalOfflineOrder extends BaseCanalData<OfflineOrder> {

  @Data
  public static class OfflineOrder {

    @JsonProperty("order_no")
    private String orderNo;

    @JsonProperty("store_code")
    private String storeCode;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("order_state")
    private String orderState;

    @JsonProperty("third_order_no")
    private String thirdOrderNo;

    @JsonProperty("third_platform_code")
    private String thirdPlatformCode;

    @JsonProperty("created")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date created;

    @JsonProperty("created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    @JsonProperty("pay_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;

    private Boolean needRoute = true;

    @JsonProperty("migration")
    private String migration;

    @JsonProperty("actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 返回是否是迁移订单
     * @return
     */
    public Boolean migrateOrder(){
//      return "true".equalsIgnoreCase(this.migration); // 跳过迁移订单处理
      return false; // 关闭判断,则处理迁移订单
    }

  }
}
