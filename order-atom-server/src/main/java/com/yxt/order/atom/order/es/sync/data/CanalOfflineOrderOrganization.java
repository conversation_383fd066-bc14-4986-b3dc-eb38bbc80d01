package com.yxt.order.atom.order.es.sync.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.common.logic.canal.BaseCanalData;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineOrderOrganization.OfflineOrderOrganization;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月20日 14:06
 * @email: <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CanalOfflineOrderOrganization extends BaseCanalData<OfflineOrderOrganization> {

  @Data
  public static class OfflineOrderOrganization {

    @JsonProperty("order_no")
    private String orderNo;

  }
}
