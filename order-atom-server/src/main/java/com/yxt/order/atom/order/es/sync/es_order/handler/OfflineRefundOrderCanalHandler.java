package com.yxt.order.atom.order.es.sync.es_order.handler;

import static com.yxt.order.types.canal.Table.OFFLINE_REFUND_ORDER_REGEX;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.common.utils.OrderDateUtils;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.es.sync.clean.ExpireDaysConstant;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder.OfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel.EsOrderItemModel;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderDetailMapper;
import com.yxt.order.common.exception.DetailNotExistsException;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.es_order.EsOrderStatus;
import com.yxt.order.types.es_order.EsOrderType;
import com.yxt.order.types.es_order.EsServiceMode;
import com.yxt.order.types.offline.enums.RefundStateEnum;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年08月20日 14:21
 * @email: <EMAIL>
 */
@Component
public class OfflineRefundOrderCanalHandler extends
    AbstractCanalHandler<CanalOfflineRefundOrder, EsOrderIndexModel> {

  @Resource
  private OfflineRefundOrderDetailMapper offlineRefundOrderDetailMapper;

  public OfflineRefundOrderCanalHandler() {
    super(CanalOfflineRefundOrder.class);
  }

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD_OFFLINE.equals(database) && Table.tableRegex(OFFLINE_REFUND_ORDER_REGEX,
        table);
  }

  @Override
  protected List<EsOrderIndexModel> assemble() {

    List<OfflineRefundOrder> offlineRefundOrderList = getData().getData();
    if (CollectionUtils.isEmpty(offlineRefundOrderList)) {
      return Lists.newArrayList();
    }

    return offlineRefundOrderList.stream()
        .filter(offlineRefundOrder -> !offlineRefundOrder.migrateRefundOrder())
        .filter(offlineOrder -> !OrderDateUtils.isExpired(offlineOrder.getCreated(),
            ExpireDaysConstant.EsOrderEfficientDays))
        .filter(
            offlineRefundOrder -> offlineRefundDoneStatus(offlineRefundOrder.getRefundState()))
        .filter(
            offlineRefundOrder -> !StringUtils.isEmpty(offlineRefundOrder.getUserId()))
        .filter(offlineRefundOrder -> ThirdPlatformCodeEnum.isValid(offlineRefundOrder.getThirdPlatformCode()))
        .map(offlineRefundOrder -> {

          EsOrderIndexModel esOrderModel = new EsOrderIndexModel();
          esOrderModel.setOrderNumber(offlineRefundOrder.getRefundNo());
          esOrderModel.setRefundNo(offlineRefundOrder.getRefundNo());
          esOrderModel.setOrderNo(offlineRefundOrder.getOrderNo());
          esOrderModel.setOrganizationCode(
              offlineRefundOrder.getStoreCode());// 这里只设置OrganizationCode,不设置onlineStore,线下单没有线上门店
          esOrderModel.setUserId(offlineRefundOrder.getUserId());
          esOrderModel.setEsOrderType(EsOrderType.REFUND);
          esOrderModel.setServiceMode(EsServiceMode.POS);
          esOrderModel.setEsOrderStatus(mappingEsOrderStatus(offlineRefundOrder.getRefundState()));
//          esOrderModel.setPayTime();
          esOrderModel.setCompleteTime(offlineRefundOrder.getCompleteTime());
          esOrderModel.setThirdOrderNo(offlineRefundOrder.getThirdOrderNo());
          esOrderModel.setThirdRefundNo(offlineRefundOrder.getThirdRefundNo());
          esOrderModel.setPlatformCode(offlineRefundOrder.getThirdPlatformCode());
          esOrderModel.setCreateTime(offlineRefundOrder.getCreated());

          if (offlineRefundOrder.getNeedRoute()) {
            try (HintManager hintManager = HintManager.getInstance()) {
              OfflineOrderHit hit = new OfflineOrderHit();
              hit.setDefineNo(esOrderModel.getOrderNumber());
              OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
              bindOfflineRefundOrderDetail(esOrderModel);
            }
          }else {
            bindOfflineRefundOrderDetail(esOrderModel);// 手动刷数,已经在抽象层路由了,不需要在执行路由
          }

          return esOrderModel;
        }).collect(Collectors.toList());
  }

  private void bindOfflineRefundOrderDetail(EsOrderIndexModel esOrderModel) {
    List<OfflineRefundOrderDetailDO> offlineRefundDetailList = getRefundOrderDetailDOList(
        esOrderModel);

    esOrderModel.setEsOrderItemModelList(offlineRefundDetailList.stream().map(detail -> {
      EsOrderItemModel itemModel = new EsOrderItemModel();
      itemModel.setCommodityCode(detail.getErpCode());
      itemModel.setCommodityName(detail.getErpName());
      itemModel.setCommodityCount(detail.getRefundCount());
      itemModel.setFiveClass(detail.getFiveClass());
      return itemModel;
    }).collect(Collectors.toList()));
  }

  @Retryable(value = DetailNotExistsException.class, maxAttempts = 8, backoff = @Backoff(delay = 2000, multiplier = 1.5))
  public List<OfflineRefundOrderDetailDO> getRefundOrderDetailDOList(
      EsOrderIndexModel esOrderModel) {
    LambdaQueryWrapper<OfflineRefundOrderDetailDO> refundDetailQuery = new LambdaQueryWrapper<>();
    refundDetailQuery.eq(OfflineRefundOrderDetailDO::getRefundNo,
        esOrderModel.getOrderNumber());
    List<OfflineRefundOrderDetailDO> offlineRefundDetailList = offlineRefundOrderDetailMapper.selectList(
        refundDetailQuery);

    if (CollectionUtils.isEmpty(offlineRefundDetailList)) {
      String error = String.format("线下单-退单明细不存在,%s", esOrderModel.getOrderNumber());
      throw new DetailNotExistsException(error);
    }
    return offlineRefundDetailList;
  }

  private EsOrderStatus mappingEsOrderStatus(String refundStatus) {
    if (offlineRefundDoneStatus(refundStatus)) {
      return EsOrderStatus.DONE;
    }
    return null;
  }

  private boolean offlineRefundDoneStatus(String refundStatus) {
    return RefundStateEnum.REFUNDED.name().equals(refundStatus);
  }
}
