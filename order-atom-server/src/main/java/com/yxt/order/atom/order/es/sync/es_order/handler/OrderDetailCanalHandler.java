package com.yxt.order.atom.order.es.sync.es_order.handler;
import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.es.sync.data.CanalOrderDetail;
import com.yxt.order.atom.order.es.sync.data.CanalOrderDetail.OrderDetail;
import com.yxt.order.atom.order.es.sync.es_order.flash.EsOrderFlashOrderInfo;
import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月20日 14:21
 * @email: <EMAIL>
 */
@Component
public class OrderDetailCanalHandler extends
    AbstractCanalHandler<CanalOrderDetail,EsOrderIndexModel> {

  @Resource
  private EsOrderFlashOrderInfo esOrderFlashOrderInfo;

  public OrderDetailCanalHandler() {
    super(CanalOrderDetail.class);
  }

  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD.equals(database) && table.equals(Table.ORDER_INFO_DETAIL);
  }

  @Override
  protected List<EsOrderIndexModel> assemble() {
    for (OrderDetail orderDetail : getData().getData()) {
      if (StringUtils.isEmpty(orderDetail.getOrderNo())) {
        continue;
      }

      FlashParam param = new FlashParam();
      param.setNoList(Lists.newArrayList(orderDetail.getOrderNo()));
      esOrderFlashOrderInfo.startFlush(param);
    }
    return Lists.newArrayList();
  }


}
