package com.yxt.order.atom.order.es.sync.es_order.service;

import com.yxt.order.atom.order.es.sync.es_order.model.EsOrderIndexModel;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月07日 18:04
 * @email: <EMAIL>
 */
public interface EsOrderModelService {

  /**
   * 创建
   *
   * @param esOrderModel
   * @return
   */
  Boolean createEsOrder(EsOrderIndexModel esOrderModel);


  Boolean saveEsOrder(EsOrderIndexModel esOrderModel);

  Boolean deleteEsOrder(EsOrderIndexModel esOrderModel);

}
