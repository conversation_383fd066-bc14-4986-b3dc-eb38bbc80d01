package com.yxt.order.atom.order.es.sync.member_transaction.dp;

import lombok.Getter;

/**
 * @author: moatkon
 * @time: 2024/12/9 14:24
 */
@Getter
public enum MemberOrderStatus {
  //5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭
  AWAITING_HANDLE(5, "待处理"),
  AWAITING_ACCEPT(10, "待接单"),
  AWAITING_PICK(20, "待拣货"),
  AWAITING_DELIVERY(30, "待配送"),
  AWAITING_RECEIPT(40, "待收货"),
  COMPLETED(100, "已完成"),
  CANCELLED(102, "已取消"),
  CLOSED(101, "已关闭"),
  ;
  private final int status;
  private final String desc;

  MemberOrderStatus(int status, String desc) {
    this.status = status;
    this.desc = desc;
  }

  /**
   *
   * @param order_info.order_state 5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭
   * @return MemberOrderStatus.status
   */
  public static Integer getByStatus(String orderState) {
    for (MemberOrderStatus memberOrderStatus : values()) {
      if(String.valueOf(memberOrderStatus.status).equals(orderState)){
        return memberOrderStatus.status;
      }
    }
    return null;
  }
}
