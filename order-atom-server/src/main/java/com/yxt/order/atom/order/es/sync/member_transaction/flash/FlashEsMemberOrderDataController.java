package com.yxt.order.atom.order.es.sync.member_transaction.flash;

import static com.yxt.order.atom.migration.config.ThreadPoolConfig.COMMON_BUSINESS_POOL;

import com.google.common.collect.Sets;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.AbstractFlash.CustomData;
import com.yxt.order.atom.order.es.sync.MemberTransaction;
import com.yxt.order.atom.order.es.sync.member_transaction.flash.req.FlashMemberOrderDataReq;
import com.yxt.order.atom.order.es.sync.member_transaction.flash.req.FlashMemberOrderDataReq.FlashType;
import com.yxt.starter.controller.AbstractController;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: moatkon
 * @time: 2025/3/31 16:52
 */
@RestController
@Slf4j
public class FlashEsMemberOrderDataController extends AbstractController {

  public static final String URL = "/2.0/es-member-order/flashDataToEs";

  @Qualifier(COMMON_BUSINESS_POOL)
  @Resource
  private ThreadPoolExecutor commonBusinessPool;

  @Resource
  private List<AbstractFlash<?, ?, MemberTransaction>> abstractFlashList;


  @PostMapping(URL)
  public ResponseBase<Boolean> memberOrderFlashDataToEs(@RequestBody @Valid FlashMemberOrderDataReq req) {
    FlashType flashType = req.getFlashType();
    for (AbstractFlash<?, ?, MemberTransaction> abstractFlash : abstractFlashList) {
      // 当前类
      Set<String> currentClazzSet = Sets.newHashSet(abstractFlash.getClass().getName(),
          abstractFlash.getClass().getSuperclass().getName());

      // 是否包含线上实例
      boolean isContainOnlineInstance =
          currentClazzSet.contains(MemberOrderInfoFlash.class.getName())
              || currentClazzSet.contains(
              MemberRefundOrderFlash.class.getName());

      if (FlashType.ONLINE.equals(flashType) && isContainOnlineInstance) {
        commitTask(req, abstractFlash, null);
      } else if (FlashType.OFFLINE.equals(flashType) && !isContainOnlineInstance) {
        List<String> shardingValueList = req.getShardingValueList();
        if (CollectionUtils.isEmpty(shardingValueList)) {
          throw new RuntimeException("线下订单,shardingValueList 不能为空");
        }
        CustomData customData = new CustomData();
        customData.setShardingValueList(shardingValueList);
        customData.setOfflineDataBase(req.getOfflineDataBaseEnum());
        commitTask(req, abstractFlash, customData);
      }
    }

    return generateSuccess(Boolean.TRUE);
  }

  private void commitTask(FlashMemberOrderDataReq req,
      AbstractFlash<?, ?, MemberTransaction> abstractFlash, CustomData customData) {
    Date startDate = req.getStartDate();
    Date endDate = req.getEndDate();
    List<String> noList = req.getNoList();

    commonBusinessPool.submit(() -> {
      try {
        log.info("flush task running,{},req:{}", abstractFlash.getClass().getName(),
            JsonUtils.toJson(req));
        FlashParam flashParam = new FlashParam();
        flashParam.setStart(startDate);
        flashParam.setEnd(endDate);
        flashParam.setNoList(noList);
        flashParam.setMonitorKey(req.getMonitorKey());
        // 自定义数据
        abstractFlash.customData(customData);
        abstractFlash.startFlush(flashParam);
        log.info("flush task done,{},req:{}", abstractFlash.getClass().getName(),
            JsonUtils.toJson(flashParam));
      } catch (Exception e) {
        log.error("MemberTransaction flash error", e);
      }
    });
  }

}
