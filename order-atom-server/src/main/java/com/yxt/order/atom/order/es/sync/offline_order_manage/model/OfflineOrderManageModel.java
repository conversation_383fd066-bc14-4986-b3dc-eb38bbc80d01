package com.yxt.order.atom.order.es.sync.offline_order_manage.model;

import com.yxt.common.logic.es.BaseEsIndexModel;
import com.yxt.order.atom.order.es.doc.EsOfflineOrderManage;
import com.yxt.order.atom.order.es.doc.EsOfflineOrderManageDetail;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2025/4/1 10:55
 */
@Data
public class OfflineOrderManageModel extends BaseEsIndexModel {

  /**
   * 系统单号
   */

  private String orderNo;

  /**
   * POS
   *
   * @see ThirdPlatformCodeEnum
   */

  private String thirdPlatformCode;

  /**
   * 平台单号
   */

  private String thirdOrderNo;

  /**
   * 门店
   */

  private String storeCode;

  /**
   * 下单时间
   */

  private Date created;

  /**
   * 公司编码
   */
  private String companyCode;
  /**
   * 实付金额
   */
  private BigDecimal actualPayAmount;


  /**
   * 退单商品明细
   */
  private List<OfflineOrderManageDetailModel> offlineOrderManageDetailModelList;

  public EsOfflineOrderManage create() {
    EsOfflineOrderManage manage = new EsOfflineOrderManage();
    manage.setId(defineId());
    manage.setOrderNo(this.getOrderNo());
    manage.setThirdPlatformCode(this.getThirdPlatformCode());
    manage.setThirdOrderNo(this.getThirdOrderNo());
    manage.setStoreCode(this.getStoreCode());
    manage.setCreated(this.getCreated());
    manage.setCompanyCode(this.getCompanyCode());
    manage.setActualPayAmount(this.getActualPayAmount());
    if (!CollectionUtils.isEmpty(this.offlineOrderManageDetailModelList)) {
      manage.setOfflineOrderManageDetailList(
          this.offlineOrderManageDetailModelList.stream().map(item -> {
            EsOfflineOrderManageDetail detailModel = new EsOfflineOrderManageDetail();
            detailModel.setErpCode(item.getErpCode());
            return detailModel;
          }).collect(Collectors.toList()));
    }

    return manage;
  }

  /**
   * 每个分表内orderNo是唯一的,不涉及到线上单,所以可以直接使用业务唯一键
   *
   * @return
   */

  public String defineId() {
    return this.getOrderNo();
  }


  @Data
  public static class OfflineOrderManageDetailModel {

    private String erpCode;


  }
}
