package com.yxt.order.atom.order.es.sync.oms_order_info.flash;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.OmsOrderInfoDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.OmsOrderTransaction;
import com.yxt.order.atom.order.es.sync.data.CanalOmsOrderInfo;
import com.yxt.order.atom.order.es.sync.data.CanalOmsOrderInfo.OmsOrderInfo;
import com.yxt.order.atom.order.es.sync.oms_order_info.handler.OmsOrderInfoCanalHandler;
import com.yxt.order.atom.order.mapper.OmsOrderInfoMapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/14
 * @since 1.0
 * OmsOrderInfo同步ES
 */
@Component
public class OmsOrderInfoFlash extends AbstractFlash<OmsOrderInfoDO, OmsOrderInfo, OmsOrderTransaction> {

    @Resource
    private OmsOrderInfoMapper omsOrderInfoMapper;

    @Resource
    private OmsOrderInfoCanalHandler omsOrderInfoCanalHandler;

    @Override
    protected Long queryCursorStartId() {
        return omsOrderInfoMapper.selectMinId(getFlashParam());
    }

    @Override
    protected Long queryCursorEndId() {
        return omsOrderInfoMapper.selectMaxId(getFlashParam());
    }

    @Override
    protected List<OmsOrderInfoDO> getSourceList() {
        LambdaQueryWrapper<OmsOrderInfoDO> queryWrapper = FlashQueryWrapper.omsOrderInfoFlashQuery(getFlashParam(), defaultLimit());
        return omsOrderInfoMapper.selectList(queryWrapper);
    }

    @Override
    protected List<OmsOrderInfo> assembleTargetData(List<OmsOrderInfoDO> omsOrderInfoDOS) {
        return omsOrderInfoDOS.stream().map(DoToCanalDtoWrapper::getOmsOrderInfo).collect(Collectors.toList());
    }

    @Override
    protected void flash(List<OmsOrderInfo> omsOrderInfos) {
        CanalOmsOrderInfo canalOmsOrderInfo = new CanalOmsOrderInfo();
        canalOmsOrderInfo.setData(omsOrderInfos);
        omsOrderInfoCanalHandler.manualFlash(canalOmsOrderInfo);
    }
}
