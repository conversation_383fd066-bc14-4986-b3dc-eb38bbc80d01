package com.yxt.order.atom.order.es.sync.oms_order_info.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.entity.OmsOrderInfoDO;
import com.yxt.order.atom.order.es.sync.data.CanalPlatformOrderInfo;
import com.yxt.order.atom.order.es.sync.data.CanalPlatformOrderInfo.PlatformOrderInfo;
import com.yxt.order.atom.order.es.sync.oms_order_info.EsOmsOrderInfoModel;
import com.yxt.order.atom.order.es.sync.oms_order_info.flash.OmsOrderInfoFlash;
import com.yxt.order.atom.order.mapper.OmsOrderInfoMapper;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
/**
 * @author: moatkon
 * @time: 2024/11/4 17:50
 */
@Component
@Slf4j
public class PlatformOrderInfoCanalHandler extends
    AbstractCanalHandler<CanalPlatformOrderInfo, EsOmsOrderInfoModel> {

  @Resource
  private OmsOrderInfoMapper omsOrderInfoMapper;

  @Resource
  private OmsOrderInfoFlash omsOrderInfoFlash;


  public PlatformOrderInfoCanalHandler() {
    super(CanalPlatformOrderInfo.class);
  }


  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD.equals(database) && table.equals(Table.PLATFORM_ORDER_INFO);
  }

  @Override
  protected List<EsOmsOrderInfoModel> assemble() {
    List<PlatformOrderInfo> platformOrderInfoList = getData().getData();
    if (CollectionUtils.isEmpty(platformOrderInfoList)) {
      return Lists.newArrayList();
    }

    LambdaQueryWrapper<OmsOrderInfoDO> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(OmsOrderInfoDO::getThirdOrderNo, platformOrderInfoList.get(0).getThirdOrderNo());
    queryWrapper.eq(OmsOrderInfoDO::getThirdPlatformCode, platformOrderInfoList.get(0).getThirdPlatformCode());
    queryWrapper.eq(OmsOrderInfoDO::getIsPostFeeOrder, 0);
    queryWrapper.eq(OmsOrderInfoDO::getDeleted, 0);
    OmsOrderInfoDO omsOrderInfoDO = omsOrderInfoMapper.selectOne(queryWrapper);

    FlashParam param = new FlashParam();
    param.setNoList(Lists.newArrayList(omsOrderInfoDO.getOmsOrderNo().toString()));
    omsOrderInfoFlash.startFlush(param);
    return Lists.newArrayList();
  }


}
