package com.yxt.order.atom.order.es.sync.operate;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yxt.common.logic.es.AbstractEsOperate;
import com.yxt.order.atom.order.es.doc.EsOrgRefund;
import com.yxt.order.atom.order.es.mapper.EsOrgRefundMapper;
import com.yxt.order.atom.order.es.sync.org_order.model.OrgRefundModel;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OrgRefundModelOperate extends AbstractEsOperate<OrgRefundModel> {

  @Resource
  private EsOrgRefundMapper esOrgRefundMapper;

  public OrgRefundModelOperate() {
    super(OrgRefundModel.class);
  }

  @Override
  protected Boolean exec() {
    if (Type.DELETE.equals(getType())) {
      return delete(getT());
    } else if (Type.SAVE.equals(getType())) {
      return save(getT());
    }

    return false;
  }

  private Boolean save(OrgRefundModel model) {
    // 适配逻辑删除
    if (Objects.nonNull(model.getDeleted()) && model.getDeleted() != 0L) {// 不为0,表示删除
      return delete(model);
    }

    EsOrgRefund esOrgRefund = model.create();
    LambdaEsQueryWrapper<EsOrgRefund> wrapper = new LambdaEsQueryWrapper<>();
    wrapper.eq(EsOrgRefund::getId, model.defineId());
    wrapper.eq(EsOrgRefund::getDeleted, 0L);
    //先添加路由查找,如果能查找到，直接更新
    wrapper.routing(model.routeKey());
    Long count = esOrgRefundMapper.selectCount(wrapper);
    if(count > 0){
      esOrgRefundMapper.updateById(model.routeKey(), esOrgRefund);
      return true;
    }
    //查不到则需要根据id查一次
    EsOrgRefund existEsRefund = esOrgRefundMapper.selectById(model.defineId());
    if(ObjectUtil.isNull(existEsRefund)){
      esOrgRefundMapper.insert(model.routeKey(), esOrgRefund);
      return true;
    }
    //判断orgCode是否发生了变更
    if(StrUtil.equalsIgnoreCase(existEsRefund.getOrgCode(), model.getOrgCode())){
      esOrgRefundMapper.updateById(model.routeKey(), esOrgRefund);
      return true;
    }
    //如果orgCode发生了变更，此时插入会导致ES中存在两条订单，所以需要删除原来的数据
    esOrgRefundMapper.deleteById(existEsRefund.getOrgCode(), model.defineId());
    //重新插入
    esOrgRefundMapper.insert(model.routeKey(), esOrgRefund);
    return true;
  }

  private Boolean delete(OrgRefundModel model) {
    return esOrgRefundMapper.deleteById(model.routeKey(), model.defineId()) > 0;
  }
}
