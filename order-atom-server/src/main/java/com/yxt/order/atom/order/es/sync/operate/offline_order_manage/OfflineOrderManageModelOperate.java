package com.yxt.order.atom.order.es.sync.operate.offline_order_manage;

import com.yxt.common.logic.es.AbstractEsOperate;
import com.yxt.order.atom.order.es.doc.EsOfflineOrderManage;
import com.yxt.order.atom.order.es.mapper.EsOfflineOrderManageMapper;
import com.yxt.order.atom.order.es.sync.offline_order_manage.model.OfflineOrderManageModel;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2024/12/10 15:03
 */
@Component
@Slf4j
public class OfflineOrderManageModelOperate extends AbstractEsOperate<OfflineOrderManageModel> {

  @Resource
  private EsOfflineOrderManageMapper esOfflineOrderManageMapper;

  public OfflineOrderManageModelOperate() {
    super(OfflineOrderManageModel.class);
  }

  @Override
  protected Boolean exec() {

    if (Type.DELETE.equals(getType())) {
      return delete(getT());
    } else if (Type.SAVE.equals(getType())) {
      return save(getT());
    }

    return false;
  }


  private Boolean delete(OfflineOrderManageModel memberOrderModel) {
    return esOfflineOrderManageMapper.deleteById(memberOrderModel.defineId()) > 0;
  }

  private Boolean save(OfflineOrderManageModel memberOrderModel) {
    EsOfflineOrderManage esMemberOrder = memberOrderModel.create();
    LambdaEsQueryWrapper<EsOfflineOrderManage> memberOrderQuery = new LambdaEsQueryWrapper<>();
    memberOrderQuery.eq(EsOfflineOrderManage::getId, memberOrderModel.defineId());
    Long count = esOfflineOrderManageMapper.selectCount(memberOrderQuery);
    if (count > 0) {
      return esOfflineOrderManageMapper.updateById(esMemberOrder) > 0;
    } else {
      return esOfflineOrderManageMapper.insert(esMemberOrder) > 0;

    }
  }
}
