package com.yxt.order.atom.order.es.sync.org_order.flash;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.es.sync.AbstractFlashEnhance;
import com.yxt.order.atom.order.es.sync.DoToCanalDtoWrapper;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.es.sync.OrgOrderScene;
import com.yxt.order.atom.order.es.sync.data.CanalOrderInfo;
import com.yxt.order.atom.order.es.sync.data.CanalOrderInfo.Order;
import com.yxt.order.atom.order.es.sync.org_order.handler.OrgOnlineOrderHandler;
import com.yxt.order.atom.order.mapper.OrderInfoMapper;
import com.yxt.order.types.offline.NumberType;
import com.yxt.order.types.order.enums.OrderSource;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class OrgOnlineOrderFlash extends AbstractFlashEnhance<OrderInfoDO, Order, OrgOrderScene> {

  @Resource
  private OrderInfoMapper orderInfoMapper;

  @Resource
  private OrgOnlineOrderHandler orgOnlineOrderHandler;


  @Override
  protected Long queryCursorStartId() {
    return orderInfoMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return orderInfoMapper.selectMaxId(getFlashParam());
  }

  /**
   * 获取数据源
   *
   * @return
   */
  @Override
  protected List<OrderInfoDO> getSourceList() {
    LambdaQueryWrapper<OrderInfoDO> query = FlashQueryWrapper.orderInfoFlashQuery(getFlashParam(),defaultLimit());
    return orderInfoMapper.selectList(query);
  }


  /**
   * 组装Canal目标数据
   *
   * @param orderInfoDOS DB数据,需要转成目标CanalData
   * @return canalData
   */
  @Override
  protected List<Order> assembleTargetData(List<OrderInfoDO> orderInfoDOS) {
    return orderInfoDOS.stream().map(DoToCanalDtoWrapper::getOrder).collect(Collectors.toList());
  }

  /**
   * 刷数
   *
   * @param orders
   */
  @Override
  protected void flash(List<Order> orders) {
    CanalOrderInfo canalOrderInfo = new CanalOrderInfo();
    canalOrderInfo.setData(orders);
    orgOnlineOrderHandler.manualFlash(canalOrderInfo);
  }

  @Override
  public OrderSource getOrderSource() {
    return OrderSource.ONLINE;
  }

  @Override
  public NumberType getOrderType() {
    return NumberType.ORDER;
  }
}
