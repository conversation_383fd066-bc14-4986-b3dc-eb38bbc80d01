package com.yxt.order.atom.order.es.sync.org_order.flash;

import static com.yxt.order.atom.migration.config.ThreadPoolConfig.COMMON_BUSINESS_POOL;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.lang.exception.YxtParamException;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.entity.OrgOrderFlashToEsJobDO;
import com.yxt.order.atom.order.es.sync.AbstractFlashEnhance;
import com.yxt.order.atom.order.es.sync.OrgOrderScene;
import com.yxt.order.atom.order.repository.batch.OrgOrderFlashToEsJobBatchRepository;
import com.yxt.order.atom.sdk.order_info.req.FlashDataToEsReq;
import com.yxt.order.atom.sdk.org_order.req.OrgOrderFlashToEsJobProcessReqDTO;
import com.yxt.order.atom.sdk.org_order.req.OrgOrderFlashToEsWithJobReqDTO;
import com.yxt.order.types.order.enums.OrderSource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class OrgOrderFlashHandler {

  @Resource
  private List<AbstractFlashEnhance<?, ?, OrgOrderScene>> orgOrderFlashList;

  @Resource
  private OrgOrderFlashToEsJobBatchRepository orgOrderFlashToEsJobBatchRepository;

  @Qualifier(COMMON_BUSINESS_POOL)
  @Resource
  private ThreadPoolExecutor commonBusinessPool;

  @SneakyThrows
  public void orgOrderFlashDataToEsWithJobProcess(OrgOrderFlashToEsJobProcessReqDTO request) {
    for (int i = 0; i < request.getProcessBatchSize(); i++) {
      List<OrgOrderFlashToEsJobDO> jobList = orgOrderFlashToEsJobBatchRepository.list(Wrappers.<OrgOrderFlashToEsJobDO>lambdaQuery()
          .eq(OrgOrderFlashToEsJobDO::getStatus, request.getJobStatus())
          .eq(StrUtil.isNotBlank(request.getOrderSource()), OrgOrderFlashToEsJobDO::getOrderSource, request.getOrderSource())
          .eq(StrUtil.isNotBlank(request.getOrderType()), OrgOrderFlashToEsJobDO::getOrderType, request.getOrderType())
          .ge(ObjectUtil.isNotNull(request.getFlashStartTime()), OrgOrderFlashToEsJobDO::getFlashStartTime, request.getFlashStartTime())
          .le(ObjectUtil.isNotNull(request.getFlashEndTime()), OrgOrderFlashToEsJobDO::getFlashEndTime, request.getFlashEndTime())
          .last(" limit " + request.getProcessSize())
          .orderByDesc(OrgOrderFlashToEsJobDO::getId)
      );
      if(CollUtil.isEmpty(jobList)){
        break;
      }
      //更新为处理中
      jobList.forEach(job-> job.setStatus(2));
      orgOrderFlashToEsJobBatchRepository.updateBatchById(jobList);
      //每个job匹配对应的处理器
      Map<OrgOrderFlashToEsJobDO,List<AbstractFlashEnhance<?, ?, OrgOrderScene>>> jobFlashMap = new HashMap<>();
      for (OrgOrderFlashToEsJobDO job : jobList) {
        List<AbstractFlashEnhance<?, ?, OrgOrderScene>> flashList = orgOrderFlashList.stream()
            .filter(flash -> {
              if (StrUtil.isAllBlank(job.getOrderSource(), job.getOrderType())) {
                return true;
              }
              if (StrUtil.isNotBlank(job.getOrderSource())) {
                if (!job.getOrderSource().equals(flash.getOrderSource().name())) {
                  return false;
                }
              }
              if (StrUtil.isNotBlank(job.getOrderType())) {
                if (!job.getOrderType().equals(flash.getOrderType().name())) {
                  return false;
                }
              }
              return true;
            }).collect(Collectors.toList());
        jobFlashMap.put(job, flashList);
      }
      //刷数据
      Set<Entry<OrgOrderFlashToEsJobDO, List<AbstractFlashEnhance<?, ?, OrgOrderScene>>>> entrySet = jobFlashMap.entrySet();
      for (Entry<OrgOrderFlashToEsJobDO, List<AbstractFlashEnhance<?, ?, OrgOrderScene>>> entry : entrySet) {
        List<AbstractFlashEnhance<?, ?, OrgOrderScene>> flashList = entry.getValue();
        OrgOrderFlashToEsJobDO job = entry.getKey();
        //按理来说，flashList的size为1
        for (AbstractFlashEnhance<?, ?, OrgOrderScene> flash : flashList) {
          try {
            FlashParam queryDto = new FlashParam();
            queryDto.setStart(Date.from(job.getFlashStartTime().atZone(ZoneId.systemDefault()).toInstant()));
            queryDto.setEnd(Date.from(job.getFlashEndTime().atZone(ZoneId.systemDefault()).toInstant()));
            flash.startFlush(queryDto);
            job.setStatus(3);
          }catch (Exception e){
            job.setStatus(4);
            job.setRemark(ExceptionUtil.stacktraceToString(e));
          }
        }
      }
      //更新
      orgOrderFlashToEsJobBatchRepository.updateBatchById(jobList);
      if(jobList.size() < request.getProcessSize()){
        break;
      }
      if(StrUtil.isNotBlank(request.getSleepTime())){
        Thread.sleep(Long.parseLong(request.getSleepTime()));
      }
    }
  }

  public void orgOrderFlashDataToEsWithJob(OrgOrderFlashToEsWithJobReqDTO request) {
    LocalDateTime startDateTime = LocalDateTimeUtil.parse(request.getFlashStartTime(), DatePattern.NORM_DATETIME_FORMATTER);
    LocalDateTime endDateTime = LocalDateTimeUtil.parse(request.getFlashEndTime(), DatePattern.NORM_DATETIME_FORMATTER);

    if (endDateTime.isAfter(LocalDateTime.now())) {
      throw new YxtParamException("结束时间不能晚于当前时间！");
    }
    //按时间拆分
    List<OrgOrderFlashToEsJobDO> jobList = new ArrayList<>();
    if(startDateTime.toLocalDate().isEqual(endDateTime.toLocalDate())){
      OrgOrderFlashToEsJobDO flashJob = new OrgOrderFlashToEsJobDO();
      flashJob.setOrderSource(request.getOrderSource());
      flashJob.setOrderType(request.getOrderType());
      flashJob.setContextId(TraceContext.traceId());
      flashJob.setFlashStartTime(startDateTime);
      flashJob.setFlashEndTime(endDateTime);
      jobList.add(flashJob);
    } else {
      int i = 1;
      while (!startDateTime.toLocalDate().isAfter(endDateTime.toLocalDate())) {
        OrgOrderFlashToEsJobDO flashJob = new OrgOrderFlashToEsJobDO();
        flashJob.setOrderSource(request.getOrderSource());
        flashJob.setOrderType(request.getOrderType());
        flashJob.setContextId(TraceContext.traceId());
        //第一天特殊处理
        if (i == 1) {
          flashJob.setFlashStartTime(startDateTime);
          flashJob.setFlashEndTime(LocalDateTimeUtil.endOfDay(startDateTime));
          startDateTime = startDateTime.plusDays(1);
          i++;
          jobList.add(flashJob);
          continue;
        }
        //最后一天特殊处理
        if (startDateTime.toLocalDate().isEqual(endDateTime.toLocalDate())) {
          flashJob.setFlashStartTime(LocalDateTimeUtil.beginOfDay(endDateTime));
          flashJob.setFlashEndTime(endDateTime);
          jobList.add(flashJob);
          break;
        }
        LocalDateTime tempEndDateTime = LocalDateTimeUtil.endOfDay(startDateTime);
        flashJob.setFlashStartTime(LocalDateTimeUtil.beginOfDay(startDateTime));
        flashJob.setFlashEndTime(tempEndDateTime);
        startDateTime = startDateTime.plusDays(1);
        jobList.add(flashJob);
      }
    }

    if (StrUtil.isBlank(request.getOrderType())) {
      jobList.forEach(job -> job.setOrderType("ORDER"));
      //再copy一份
      List<OrgOrderFlashToEsJobDO> copyJobList = BeanUtil.copyToList(jobList, OrgOrderFlashToEsJobDO.class);
      copyJobList.forEach(job -> job.setOrderType("REFUND"));
      jobList.addAll(copyJobList);
    }
    if (StrUtil.isBlank(request.getOrderSource())) {
      jobList.forEach(job -> job.setOrderSource(OrderSource.ONLINE.name()));
      //再copy一份
      List<OrgOrderFlashToEsJobDO> copyJobList = BeanUtil.copyToList(jobList, OrgOrderFlashToEsJobDO.class);
      copyJobList.forEach(job -> job.setOrderSource(OrderSource.OFFLINE.name()));
      jobList.addAll(copyJobList);
    }
    //插入数据库
    orgOrderFlashToEsJobBatchRepository.saveBatch(jobList);
  }

  public void orgOrderFlashDataToEs(FlashDataToEsReq flashDataToEsReq) {
    Date startDate = flashDataToEsReq.getStartDate();
    Date endDate = flashDataToEsReq.getEndDate();
    List<String> noList = flashDataToEsReq.getNoList();

    for (AbstractFlashEnhance<?, ?, OrgOrderScene> abstractFlash : orgOrderFlashList) {
      commonBusinessPool.submit(() -> {
        log.info("flush task running,{},req:{}", abstractFlash.getClass().getName(),
            JsonUtils.toJson(flashDataToEsReq));
        FlashParam queryDto = new FlashParam();
        queryDto.setStart(startDate);
        queryDto.setEnd(endDate);
        queryDto.setNoList(noList);
        abstractFlash.startFlush(queryDto);
        log.info("flush task done,{},req:{}", abstractFlash.getClass().getName(),
            JsonUtils.toJson(queryDto));
      });
    }
  }
}
