package com.yxt.order.atom.order.es.sync.org_order.handler;

import static com.yxt.order.types.canal.Table.OFFLINE_REFUND_ORDER_DETAIL_REGEX;
import static com.yxt.order.types.canal.Table.OFFLINE_REFUND_ORDER_REGEX;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.es.components.SyncComponent;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.data.CanalOfflineRefundOrder.OfflineRefundOrder;
import com.yxt.order.atom.order.es.sync.org_order.model.OrgRefundDetailModel;
import com.yxt.order.atom.order.es.sync.org_order.model.OrgRefundModel;
import com.yxt.order.atom.order.mapper.dto.OrganizationInfoDto;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.offline.enums.AfterSaleTypeEnum;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import com.yxt.order.types.order.enums.OrderSource;
import com.yxt.order.types.order.enums.RefundAfterSaleTypeEnum;
import com.yxt.order.types.order.enums.RefundFlagEnum;
import com.yxt.order.types.order.enums.RefundStateEnum;
import com.yxt.order.types.order.enums.RefundTypeEnum;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class OrgOfflineRefundHandler extends AbstractCanalHandler<CanalOfflineRefundOrder, OrgRefundModel> {

  @Resource
  private SyncComponent syncComponent;


  @Value("${org-order.offline-order-es-keep-day:365}")
  private Integer offlineOrderEsKeepDays;

  public OrgOfflineRefundHandler() {
    super(CanalOfflineRefundOrder.class);
  }

  /**
   * 检查
   *
   * @return
   */
  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    return Database.DSCLOUD_OFFLINE.equals(database) && (Table.tableRegex(OFFLINE_REFUND_ORDER_REGEX, table) || Table.tableRegex(OFFLINE_REFUND_ORDER_DETAIL_REGEX, table));
  }

  /**
   * 组装ES数据模型
   *
   * @return
   */
  @Override
  protected List<OrgRefundModel> assemble() {
    List<OfflineRefundOrder> canalRefundList = getData().getData();
    if (CollectionUtils.isEmpty(canalRefundList)) {
      return Lists.newArrayList();
    }
    List<OfflineRefundOrder> filterRefundList = canalRefundList.stream().filter(this::efficientData).collect(Collectors.toList());
    if (CollectionUtils.isEmpty(filterRefundList)) {
      return Lists.newArrayList();
    }
    //会员信息
    Map<String, String> memberMap = new HashMap<>();
    Map<String, String> organizationMap = new HashMap<>();
    List<OrgRefundModel> refundModelList = new ArrayList<>();
    for (OfflineRefundOrder canalRefund : filterRefundList) {
      if(canalRefund.migrateRefundOrder()){
        continue;
      }
      OfflineRefundOrderDO refund = syncComponent.getOfflineRefundByRefundNo(canalRefund.getRefundNo(), canalRefund.getNeedRoute());
      if (ObjectUtil.isNull(refund)) {
        continue;
      }
      LocalDateTime refundCreated = DateUtil.toLocalDateTime(refund.getCreated());
      if(refundCreated.isBefore(LocalDateTime.now().minusDays(offlineOrderEsKeepDays))){
        continue;
      }
      OrgRefundModel refundModel = new OrgRefundModel();
      refundModel.setOrderNo(StrUtil.isBlank(refund.getOrderNo()) ? "-1" : refund.getOrderNo());
      refundModel.setThirdOrderNo(StrUtil.isBlank(refund.getThirdOrderNo()) ? "-1" : refund.getThirdOrderNo());
      refundModel.setRefundNo(StrUtil.toStringOrNull(refund.getRefundNo()));
      refundModel.setThirdRefundNo(refund.getThirdRefundNo());
      refundModel.setCreated(refund.getCreated());
      refundModel.setCreateTime(refund.getCreatedTime());
      refundModel.setStoreCode(refund.getStoreCode());
      refundModel.setOrgCode(refund.getStoreCode());
      refundModel.setSourceStoreCode(refund.getStoreCode());
      refundModel.setSourceOrgCode(refund.getStoreCode());
      refundModel.setRefundStatus(RefundStateEnum.SUCCESS.getCode());
      refundModel.setErpStatus(RefundStateEnum.SUCCESS.getCode().toString());
      refundModel.setErpTime(refund.getBillTime());
      refundModel.setErpRefundNo(refund.getThirdRefundNo());
      refundModel.setOrderSource(OrderSource.OFFLINE.name());
      refundModel.setPlatformCode(ThirdPlatformCodeEnum.fromString(refund.getThirdPlatformCode())
          .name());
      refundModel.setRefundType(RefundTypeEnum.fromString(refund.getRefundType()).name());
      refundModel.setAfterSaleType(AfterSaleTypeEnum.fromString(refund.getAfterSaleType()) == AfterSaleTypeEnum.AFTER_SALE_AMOUNT ? RefundAfterSaleTypeEnum.REFUND.name() : RefundAfterSaleTypeEnum.RETURN.name());
      refundModel.setRefundFlags(null);
      if (StrUtil.isBlank(refund.getOrderNo())) {
        refundModel.addRefundFlags(RefundFlagEnum.NO_ORDER);
      }
      refundModel.setRefundBillAmount(BigDecimal.ZERO);
      List<OfflineRefundOrderDetailDO> refundDetailList = syncComponent.getOfflineRefundDetailByRefundNo(canalRefund.getRefundNo(), canalRefund.getNeedRoute());
      if (CollUtil.isNotEmpty(refundDetailList)) {
        List<OrgRefundDetailModel> detailModels = refundDetailList.stream().map(detail -> {
          OrgRefundDetailModel detailModel = new OrgRefundDetailModel();
          detailModel.setOrderDetailId(StrUtil.toStringOrNull(detail.getId()));
          detailModel.setErpCode(detail.getErpCode());
          detailModel.setItemName(detail.getErpName());
          return detailModel;
        }).collect(Collectors.toList());
        refundModel.setDetailList(detailModels);
        BigDecimal billAmount = refundDetailList.stream()
            .map(OfflineRefundOrderDetailDO::getBillAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        refundModel.setRefundBillAmount(billAmount);
      }
      refundModel.setOrderCreated(refund.getCreated());
      if (StrUtil.isNotBlank(refund.getOrderNo())) {
        OfflineOrderDO orderDO = syncComponent.getOfflineOrderByOrderNo(refund.getOrderNo(), canalRefund.getNeedRoute());
        if (ObjectUtil.isNotNull(orderDO)) {
          refundModel.setOrderCreated(orderDO.getCreated());
        }
      }
      refundModel.setUserId(refund.getUserId());
      if (memberMap.containsKey(refund.getUserId())) {
        refundModel.setUserCardNo(memberMap.get(refund.getUserId()));
      } else {
        String userCardNo = syncComponent.getUserCardNoByRefundNo(refund.getRefundNo(), canalRefund.getNeedRoute());
        refundModel.setUserCardNo(userCardNo);
        memberMap.put(refund.getUserId(), userCardNo);
      }

      if (organizationMap.containsKey(refund.getStoreCode())) {
        refundModel.setStoreType(organizationMap.get(refund.getStoreCode()));
      } else {
        OrganizationInfoDto organization = syncComponent.getOrganizationByRefundNo(refund.getRefundNo(), canalRefund.getNeedRoute());
        if (organization != null) {
          refundModel.setStoreType(organization.getStoreType());
          organizationMap.put(refund.getStoreCode(), organization.getStoreType());
        }
        organizationMap.put(refund.getStoreCode(), "");
      }
      refundModel.setDeleted(0L);
      refundModelList.add(refundModel);
    }
    return refundModelList;
  }

  public boolean efficientData(OfflineRefundOrder offlineRefundOrder) {
    //只筛选线下单平台
    if(!ThirdPlatformCodeEnum.isValid(offlineRefundOrder.getThirdPlatformCode())){
      return false;
    }
    return true;
  }
}
