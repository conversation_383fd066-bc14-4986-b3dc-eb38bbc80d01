package com.yxt.order.atom.order.es.sync.org_order.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.yxt.order.atom.migration.config.ThreadPoolConfig;
import com.yxt.order.atom.order.entity.ErpBillInfoDO;
import com.yxt.order.atom.order.entity.OrderDeliveryRecordDO;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.entity.RouteAllotDO;
import com.yxt.order.atom.order.es.components.SyncComponent;
import com.yxt.common.logic.canal.AbstractCanalHandler;
import com.yxt.order.atom.order.es.sync.data.CanalOrderInfo;
import com.yxt.order.atom.order.es.sync.data.CanalOrderInfo.Order;
import com.yxt.order.atom.order.es.sync.member_transaction.utils.OnlineOrderStoreTypeUtils;
import com.yxt.order.atom.order.es.sync.org_order.model.OrgOrderDetailModel;
import com.yxt.order.atom.order.es.sync.org_order.model.OrgOrderModel;
import com.yxt.order.types.canal.Database;
import com.yxt.order.types.canal.Table;
import com.yxt.order.types.order.enums.DeliveryPlatformEnum;
import com.yxt.order.types.order.enums.DeliveryTypeEnum;
import com.yxt.order.types.order.enums.OrderFlagEnum;
import com.yxt.order.types.order.enums.OrderLockFlagEnum;
import com.yxt.order.types.order.enums.OrderServiceModeEnum;
import com.yxt.order.types.order.enums.OrderSource;
import com.yxt.order.types.order.enums.PlatformCodeEnum;
import com.yxt.order.types.order.enums.RefundStateEnum;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class OrgOnlineOrderHandler extends AbstractCanalHandler<CanalOrderInfo, OrgOrderModel> {

  @Resource
  private SyncComponent syncComponent;

  @Resource(name = ThreadPoolConfig.ORDER_SYNC_TO_ES_POOL)
  private Executor orderSyncToEsPool;

  @Value("${org-order.online-order-es-keep-day:365}")
  private Integer onlineOrderEsKeepDays;

  public OrgOnlineOrderHandler() {
    super(CanalOrderInfo.class);
  }

  /**
   * 检查
   *
   * @returnx
   */
  @Override
  protected Boolean check() {
    String database = getData().getDatabase();
    String table = getData().getTable();
    List<String> orderRelateTableList = Lists.newArrayList(Table.ORDER_INFO, Table.ORDER_DETAIL, Table.ERP_BILL_INFO, Table.REFUND_ORDER, Table.ORDER_DELIVERY_RECORD);
    return Database.DSCLOUD.equals(database) && orderRelateTableList.contains(table);
  }

  /**
   * 组装ES数据模型
   *
   * @return
   */
  @Override
  protected List<OrgOrderModel> assemble() {
    List<Order> canalOrderList = getData().getData();
    if (CollectionUtils.isEmpty(canalOrderList)) {
      return Lists.newArrayList();
    }
    List<String> canalOrderNoList = canalOrderList.stream().map(Order::getOrderNo).distinct().collect(Collectors.toList());
    List<OrderInfoDO> orderList = syncComponent.getOrderListByOrderNo(canalOrderNoList)
        .stream()
        //排除B2C
        .filter(order-> StrUtil.equalsIgnoreCase(OrderServiceModeEnum.O2O.getCode(),order.getServiceMode()))
        //排除机构为空的
        .filter(order-> StrUtil.isNotBlank(order.getOrganizationCode()))
        //排除时间范围
        .filter(order-> !DateUtil.toLocalDateTime(order.getCreated()).isBefore(LocalDateTime.now().minusDays(onlineOrderEsKeepDays)))
        .collect(Collectors.toList());
    if(CollUtil.isEmpty(orderList)){
      return Lists.newArrayList();
    }
    List<String> orderNoList = orderList.stream().map(order->StrUtil.toStringOrNull(order.getOrderNo())).collect(Collectors.toList());

    AtomicReference<Map<Long, List<OrderDetailDO>>> orderDetailMap = new AtomicReference<>(new HashMap<>());
    AtomicReference<Map<Long, ErpBillInfoDO>> erpBillMap = new AtomicReference<>(new HashMap<>());
    AtomicReference<Map<Long, List<RefundOrderDO>>> refundMap = new AtomicReference<>(new HashMap<>());
    AtomicReference<Map<Long, RouteAllotDO>> orderRouteAllotMap = new AtomicReference<>(new HashMap<>());
    AtomicReference<Map<Long, OrderDeliveryRecordDO>> orderDeliveryRecordMap = new AtomicReference<>(new HashMap<>());

    List<CompletableFuture<Void>> futureList = new ArrayList<>();
    CompletableFuture<Void> orderDetailFuture = CompletableFuture.runAsync(() -> {
      List<OrderDetailDO> orderDetailList = syncComponent.getOrderDetailListByOrderNo(orderNoList);
      if (CollUtil.isNotEmpty(orderDetailList)) {
        orderDetailMap.set(orderDetailList.stream()
            .collect(Collectors.groupingBy(OrderDetailDO::getOrderNo)));
      }
    }, orderSyncToEsPool);
    futureList.add(orderDetailFuture);

    CompletableFuture<Void> orderErpFuture = CompletableFuture.runAsync(() -> {
      List<ErpBillInfoDO> orderErpInfoList = syncComponent.getOrderErpInfoListByOrderNo(orderNoList);
      if (CollUtil.isNotEmpty(orderErpInfoList)) {
        erpBillMap.set(orderErpInfoList.stream()
            .collect(Collectors.toMap(ErpBillInfoDO::getOrderNo, Function.identity())));
      }
    }, orderSyncToEsPool);
    futureList.add(orderErpFuture);

    CompletableFuture<Void> refundFuture = CompletableFuture.runAsync(() -> {
      List<RefundOrderDO> refundList = syncComponent.getRefundListByOrderNo(orderNoList);
      if (CollUtil.isNotEmpty(refundList)) {
        refundMap.set(refundList.stream()
            .collect(Collectors.groupingBy(RefundOrderDO::getOrderNo)));
      }
    }, orderSyncToEsPool);
    futureList.add(refundFuture);

    CompletableFuture<Void> orderRouteAllotFuture = CompletableFuture.runAsync(() -> {
      //查询订单路由记录
      List<RouteAllotDO> orderRouteAllotList = syncComponent.getOrderRouteAllot(orderNoList);
      if (CollUtil.isNotEmpty(orderRouteAllotList)) {
        orderRouteAllotMap.set(orderRouteAllotList.stream()
            .collect(Collectors.toMap(RouteAllotDO::getOmsNo, Function.identity(), (a, b) -> b)));
      }
    }, orderSyncToEsPool);
    futureList.add(orderRouteAllotFuture);

    CompletableFuture<Void> orderDeliveryRecordFuture = CompletableFuture.runAsync(() -> {
      List<OrderDeliveryRecordDO> orderDeliveryRecordList = syncComponent.getOrderDeliveryRecordInfo(orderNoList);
      if (CollUtil.isNotEmpty(orderDeliveryRecordList)) {
        orderDeliveryRecordMap.set(orderDeliveryRecordList.stream()
            .collect(Collectors.toMap(OrderDeliveryRecordDO::getOrderNo, Function.identity(), (a, b) -> b)));
      }
    }, orderSyncToEsPool);
    futureList.add(orderDeliveryRecordFuture);

    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();

    List<OrgOrderModel> orgOrderModelList = new ArrayList<>();
    for (OrderInfoDO orderInfoDO : orderList) {
      OrgOrderModel orgOrderModel = new OrgOrderModel();
      orgOrderModel.setOrderNo(StrUtil.toStringOrNull(orderInfoDO.getOrderNo()));
      orgOrderModel.setThirdOrderNo(orderInfoDO.getThirdOrderNo());
      orgOrderModel.setCreated(orderInfoDO.getCreated());
      orgOrderModel.setCreateTime(orderInfoDO.getCreateTime());
      orgOrderModel.setPayTime(ObjectUtil.isNull(orderInfoDO.getPayTime()) ? orderInfoDO.getCreated() : orderInfoDO.getPayTime());
      orgOrderModel.setPayDate(DateUtil.formatDate(orgOrderModel.getPayTime()));
      orgOrderModel.setStoreCode(orderInfoDO.getOnlineStoreCode());
      orgOrderModel.setOrgCode(orderInfoDO.getOrganizationCode());
      orgOrderModel.setSourceStoreCode(orderInfoDO.getSourceOnlineStoreCode());
      orgOrderModel.setSourceOrgCode(orderInfoDO.getSourceOrganizationCode());
      orgOrderModel.setOrderStatus(orderInfoDO.getOrderState());
      orgOrderModel.setErpStatus(orderInfoDO.getErpState());
      orgOrderModel.setErpTime(orderInfoDO.getBillTime());
      orgOrderModel.setErpSaleNo(orderInfoDO.getErpSaleNo());
      orgOrderModel.setOrderSource(OrderSource.ONLINE.name());
      orgOrderModel.setPlatformCode(PlatformCodeEnum.getByCode(orderInfoDO.getThirdPlatformCode()).name());
      orgOrderModel.setLockFlag(StrUtil.toStringOrNull(orderInfoDO.getLockFlag()));
      if(orderDeliveryRecordMap.get().containsKey(orderInfoDO.getOrderNo())){
        OrderDeliveryRecordDO orderDeliveryRecord = orderDeliveryRecordMap.get().get(orderInfoDO.getOrderNo());
        orgOrderModel.setDeliveryType(orderDeliveryRecord.getDeliveryType());
        if(StrUtil.isNotBlank(orderDeliveryRecord.getDeliveryPlatName()) && DeliveryTypeEnum.SELLER_SELF.getCode().equals(orderDeliveryRecord.getDeliveryType())){
          orgOrderModel.setDeliveryType(DeliveryPlatformEnum.getByName(orderDeliveryRecord.getDeliveryPlatName()).getCode());
        }
      }
      //先置为null
      orgOrderModel.setOrderFlags(null);
      if (orderInfoDO.getLockFlag() != null
          && orderInfoDO.getLockFlag() > OrderLockFlagEnum.LOCK_EXCEPTION_MIN_CODE.getCode()
          && orderInfoDO.getLockFlag() < OrderLockFlagEnum.LOCK_EXCEPTION_MAX_CODE.getCode()
      ) {
        orgOrderModel.addOrderFlags(OrderFlagEnum.EXCEPTION);
      }
      if (orderInfoDO.getPrescriptionFlag() != null && orderInfoDO.getPrescriptionFlag() == 1) {
        orgOrderModel.addOrderFlags(OrderFlagEnum.PRESCRIPTION);
      }
      if (orderInfoDO.getMedicalInsurance() != null && orderInfoDO.getMedicalInsurance() == 1) {
        orgOrderModel.addOrderFlags(OrderFlagEnum.MEDICAL);
      }
      if(orderRouteAllotMap.get().containsKey(orderInfoDO.getOrderNo())
      ) {
          orgOrderModel.addOrderFlags(OrderFlagEnum.ROUTE);
      }
      if(refundMap.get().containsKey(orderInfoDO.getOrderNo())){
        Set<Integer> refundStateSet = refundMap.get().get(orderInfoDO.getOrderNo()).stream().map(RefundOrderDO::getState).collect(Collectors.toSet());
        if(refundStateSet.contains(RefundStateEnum.NOT_CHECK.getCode()) || refundStateSet.contains(RefundStateEnum.NOT_RETURN_GOODS.getCode()) ){
          orgOrderModel.addOrderFlags(OrderFlagEnum.REFUNDING);
        }
      }
      if (erpBillMap.get().containsKey(orderInfoDO.getOrderNo())) {
        ErpBillInfoDO erpBillInfo = erpBillMap.get().get(orderInfoDO.getOrderNo());
        orgOrderModel.setBillAmount(erpBillInfo.getBillTotalAmount());
      }
      if(orderDetailMap.get().containsKey(orderInfoDO.getOrderNo())){
        List<OrderDetailDO> orderDetailDOS = orderDetailMap.get().get(orderInfoDO.getOrderNo());
        List<OrgOrderDetailModel> detailModelList = orderDetailDOS.stream().map(detail -> {
          OrgOrderDetailModel detailModel = new OrgOrderDetailModel();
          detailModel.setOrderDetailId(StrUtil.toStringOrNull(detail.getId()));
          detailModel.setErpCode(detail.getErpCode());
          detailModel.setItemName(detail.getCommodityName());
          return detailModel;
        }).collect(Collectors.toList());
        orgOrderModel.setDetailList(detailModelList);
      }
      orgOrderModel.setUserCardNo(orderInfoDO.getMemberNo());
      orgOrderModel.setUserId(syncComponent.queryUserId(orderInfoDO.getMemberNo()));
      orgOrderModel.setStoreType(OnlineOrderStoreTypeUtils.storeType(orderInfoDO.getOrganizationCode()).name());
      orgOrderModel.setServiceMode(orderInfoDO.getServiceMode());
      orgOrderModel.setDeleted(orderInfoDO.getDeleted());
      orgOrderModelList.add(orgOrderModel);
    }
    return orgOrderModelList;
  }

  public boolean efficientData(Order order) {
    return StrUtil.equalsIgnoreCase(order.getServiceMode(),OrderServiceModeEnum.O2O.getCode()) && StrUtil.isNotBlank(order.getOrganizationCode());
  }
}
