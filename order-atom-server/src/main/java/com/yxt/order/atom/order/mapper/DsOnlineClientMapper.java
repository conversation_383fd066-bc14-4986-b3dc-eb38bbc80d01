package com.yxt.order.atom.order.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.order.atom.order.entity.DsOnlineClientDO;
import com.yxt.order.atom.sdk.online_order.store.res.The3DsOnlineClientResDto;
import com.yxt.order.atom.sdk.online_order.store.res.The3DsStoreResDto;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * o2o平台网店信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-31
 */

@Repository
public interface DsOnlineClientMapper extends BaseMapper<DsOnlineClientDO> {

  /**
   * 获取服务商模式网店信息
   *
   * @param platformCode
   * @param onlineStoreCode
   * @return
   */
  DsOnlineClientDO selectSupplierClient(@Param("platformCode") String platformCode,
      @Param("onlineStoreCode") String onlineStoreCode);

  The3DsStoreResDto getStoreAccess(@Param("merCode") String merCode,
      @Param("platformCode") String platformCode, @Param("onlineStoreCode") String onlineStoreCode,
      @Param("onlineClientCode") String onlineClientCode);

  List<The3DsOnlineClientResDto> getOnlineStoreByPlatformShopId(@Param("merCode") String merCode,
      @Param("platformCode") String platformCode, @Param("platformShopId") String platformShopId,
      @Param("onlineClientCode") String onlineClientCode);
}
