package com.yxt.order.atom.order.mapper;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.order.atom.order.entity.DsStoreRiderPollingConfigDO;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

/**
 * <p>
 * 线上门店订单配置
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ds_store_order_config")
public class DsStoreOrderConfigDO implements Serializable {

  private static final long serialVersionUID = 1L;

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 线上门店id
   */
  private Long onlineStoreId;

  /**
   * 是否自动接单，0否，1是
   */
  private Integer autoAcceptFlag;

  /**
   * 接单后自动打印拣货单，0否，1是
   */
  private Integer autoPrintPick;

  /**
   * 拣货后自动打印小票，0否，1是
   */
  private Integer autoPrintReceipt;

  /**
   * 自动呼叫骑手，0接单后呼叫骑手，1拣货后呼叫骑手
   */
  private Integer autoCallRider;

  /**
   * 订单是否转发到HEMS, 0 不转HEMS, 1 转HEMS
   */
  private Integer autoToHems;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 修改时间
   */
  private Date modifyTime;

  /**
   * 打印模板id
   */
  private Long printTemplateId;

  /**
   * 第二个打印模板id，目前用于拣货后自动打印
   */
  private Long secondPrintTemplateId;
  /**
   * 是否开启b2c
   */
  private Integer openB2c;
  /**
   * b2c关联id
   */
  private String outB2cClientId;
  /**
   * 骑手循环呼叫 0-否，1-是
   */
  private Integer riderPolling;
  /**
   * 骑手比价 0-否，1-是
   */
  private Integer riderCompare;
  /**
   * 骑手比价延时配置 单位：分钟，默认5分钟
   */
  private Integer riderCompareDelay;
  /**
   * 循环呼叫配置
   */
  @TableField(exist = false)
  private List<DsStoreRiderPollingConfigDO> riderPollingConfig;


  public static DsStoreOrderConfigDO defaultConfig(String merCode, Long storeId,
      DsStoreOrderConfigDO defaultStoreOrderConfig) {
    DsStoreOrderConfigDO storeOrderConf = new DsStoreOrderConfigDO();
    if (Objects.nonNull(defaultStoreOrderConfig)) {
      BeanUtils.copyProperties(defaultStoreOrderConfig, storeOrderConf);
    } else {
      /** 是否自动接单，0否，1是 **/
      storeOrderConf.setAutoAcceptFlag(1);
      /** 拣货后自动打印小票，0否，1是 **/
      storeOrderConf.setAutoPrintPick(0);
      /** 接单后自动打印拣货单，0否，1是 **/
      storeOrderConf.setAutoPrintReceipt(1);
      /** 自动呼叫骑手，0接单后呼叫骑手，1拣货后呼叫骑手 **/
      storeOrderConf.setAutoCallRider(1);
      storeOrderConf.setOpenB2c(0);
      /** 是否循环呼叫骑手，0否，1是 **/
      storeOrderConf.setRiderPolling(0);
      /** 是否启用骑手比价，0否，1是 **/
      storeOrderConf.setRiderCompare(0);
      /** 比价延时配置，单位：分钟，默认5分钟 **/
      storeOrderConf.setRiderCompareDelay(5);
    }

    storeOrderConf.setMerCode(merCode);
    storeOrderConf.setOnlineStoreId(storeId);
    return storeOrderConf;
  }
}
