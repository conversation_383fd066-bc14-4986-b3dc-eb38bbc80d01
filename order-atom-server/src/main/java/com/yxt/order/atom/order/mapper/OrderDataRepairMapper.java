package com.yxt.order.atom.order.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.entity.OrderDataRepairDO;
import com.yxt.order.atom.repair.dto.StartEndId;
import com.yxt.order.types.offline.enums.PreCheckEnum;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;
@Repository
@DS(DATA_SOURCE.ORDER_OFFLINE)
public interface OrderDataRepairMapper extends BaseMapper<OrderDataRepairDO> {

  @Update("update order_data_repair_2 set pre_check = #{preCheck},number=#{number} where id=#{id}")
  void updatePreCheck(@Param("id") Long id, @Param("preCheck") PreCheckEnum preCheck,@Param("number")String number);

  @Select("select IFNULL(min(id),0) as startId,IFNULL(max(id),0) as endId from order_data_repair_2 where scene = #{sceneName}  and pre_check = #{preCheckName}")
  StartEndId selectStartEndId(@Param("sceneName") String sceneName, @Param("preCheckName") String preCheckName);
}
