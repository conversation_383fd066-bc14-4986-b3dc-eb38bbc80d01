package com.yxt.order.atom.order.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 基本订单信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-16
 */
@Repository
public interface OrderDetailMapper extends BaseMapper<OrderDetailDO> {

  @Select("select min(id) from order_detail where create_time >= #{flashParam.start} and create_time <= #{flashParam.end} ")
  Long selectMinId(@Param("flashParam") FlashParam flashParam);

  @Select("select max(id) from order_detail where create_time >= #{flashParam.start} and create_time <= #{flashParam.end} ")
  Long selectMaxId(@Param("flashParam") FlashParam flashParam);

}
