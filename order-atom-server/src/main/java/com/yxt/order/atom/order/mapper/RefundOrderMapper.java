package com.yxt.order.atom.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yxt.common.logic.consistency.EfficientParam;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

@Repository
public interface RefundOrderMapper extends BaseMapper<RefundOrderDO> {
  Long selectMaxId(@Param("flashParam") FlashParam flashParam);

  Long selectMinId(@Param("flashParam") FlashParam flashParam);

  @Select("select max(id) from refund_order where create_time >= #{param.startDate} and create_time <= #{param.endDate}")
  Long selectEfficientCountMaxId(@Param("param")EfficientParam param);

  @Select("select min(id) from refund_order where create_time >= #{param.startDate} and create_time <= #{param.endDate}")
  Long selectEfficientCountMinId(@Param("param")EfficientParam param);
}