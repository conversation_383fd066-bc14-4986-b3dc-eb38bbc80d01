package com.yxt.order.atom.order.mongo;

import com.yxt.order.types.offline.NumberType;
import com.yxt.order.types.offline.StagingType;
import java.util.Date;
import lombok.Data;
import org.springframework.data.annotation.Id;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年08月13日 16:14
 * @email: <EMAIL>
 */
@Data
public class StagingOrder {

  @Id
  private String id;

  public static final String COLLECTION_NAME = "offline_staging_order";

  /**
   * 是否是迁移单
   */
  private Boolean migration;


  /**
   * 暂存类型
   */
  private StagingType stagingType;

  /**
   * 原始数据
   */
  private String data;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 是否已处理
   */
  private Boolean handled;


  /**
   * 单号类型 ORDER,REFUND
   */
  private NumberType numberType;
  private String storeCode;
  private String thirdPlatformCode;
  private String thirdOrderNo; // numberType=ORDER
  private String thirdRefundNo;// numberType=REFUND
  private String userCardNo;

  private String defineNo; // 用于确定分表位置

  private Date createDateTime;
}
