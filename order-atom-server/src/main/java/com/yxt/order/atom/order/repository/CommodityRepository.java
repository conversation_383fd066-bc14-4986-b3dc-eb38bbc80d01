package com.yxt.order.atom.order.repository;

import com.yxt.order.atom.sdk.online_order.commodity.req.CommodityStockQueryReq;
import com.yxt.order.atom.sdk.online_order.commodity.res.CommodityStockRes;
import java.util.List;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 16:28
 * @email: <EMAIL>
 * <p>
 */
public interface CommodityRepository {


  List<CommodityStockRes> queryCommodityStock(CommodityStockQueryReq req);


}
