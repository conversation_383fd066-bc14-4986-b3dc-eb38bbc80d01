package com.yxt.order.atom.order.repository;

import com.yxt.order.atom.dto.SimpleOfflineOrderDetailDto;
import com.yxt.order.atom.dto.SimpleOfflineRefundOrderDetailDto;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.sdk.offline_order.dto.ExistOfflineOrderResDto;
import com.yxt.order.atom.sdk.offline_order.dto.ExistOfflineRefundOrderResDto;
import com.yxt.order.atom.sdk.offline_order.dto.GetOfflineOrderByDateResDto;
import com.yxt.order.atom.sdk.offline_order.dto.GetOfflineRefundOrderByDateResDto;
import com.yxt.order.atom.sdk.offline_order.req.CommonOfflineRefundQueryReqDto;
import com.yxt.order.atom.sdk.offline_order.req.GetOfflineOrderByDateReqDto;
import com.yxt.order.atom.sdk.offline_order.req.GetOfflineRefundOrderByDateReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderDetailReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderExistsReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderInfoQryByScaleBatchReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderInfoQryByScaleReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderInfoReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundAmountReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundInfoQryBatchReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundInfoQryReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderDetailReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderExistsReqDto;
import com.yxt.order.atom.sdk.offline_order.req.UnionOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.res.CommonOfflineRefundResDto;
import com.yxt.order.atom.sdk.offline_order.res.ExistOrderInfo;
import com.yxt.order.atom.sdk.offline_order.res.ExistRefundOrderInfo;
import com.yxt.order.atom.sdk.offline_order.res.OfflineOrderDetailResDto;
import com.yxt.order.atom.sdk.offline_order.res.OfflineOrderInfoResDto;
import com.yxt.order.atom.sdk.offline_order.res.OfflineRefundAmountResDTO;
import com.yxt.order.atom.sdk.offline_order.res.OfflineRefundOrderDetailResDto;
import com.yxt.order.atom.sdk.offline_order.res.UnionOrderResDto;
import java.util.List;

/**
 * <AUTHOR> Runkang (moatkon)
 * @date 2024年04月07日 16:29
 * @email: <EMAIL>
 */
public interface OfflineOrderRepository {

  void saveOfflineOrderDO(OfflineOrderDO offlineOrderDO);

  void updateOfflineParentOrderData(OfflineOrderDO offlineOrderDO);

  void saveOfflineRefundOrderDO(OfflineRefundOrderDO offlineRefundOrderDO);

  void updateOfflineParentRefundData(OfflineRefundOrderDO offlineRefundOrderDO);

  OfflineOrderInfoResDto get(OfflineOrderInfoReqDto offlineOrderInfoReqDto);

  OfflineRefundAmountResDTO getOrderRefundAmount(
      OfflineRefundAmountReqDto offlineRefundAmountReqDto);

  OfflineOrderDetailResDto detail(OfflineOrderDetailReqDto detailReqDto);

  OfflineRefundOrderDetailResDto refundDetail(OfflineRefundOrderDetailReqDto refundDetailReqDto);

  GetOfflineOrderByDateResDto orderData2MqQuery(GetOfflineOrderByDateReqDto reqDto);

  GetOfflineRefundOrderByDateResDto refundData2MqQuery(GetOfflineRefundOrderByDateReqDto reqDto);

  Boolean offlineThirdOrderExists(OfflineOrderExistsReqDto offlineOrderExistsReqDto);
  ExistOrderInfo offlineThirdOrderExistsInfo(OfflineOrderExistsReqDto reqDto);
  OfflineOrderDO offlineOrderInfoForRepair(OfflineOrderExistsReqDto reqDto);

  Boolean offlineThirdRefundOrderExists(
      OfflineRefundOrderExistsReqDto offlineRefundOrderExistsReqDto);

  ExistRefundOrderInfo offlineThirdRefundOrderExistsInfo(
      OfflineRefundOrderExistsReqDto offlineRefundOrderExistsReqDto);

  OfflineRefundOrderDO offlineRefundOrderForRepair(
      OfflineRefundOrderExistsReqDto offlineRefundOrderExistsReqDto);

  ExistOfflineOrderResDto queryOfflineOrderByDto(OfflineOrderExistsReqDto reqDto);

  ExistOfflineRefundOrderResDto queryOfflineRefundOrderByDto(OfflineRefundOrderExistsReqDto reqDto);

  UnionOrderResDto unionOrder(UnionOrderReqDto unionOrderReqDto);

  CommonOfflineRefundResDto commonRefundInfo(
      CommonOfflineRefundQueryReqDto commonOfflineRefundQuery);

  void identifyMainOrder(OfflineOrderDO offlineOrderDO);

  void compensateRefundOrderData(OfflineRefundOrderDO refundOrderDO);

  void writeRefundPromotionCouponData(OfflineRefundOrderDO refundOrderDO);

  SimpleOfflineOrderDetailDto querySimpleOfflineOrderDetail(String offlineOrderNo);

  SimpleOfflineRefundOrderDetailDto querySimpleOfflineRefundOrderDetail(String offlineRefundNo);

  void compensateHdMissPromotionCouponData(Long compensateId,OfflineOrderDO offlineOrderDO);

  List<OfflineOrderDetailResDto> getOrderInfoBatchByScale(OfflineOrderInfoQryByScaleBatchReqDto request);

  OfflineOrderDetailResDto getOrderInfoByScale(OfflineOrderInfoQryByScaleReqDto request);

  List<OfflineRefundOrderDetailResDto> getRefundInfoBatchByScale(OfflineRefundInfoQryBatchReqDto request);

  OfflineRefundOrderDetailResDto getRefundInfoByScale(OfflineRefundInfoQryReqDto request);

  Boolean deletedOfflineOrder(OfflineOrderDO offlineOrderDO,String reason,Boolean deleteAll);
  Boolean deletedOfflineRefundOrder(OfflineRefundOrderDO offlineRefundOrderDO,String reason,Boolean deleteAll);

  Boolean chaiLingOfflineOrderUpdate(OfflineOrderDO dbOfflineOrder);

  Boolean chaiLingOfflineRefundOrderUpdate(OfflineRefundOrderDO dbRefund);

  List<OfflineRefundOrderDetailDO> queryBatchRefundDetail(List<String> offlineRefundNoList);

  List<OfflineOrderDetailDO> queryBatchOrderDetail(List<String> orderNoList);
}
