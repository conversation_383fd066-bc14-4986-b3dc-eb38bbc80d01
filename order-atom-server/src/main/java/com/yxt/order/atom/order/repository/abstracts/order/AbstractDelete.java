package com.yxt.order.atom.order.repository.abstracts.order;


import com.yxt.order.atom.sdk.online_order.order_info.dto.req.DeleteBeforeSaveDTO;
import java.util.Objects;
import org.springframework.util.Assert;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 11:46
 * @email: <EMAIL>
 */
public abstract class AbstractDelete {

  protected DeleteBeforeSaveDTO dto;
  protected Long orderNo;

  protected abstract Boolean canDelete();

  protected abstract Integer delete();


  public Integer exec(DeleteBeforeSaveDTO dto) {
    Assert.isTrue(Objects.nonNull(dto.getOrderNo()), "orderNo can not null");

    init(dto);

    if (!canDelete()) {
      return 0;
    }

    return delete();
  }

  private void init(DeleteBeforeSaveDTO dto) {
    this.dto = dto;
    this.orderNo = dto.getOrderNo();
  }


}
