package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.CommodityExceptionOrderDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.batch.CommodityExceptionOrderBatchRepository;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:58
 * @email: <EMAIL>
 */
@Component
public class CommodityExceptionOrderInsert extends AbstractInsert<List<CommodityExceptionOrderDO>> {

  @Resource
  private CommodityExceptionOrderBatchRepository commodityExceptionOrderBatchRepository;

  @Override
  protected Boolean canInsert() {
    return !CollectionUtils.isEmpty(saveDataOptional.getCommodityExceptionOrderList());
  }

  @Override
  protected Integer insert(List<CommodityExceptionOrderDO> list) {
    return commodityExceptionOrderBatchRepository.saveBatch(list) ? list.size() : 0;
  }

  @Override
  protected List<CommodityExceptionOrderDO> data() {
    return saveDataOptional.getCommodityExceptionOrderList();
  }
}
