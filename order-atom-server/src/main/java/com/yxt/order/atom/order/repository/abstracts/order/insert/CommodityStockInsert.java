package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.order.entity.CommodityStockDO;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.abstracts.order.InsertOrder;
import com.yxt.order.atom.order.repository.abstracts.order.OrderSaveHelper;
import com.yxt.order.atom.order.repository.batch.CommodityStockBatchRepository;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:58
 * @email: <EMAIL>
 */
@Component
@Order(InsertOrder.CommodityStockInsert)
public class CommodityStockInsert extends AbstractInsert<List<CommodityStockDO>> {

  @Resource
  private CommodityStockBatchRepository commodityStockBatchRepository;

  @Override
  protected Boolean canInsert() {
    return !CollectionUtils.isEmpty(saveDataOptional.getCommodityStockList());
  }

  @Override
  protected Integer insert(List<CommodityStockDO> list) {
    return commodityStockBatchRepository.saveBatch(list) ? list.size() : 0;
  }

  @Override
  protected List<CommodityStockDO> data() {
    List<CommodityStockDO> commodityStockList = saveDataOptional.getCommodityStockList();

    List<OrderDetailDO> orderDetailList = saveDataOptional.getOrderDetailList();
    if (!CollectionUtils.isEmpty(orderDetailList)) {
      for (CommodityStockDO commodityStockDO : commodityStockList) {
        Long orderDetailId = OrderSaveHelper.findOrderDetailId(orderDetailList, commodityStockDO);
        Assert.isTrue(Objects.nonNull(orderDetailId), String.format("commodity_stock order_detail_id 不能为空,%s",
            JsonUtils.toJson(commodityStockDO)));
        commodityStockDO.setOrderDetailId(orderDetailId);
      }
    }

    return commodityStockList;
  }
}
