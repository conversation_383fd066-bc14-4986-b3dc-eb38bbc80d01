package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.ErpBillInfoDO;
import com.yxt.order.atom.order.mapper.ErpBillInfoMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:58
 * @email: <EMAIL>
 */
@Component
public class ErpBillInfoInsert extends AbstractInsert<ErpBillInfoDO> {

  @Resource
  private ErpBillInfoMapper erpBillInfoMapper;

  @Override
  protected Boolean canInsert() {
    return Objects.nonNull(saveDataOptional.getErpBillInfo());
  }

  @Override
  protected Integer insert(ErpBillInfoDO erpBillInfoDO) {
    return erpBillInfoMapper.insert(erpBillInfoDO);
  }

  @Override
  protected ErpBillInfoDO data() {
    return saveDataOptional.getErpBillInfo();
  }
}
