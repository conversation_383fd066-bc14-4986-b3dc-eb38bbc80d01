package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderBusinessConsumerMessageDO;
import com.yxt.order.atom.order.mapper.OrderBusinessConsumerMessageMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:58
 * @email: <EMAIL>
 */
@Component
public class OrderBusinessConsumerMessageInsert extends
    AbstractInsert<OrderBusinessConsumerMessageDO> {

  @Resource
  private OrderBusinessConsumerMessageMapper orderBusinessConsumerMessageMapper;

  @Override
  protected Boolean canInsert() {
    return Objects.nonNull(saveDataOptional.getOrderBusinessConsumerMessage());
  }

  @Override
  protected Integer insert(OrderBusinessConsumerMessageDO data) {
    return orderBusinessConsumerMessageMapper.insert(data);
  }

  @Override
  protected OrderBusinessConsumerMessageDO data() {
    return saveDataOptional.getOrderBusinessConsumerMessage();
  }
}
