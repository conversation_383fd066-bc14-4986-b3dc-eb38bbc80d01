package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderDeliveryAddressDO;
import com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:57
 * @email: <EMAIL>
 */
@Component
public class OrderDeliveryAddressInsert extends AbstractInsert<OrderDeliveryAddressDO> {

  @Resource
  private OrderDeliveryAddressMapper orderDeliveryAddressMapper;

  @Override
  protected Boolean canInsert() {
    return Objects.nonNull(saveDataOptional.getOrderDeliveryAddress());
  }

  @Override
  protected Integer insert(OrderDeliveryAddressDO orderDeliveryAddressDO) {
    return orderDeliveryAddressMapper.insert(orderDeliveryAddressDO);
  }

  @Override
  protected OrderDeliveryAddressDO data() {
    return saveDataOptional.getOrderDeliveryAddress();
  }
}
