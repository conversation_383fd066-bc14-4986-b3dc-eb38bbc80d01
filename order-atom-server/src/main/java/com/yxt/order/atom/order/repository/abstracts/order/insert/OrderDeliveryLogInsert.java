package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderDeliveryLogDO;
import com.yxt.order.atom.order.mapper.OrderDeliveryLogMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:57
 * @email: <EMAIL>
 */
@Component
public class OrderDeliveryLogInsert extends AbstractInsert<OrderDeliveryLogDO> {

  @Resource
  private OrderDeliveryLogMapper orderDeliveryLogMapper;

  @Override
  protected Boolean canInsert() {
    return Objects.nonNull(saveDataOptional.getOrderDeliveryLog());
  }

  @Override
  protected Integer insert(OrderDeliveryLogDO orderDeliveryLogDO) {
    return orderDeliveryLogMapper.insert(orderDeliveryLogDO);
  }

  @Override
  protected OrderDeliveryLogDO data() {
    return saveDataOptional.getOrderDeliveryLog();
  }
}
