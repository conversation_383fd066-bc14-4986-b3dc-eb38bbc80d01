package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderGiftInfoDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.batch.OrderGiftInfoBatchRepository;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:58
 * @email: <EMAIL>
 */
@Component
public class OrderGiftInfoInsert extends AbstractInsert<List<OrderGiftInfoDO>> {

  @Resource
  private OrderGiftInfoBatchRepository orderGiftInfoBatchRepository;

  @Override
  protected Boolean canInsert() {
    return !CollectionUtils.isEmpty(saveDataOptional.getOrderGiftInfoList());
  }

  @Override
  protected Integer insert(List<OrderGiftInfoDO> list) {
    return orderGiftInfoBatchRepository.saveBatch(list) ? list.size() : 0;
  }

  @Override
  protected List<OrderGiftInfoDO> data() {
    return saveDataOptional.getOrderGiftInfoList();
  }
}
