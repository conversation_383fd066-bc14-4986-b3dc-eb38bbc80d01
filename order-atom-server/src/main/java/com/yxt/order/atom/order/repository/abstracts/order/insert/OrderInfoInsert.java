package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.yxt.order.atom.order.entity.OrderInfoDO;
import com.yxt.order.atom.order.mapper.OrderInfoMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 14:56
 * @email: <EMAIL>
 */
@Component
public class OrderInfoInsert extends AbstractInsert<OrderInfoDO> {

  @Resource
  private OrderInfoMapper orderInfoMapper;

  @Override
  protected Boolean canInsert() {
    return Objects.nonNull(saveDataOptional.getOrderInfo());
  }

  @Override
  protected Integer insert(OrderInfoDO orderInfoDO) {
    return orderInfoMapper.insert(orderInfoDO);
  }

  @Override
  protected OrderInfoDO data() {
    return saveDataOptional.getOrderInfo();
  }


}
