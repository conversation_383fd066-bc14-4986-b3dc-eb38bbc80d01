package com.yxt.order.atom.order.repository.abstracts.order.insert;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.entity.OrderPickInfoDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.abstracts.order.InsertOrder;
import com.yxt.order.atom.order.repository.batch.OrderPickInfoBatchRepository;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> hua❀
 * @Date 2024-11-21 10:56
 * @Version 1.0
 **/
@Component
@Order(InsertOrder.OrderPickInfoInsert)
public class OrderPickInfoInsert extends AbstractInsert<List<OrderPickInfoDO>> {

    @Resource
    private OrderPickInfoBatchRepository pickInfoBatchRepository;

    @Override
    protected Boolean canInsert() {
        return !CollectionUtils.isEmpty(saveDataOptional.getOrderPickInfoList());
    }

    @Override
    protected Integer insert(List<OrderPickInfoDO> list) {
        return pickInfoBatchRepository.saveBatch(list) ? list.size() : 0;
    }


    @Override
    protected List<OrderPickInfoDO> data() {
        List<OrderPickInfoDO> orderPickInfoList = saveDataOptional.getOrderPickInfoList();
        OrderDetailDO orderDetailList = saveDataOptional.getOrderDetailList().get(0);
        if(Objects.nonNull(orderDetailList)){
            orderPickInfoList.forEach(a->a.setOrderDetailId(orderDetailList.getId()));
        }
        return orderPickInfoList;
    }
}
