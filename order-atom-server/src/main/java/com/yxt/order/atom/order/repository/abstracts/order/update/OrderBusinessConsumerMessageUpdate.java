package com.yxt.order.atom.order.repository.abstracts.order.update;

import cn.hutool.core.bean.BeanUtil;
import com.yxt.order.atom.order.entity.OrderBusinessConsumerMessageDO;
import com.yxt.order.atom.order.mapper.OrderBusinessConsumerMessageMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractUpdate;
import com.yxt.order.atom.sdk.common.data.OrderBusinessConsumerMessageDTO;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 16:00
 * @email: <EMAIL>
 */
@Component
public class OrderBusinessConsumerMessageUpdate extends
    AbstractUpdate<OrderBusinessConsumerMessageDO> {

  @Resource
  private OrderBusinessConsumerMessageMapper orderBusinessConsumerMessageMapper;

  @Override
  protected Boolean canUpdate() {
    OrderBusinessConsumerMessageDTO dto = req.getOrderBusinessConsumerMessageDto();
    if (Objects.isNull(dto)) {
      return false;
    }

    Assert.isTrue(!StringUtils.isEmpty(dto.getId()), "id can not null");
    return Boolean.TRUE;
  }

  @Override
  protected Integer update(OrderBusinessConsumerMessageDO orderBusinessConsumerMessageDO) {
    return orderBusinessConsumerMessageMapper.updateById(orderBusinessConsumerMessageDO);
  }

  @Override
  protected OrderBusinessConsumerMessageDO convert() {
    OrderBusinessConsumerMessageDTO dto = req.getOrderBusinessConsumerMessageDto();
    return BeanUtil.copyProperties(dto, OrderBusinessConsumerMessageDO.class);
  }
}
