package com.yxt.order.atom.order.repository.abstracts.order.update;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.order.entity.OrderDeliveryAddressDO;
import com.yxt.order.atom.order.mapper.OrderDeliveryAddressMapper;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractUpdate;
import com.yxt.order.atom.sdk.common.data.OrderDeliveryAddressDTO;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 16:00
 * @email: <EMAIL>
 */
@Component
public class OrderDeliveryAddressUpdate extends AbstractUpdate<OrderDeliveryAddressDO> {

  @Resource
  private OrderDeliveryAddressMapper orderDeliveryAddressMapper;

  @Override
  protected Boolean canUpdate() {
    OrderDeliveryAddressDTO dto = req.getOrderDeliveryAddressDto();
    if (Objects.isNull(dto)) {
      return false;
    }
    
    // 统一通过 orderNo 来更新明细,系统遗留问题: b2c会存储2条记录,o2o只会有1条记录
    Assert.isTrue(!StringUtils.isEmpty(dto.getOrderNo()), "orderNo can not null");
    return Boolean.TRUE;
  }

  @Override
  protected Integer update(OrderDeliveryAddressDO orderDeliveryAddressDO) {
    LambdaQueryWrapper<OrderDeliveryAddressDO> wrapper = Wrappers.lambdaQuery();
    wrapper.eq(OrderDeliveryAddressDO::getOrderNo, orderDeliveryAddressDO.getOrderNo());
    return orderDeliveryAddressMapper.update(orderDeliveryAddressDO, wrapper);
  }

  @Override
  protected OrderDeliveryAddressDO convert() {
    return BeanUtil.copyProperties(req.getOrderDeliveryAddressDto(),OrderDeliveryAddressDO.class);
  }
}
