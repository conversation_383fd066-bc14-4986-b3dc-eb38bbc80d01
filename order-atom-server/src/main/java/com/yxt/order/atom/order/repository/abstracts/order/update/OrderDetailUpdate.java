package com.yxt.order.atom.order.repository.abstracts.order.update;

import cn.hutool.core.bean.BeanUtil;
import com.yxt.order.atom.order.entity.OrderDetailDO;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractUpdate;
import com.yxt.order.atom.order.repository.batch.OrderDetailBatchRepository;
import com.yxt.order.atom.sdk.common.data.OrderDetailDTO;
import java.util.List;
import javax.annotation.Resource;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月13日 18:08
 * @email: <EMAIL>
 */
@Component
public class OrderDetailUpdate extends AbstractUpdate<List<OrderDetailDO>> {

  @Resource
  private OrderDetailBatchRepository orderDetailBatchRepository;

  @Override
  protected Boolean canUpdate() {
    List<OrderDetailDTO> orderDetailDtoList = req.getOrderDetailDtoList();
    if (CollectionUtils.isEmpty(orderDetailDtoList)) {
      return false;
    }

    // 强制校验Id
    orderDetailDtoList.forEach(
        dto -> Assert.isTrue(!StringUtils.isEmpty(dto.getId()), "id can not null"));

    return Boolean.TRUE;
  }

  @Override
  protected Integer update(List<OrderDetailDO> list) {
    return orderDetailBatchRepository.updateBatchById(list) ? list.size() : 0;
  }

  @Override
  protected List<OrderDetailDO> convert() {
    return BeanUtil.copyToList(req.getOrderDetailDtoList(),OrderDetailDO.class);
  }
}