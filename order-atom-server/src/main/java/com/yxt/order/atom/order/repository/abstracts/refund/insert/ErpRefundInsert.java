package com.yxt.order.atom.order.repository.abstracts.refund.insert;

import cn.hutool.core.collection.CollUtil;
import com.yxt.order.atom.order.entity.ErpRefundInfoDO;
import com.yxt.order.atom.order.entity.RefundDetailDO;
import com.yxt.order.atom.order.repository.abstracts.refund.AbstractRefundInsert;
import com.yxt.order.atom.order.repository.batch.ErpRefundBatchRepository;
import com.yxt.order.atom.order.repository.batch.RefundDetailBatchRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ErpRefundInsert extends AbstractRefundInsert<List<ErpRefundInfoDO>> {

  @Autowired
  private ErpRefundBatchRepository erpRefundBatchRepository;

  @Override
  protected Boolean canInsert() {
    return CollUtil.isNotEmpty(data());
  }

  @Override
  protected Integer insert(List<ErpRefundInfoDO> erpRefundInfoList) {
    return erpRefundBatchRepository.saveBatch(erpRefundInfoList) ? erpRefundInfoList.size() : 0;
  }

  /**
   * 转换成目标对象T
   *
   * @return
   */
  @Override
  protected List<ErpRefundInfoDO> data() {
    return this.saveData.getErpRefundList();
  }
}
