package com.yxt.order.atom.order.repository.abstracts.refund.update;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.order.entity.ErpRefundInfoDO;
import com.yxt.order.atom.order.entity.RefundDetailDO;
import com.yxt.order.atom.order.repository.abstracts.refund.AbstractRefundUpdate;
import com.yxt.order.atom.order.repository.batch.ErpRefundBatchRepository;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

@Component
public class ErpRefundUpdate extends AbstractRefundUpdate<List<ErpRefundInfoDO>> {

  @Autowired
  private ErpRefundBatchRepository erpRefundBatchRepository;

  @Override
  protected Boolean canUpdate() {
    List<ErpRefundInfoDO> erpRefundInfoDOS = data();
    if(CollUtil.isEmpty(erpRefundInfoDOS)){
      return false;
    }
    // 强制校验Id
    erpRefundInfoDOS.forEach(dto -> Assert.isTrue(!StringUtils.isEmpty(dto.getId()), "id can not null"));
    return true;
  }

  @Override
  protected Integer update(List<ErpRefundInfoDO> erpRefundList) {
      return erpRefundBatchRepository.updateBatchById(erpRefundList) ? erpRefundList.size() : 0;
  }

  /**
   * 转换成目标对象T
   *
   * @return
   */
  @Override
  protected List<ErpRefundInfoDO> data() {
    return this.saveData.getErpRefundList();
  }

}
