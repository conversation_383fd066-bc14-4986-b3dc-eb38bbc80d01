package com.yxt.order.atom.order.repository.abstracts.refund.update;

import cn.hutool.core.collection.CollUtil;
import com.yxt.order.atom.order.entity.RefundDetailDO;
import com.yxt.order.atom.order.repository.abstracts.refund.AbstractRefundUpdate;
import com.yxt.order.atom.order.repository.batch.RefundDetailBatchRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

@Component
public class RefundDetailUpdate extends AbstractRefundUpdate<List<RefundDetailDO>> {

  @Autowired
  private RefundDetailBatchRepository refundDetailBatchRepository;

  @Override
  protected Boolean canUpdate() {
    List<RefundDetailDO> detailDOS = data();
    if(CollUtil.isEmpty(detailDOS)){
      return false;
    }
    // 强制校验Id
    detailDOS.forEach(dto -> Assert.isTrue(!StringUtils.isEmpty(dto.getId()), "id can not null"));
    return true;
  }

  @Override
  protected Integer update(List<RefundDetailDO> refundDetailDOS) {
    return refundDetailBatchRepository.updateBatchById(refundDetailDOS) ? refundDetailDOS.size() : 0;
  }

  /**
   * 转换成目标对象T
   *
   * @return
   */
  @Override
  protected List<RefundDetailDO> data() {
    return this.saveData.getRefundDetailList();
  }
}
