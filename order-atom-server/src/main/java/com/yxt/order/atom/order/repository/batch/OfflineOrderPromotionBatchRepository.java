package com.yxt.order.atom.order.repository.batch;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order.entity.OfflineOrderPromotionDO;
import com.yxt.order.atom.order.mapper.OfflineOrderPromotionMapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 17:05
 * @email: <EMAIL>
 */
@Repository
public class OfflineOrderPromotionBatchRepository extends
    ServiceImpl<OfflineOrderPromotionMapper, OfflineOrderPromotionDO> {

}
