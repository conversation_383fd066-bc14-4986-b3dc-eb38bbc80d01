package com.yxt.order.atom.order.repository.batch;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailPickDO;
import com.yxt.order.atom.order.mapper.OfflineRefundOrderDetailPickMapper;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年04月07日 17:05
 * @email: <EMAIL>
 */
@Repository
public class OfflineRefundOrderDetailPickBatchRepository extends
    ServiceImpl<OfflineRefundOrderDetailPickMapper, OfflineRefundOrderDetailPickDO> {

}
