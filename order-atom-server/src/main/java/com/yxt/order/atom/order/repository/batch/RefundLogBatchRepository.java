package com.yxt.order.atom.order.repository.batch;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order.entity.RefundLogDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.mapper.RefundLogMapper;
import com.yxt.order.atom.order.mapper.RefundOrderMapper;
import org.springframework.stereotype.Repository;


@Repository
public class RefundLogBatchRepository extends
    ServiceImpl<RefundLogMapper, RefundLogDO> {

}

