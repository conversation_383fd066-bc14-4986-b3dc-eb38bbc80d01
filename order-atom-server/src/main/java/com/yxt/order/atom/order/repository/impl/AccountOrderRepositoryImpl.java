package com.yxt.order.atom.order.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.order.converter.AccountOrder2DtoConverter;
import com.yxt.order.atom.order.entity.AccountOrderDO;
import com.yxt.order.atom.order.entity.OfflineOrderOrganizationDO;
import com.yxt.order.atom.order.mapper.AccountOrderMapper;
import com.yxt.order.atom.order.mapper.OrderDeliveryRecordMapper;
import com.yxt.order.atom.order.repository.AccountOrderRepository;
import com.yxt.order.atom.sdk.common.data.AccountOrderDTO;
import com.yxt.order.atom.sdk.online_order.account.dto.req.ApplyAccountOrderReqDto;
import com.yxt.order.atom.sdk.online_order.account.dto.req.ApplyAccountRefundOrderReqDto;
import com.yxt.order.types.order.OrderNo;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR> (moatkon)
 * @date 2024年02月28日 18:22
 * @email: <EMAIL>
 */
@Repository
public class AccountOrderRepositoryImpl implements AccountOrderRepository {

  @Resource
  private AccountOrderMapper accountOrderMapper ;


  @Override
  public Boolean insertAccountOrder(ApplyAccountOrderReqDto req) {
    return null;
  }

  @Override
  public AccountOrderDTO getAccountOrderByOrderNo(OrderNo orderNo) {
    AccountOrder2DtoConverter instance = AccountOrder2DtoConverter.INSTANCE;
    AccountOrderDO accountOrderDO = accountOrderMapper.selectOne(
        new LambdaQueryWrapper<AccountOrderDO>().eq(
            AccountOrderDO::getOrderNo, orderNo.getOrderNo()).eq(AccountOrderDO::getDeleted, 0L));
    return instance.toDto(accountOrderDO);
  }


}
