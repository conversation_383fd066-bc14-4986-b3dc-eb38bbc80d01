package com.yxt.order.atom.order.repository.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.migration.config.ThreadPoolConfig;
import com.yxt.order.atom.order.converter.RefundInfoConverter;
import com.yxt.order.atom.order.entity.ErpRefundInfoDO;
import com.yxt.order.atom.order.entity.OrderLogDO;
import com.yxt.order.atom.order.entity.RefundCheckDO;
import com.yxt.order.atom.order.entity.RefundDetailDO;
import com.yxt.order.atom.order.entity.RefundLogDO;
import com.yxt.order.atom.order.entity.RefundOrderDO;
import com.yxt.order.atom.order.mapper.ErpRefundInfoMapper;
import com.yxt.order.atom.order.mapper.RefundDetailMapper;
import com.yxt.order.atom.order.mapper.RefundOrderMapper;
import com.yxt.order.atom.order.repository.OrderRepository;
import com.yxt.order.atom.order.repository.RefundRepository;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractInsert;
import com.yxt.order.atom.order.repository.abstracts.order.AbstractUpdate;
import com.yxt.order.atom.order.repository.abstracts.order.SaveOrderOptionalDO;
import com.yxt.order.atom.order.repository.abstracts.refund.AbstractRefundInsert;
import com.yxt.order.atom.order.repository.abstracts.refund.AbstractRefundUpdate;
import com.yxt.order.atom.order.repository.abstracts.refund.SaveRefundOptionalDO;
import com.yxt.order.atom.sdk.common.data.ErpBillInfoDTO;
import com.yxt.order.atom.sdk.common.data.ErpRefundInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderCommodityDetailCostPriceDTO;
import com.yxt.order.atom.sdk.common.data.OrderDetailCommodityCostPriceDTO;
import com.yxt.order.atom.sdk.common.data.OrderDetailDTO;
import com.yxt.order.atom.sdk.common.data.OrderInfoDTO;
import com.yxt.order.atom.sdk.common.data.OrderPickInfoDTO;
import com.yxt.order.atom.sdk.common.data.RefundDetailDTO;
import com.yxt.order.atom.sdk.common.data.RefundOrderDTO;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.OrderInfoQryByScaleReqDto;
import com.yxt.order.atom.sdk.online_order.order_info.dto.req.UpdateOrderOptionalReq;
import com.yxt.order.atom.sdk.online_order.order_info.dto.res.FullOrderDtoResDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.ErpRefundQueryReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundAuditOptionalReq;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundDetailInsertReq;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundInfoQryBatchReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundInfoQryReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundLogOptionalReq;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.req.RefundWithOrderQryReqDto;
import com.yxt.order.atom.sdk.online_order.refund_info.dto.res.RefundInfoQryResDto;
import com.yxt.order.common.utils.CompletableFutureUtils;
import com.yxt.order.types.DsConstants;
import com.yxt.order.types.order.OrderNo;
import com.yxt.order.types.order.RefundOrderNo;
import com.yxt.order.types.order.enums.RefundQryScaleEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Slf4j
public class RefundRepositoryImpl implements RefundRepository {

  @Autowired
  private RefundOrderMapper refundOrderMapper;

  @Autowired
  private RefundDetailMapper refundDetailMapper;

  @Autowired
  private ErpRefundInfoMapper erpRefundInfoMapper;

  @Resource
  private List<AbstractUpdate<?>> abstractOrderUpdateList;

  @Resource
  private List<AbstractInsert<?>> abstractOrderInsertList;

  @Resource
  private List<AbstractRefundInsert<?>> abstractRefundInsertList;

  @Resource
  private List<AbstractRefundUpdate<?>> abstractRefundUpdateList;

  @Resource(name = ThreadPoolConfig.ORDER_BATCH_SEARCH_POOL)
  private Executor orderSearchPool;

  @Resource(name = ThreadPoolConfig.ORDER_BATCH_SEARCH_SUB_POOL)
  private Executor orderSearchSubPool;

  @DS(DsConstants.DB_ORDER_SLAVE)
  @Override
  public RefundInfoQryResDto queryRefundInfo(RefundInfoQryReqDto request) {
    RefundOrderDTO refundMainInfo = queryRefundMainInfo(request);
    if (ObjectUtil.isNull(refundMainInfo)) {
      return null;
    }
    List<CompletableFuture<Void>> futureList = new ArrayList<>();
    AtomicReference<RefundInfoQryResDto> refundInfoQryResDto = new AtomicReference<>(new RefundInfoQryResDto());
    for (RefundQryScaleEnum qryScale : request.getQryScaleList()) {
      switch (qryScale) {
        case MAIN:
          refundInfoQryResDto.get().setRefundMainInfo(refundMainInfo);
          break;
        case DETAIL:
          CompletableFuture<Void> detailFuture = CompletableFuture.runAsync(() -> {
            List<RefundDetailDTO> refundDetailInfos = queryRefundDetailInfo(request.getRefundOrderNo());
            refundInfoQryResDto.get().setRefundDetailList(refundDetailInfos);
          }, orderSearchSubPool);
          futureList.add(detailFuture);
          break;
        case ERP:
          CompletableFuture<Void> erpFuture = CompletableFuture.runAsync(() -> {
            refundInfoQryResDto.get().setErpRefundInfoDTO(queryRefundErpInfo(request.getRefundOrderNo()));
          }, orderSearchSubPool);
          futureList.add(erpFuture);
          break;
        case OTHER_REFUND:
          CompletableFuture<Void> otherRefundFuture = CompletableFuture.runAsync(() -> {
            List<RefundOrderDTO> otherRefundOrderList = getOtherRefundOrderList(refundMainInfo.getOrderNo(), request.getRefundOrderNo().getRefundOrderNo());
            refundInfoQryResDto.get().setOtherRefundOrderList(otherRefundOrderList);
          }, orderSearchSubPool);
          futureList.add(otherRefundFuture);
          break;
        case OTHER_REFUND_DETAIL:
          CompletableFuture<Void> otherRefundDetailFuture = CompletableFuture.runAsync(() -> {
            List<Long> otherRefundOrderNoList = new ArrayList<>();
            if (CollUtil.isNotEmpty(refundInfoQryResDto.get().getOtherRefundOrderList())) {
              otherRefundOrderNoList = refundInfoQryResDto.get().getOtherRefundOrderList().stream().map(RefundOrderDTO::getRefundNo).collect(Collectors.toList());
            } else {
              List<RefundOrderDTO> otherRefundOrderList1 = getOtherRefundOrderList(refundMainInfo.getOrderNo(), request.getRefundOrderNo().getRefundOrderNo());
              otherRefundOrderNoList = otherRefundOrderList1.stream().map(RefundOrderDTO::getRefundNo).collect(Collectors.toList());
            }
            if (CollUtil.isNotEmpty(otherRefundOrderNoList)) {
              List<RefundDetailDTO> otherRefundDetailList = queryOtherRefundDetailList(otherRefundOrderNoList);
              refundInfoQryResDto.get().setOtherRefundDetailList(otherRefundDetailList);
            }
          }, orderSearchSubPool);
          futureList.add(otherRefundDetailFuture);
          break;
        default:
          break;
      }
    }
    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
    return refundInfoQryResDto.get();
  }

  private List<RefundDetailDTO> queryOtherRefundDetailList(List<Long> otherRefundOrderNoList) {
    List<RefundDetailDO> refundDetailDOList = refundDetailMapper.selectList(Wrappers.<RefundDetailDO>lambdaQuery()
        .in(RefundDetailDO::getRefundNo, otherRefundOrderNoList));
    if (CollUtil.isEmpty(refundDetailDOList)) {
      return new ArrayList<>(0);
    }
    return RefundInfoConverter.toRefundDetailInfo(refundDetailDOList);
  }

  private List<RefundOrderDTO> getOtherRefundOrderList(Long orderNo, Long refundOrderNo) {
    List<RefundOrderDO> refundOrderList = refundOrderMapper.selectList(Wrappers.<RefundOrderDO>lambdaQuery()
        .eq(RefundOrderDO::getOrderNo, orderNo)
        .ne(RefundOrderDO::getRefundNo, refundOrderNo)
    );
    if (CollUtil.isEmpty(refundOrderList)) {
      return new ArrayList<>();
    }
    return RefundInfoConverter.toRefundInfoList(refundOrderList);
  }


  @Override
  public RefundOrderDTO queryLatestRefundInfo(RefundWithOrderQryReqDto request) {

    List<RefundOrderDO> refundOrderList = refundOrderMapper.selectList(Wrappers.<RefundOrderDO>lambdaQuery()
        .eq(RefundOrderDO::getOrderNo, request.getOrderNo().getOrderNo())
        .orderByDesc(RefundOrderDO::getCreateTime)
        .last(" LIMIT 1 ")
    );
    if (CollUtil.isEmpty(refundOrderList)) {
      return null;
    }
    RefundOrderDO refundOrderDO = refundOrderList.get(0);
    return RefundInfoConverter.toRefundMainInfo(refundOrderDO);
  }

  public RefundOrderDTO queryRefundMainInfo(RefundInfoQryReqDto request) {
    LambdaQueryWrapper<RefundOrderDO> wrapper = Wrappers.<RefundOrderDO>lambdaQuery()
        .eq(RefundOrderDO::getRefundNo, request.getRefundOrderNo().getRefundOrderNo());
    if (ObjectUtil.isNotNull(request.getRefundState())) {
      wrapper.eq(RefundOrderDO::getState, request.getRefundState().getCode());
    }
    if (ObjectUtil.isNotNull(request.getRefundErpStatus())) {
      wrapper.eq(RefundOrderDO::getErpState, request.getRefundErpStatus().getCode());
    } if (ObjectUtil.isNotNull(request.getDataVersion())) {
      wrapper.eq(RefundOrderDO::getDataVersion, request.getDataVersion().getDataVersion());
    }

    List<RefundOrderDO> refundOrderList = refundOrderMapper.selectList(wrapper);

    if (CollUtil.isEmpty(refundOrderList)) {
      return null;
    }
    RefundOrderDO refundOrderDO = refundOrderList.get(0);
    return RefundInfoConverter.toRefundMainInfo(refundOrderDO);
  }

  public List<RefundOrderDTO> queryRefundByOrderNo(Long orderNo) {
    List<RefundOrderDO> refundOrderList = refundOrderMapper.selectList(Wrappers.<RefundOrderDO>lambdaQuery()
        .eq(RefundOrderDO::getOrderNo,orderNo));
    if (CollUtil.isEmpty(refundOrderList)) {
      return null;
    }

    return RefundInfoConverter.toRefundInfoList(refundOrderList);
  }

  public List<RefundDetailDTO> queryRefundDetailInfo(RefundOrderNo refundOrderNo) {
    List<RefundDetailDO> refundDetailDOList = refundDetailMapper.selectList(Wrappers.<RefundDetailDO>lambdaQuery()
        .eq(RefundDetailDO::getRefundNo, refundOrderNo.getRefundOrderNo()));
    if (CollUtil.isEmpty(refundDetailDOList)) {
      return new ArrayList<>(0);
    }
    return RefundInfoConverter.toRefundDetailInfo(refundDetailDOList);
  }

  public ErpRefundInfoDTO queryRefundErpInfo(RefundOrderNo refundOrderNo) {
    List<ErpRefundInfoDO> erpRefundList = erpRefundInfoMapper.selectList(Wrappers.<ErpRefundInfoDO>lambdaQuery()
        .eq(ErpRefundInfoDO::getRefundNo, refundOrderNo.getRefundOrderNo()));
    if (CollUtil.isEmpty(erpRefundList)) {
      return null;
    }
    return RefundInfoConverter.toErpRefundInfo(erpRefundList.get(0));
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void saveRefundAuditResult(RefundAuditOptionalReq request) {

    List<RefundOrderDTO> refundOrderUpdateList = new ArrayList<>();
    List<RefundDetailDO> refundDetailUpdateList = new ArrayList<>();
    List<RefundDetailDO> refundDetailInsertList = new ArrayList<>();
    List<RefundOrderDTO> refundOrderInsertList = new ArrayList<>();
    List<ErpRefundInfoDTO> erpRefundInsertList = new ArrayList<>();
    //需要更新的订单信息
    UpdateOrderOptionalReq orderUpdateOptional = new UpdateOrderOptionalReq();
    //需要更新的邮费单信息
    UpdateOrderOptionalReq freightOrderUpdateOptional = new UpdateOrderOptionalReq();
    //需要更新的退款单信息
    SaveRefundOptionalDO refundUpdateOptionalDO = new SaveRefundOptionalDO();
    if(ObjectUtil.isNotNull(request.getUpdateRefundEntity())){
      RefundOrderDTO refundOrder = request.getUpdateRefundEntity().getRefundOrder();
      if(ObjectUtil.isNotNull(refundOrder)){
        refundOrderUpdateList.add(refundOrder);
      }

      List<RefundOrderDTO> otherRefundList = request.getUpdateRefundEntity().getOtherRefundList();
      if(CollUtil.isNotEmpty(otherRefundList)){
        refundOrderUpdateList.addAll(otherRefundList);
      }

      RefundOrderDTO freightRefundInfo = request.getUpdateRefundEntity().getFreightRefundInfo();
      if(ObjectUtil.isNotNull(freightRefundInfo)){
        refundOrderUpdateList.add(freightRefundInfo);
      }

      List<RefundDetailDTO> refundDetailList = request.getUpdateRefundEntity().getRefundDetailList();
      if(CollUtil.isNotEmpty(refundDetailList)){
        refundDetailUpdateList.addAll(BeanUtil.copyToList(refundDetailList, RefundDetailDO.class));
      }
      ErpRefundInfoDTO erpRefundInfo = request.getUpdateRefundEntity().getErpRefundInfo();
      if(ObjectUtil.isNotNull(erpRefundInfo)){
        refundUpdateOptionalDO.setErpRefundList(ListUtil.toList(BeanUtil.toBean(erpRefundInfo, ErpRefundInfoDO.class)));
      }

      //订单部分
      OrderInfoDTO orderInfo = request.getUpdateRefundEntity().getOrderInfo();
      if(ObjectUtil.isNotNull(orderInfo)){
        orderUpdateOptional.setOrderInfoDto(orderInfo);
      }

      List<OrderDetailDTO> orderDetailList = request.getUpdateRefundEntity().getOrderDetailList();
      if(CollUtil.isNotEmpty(orderDetailList)){
        orderUpdateOptional.setOrderDetailDtoList(orderDetailList);
      }

      OrderInfoDTO freightOrderInfo = request.getUpdateRefundEntity().getFreightOrderInfo();
      if(ObjectUtil.isNotNull(freightOrderInfo)){
        freightOrderUpdateOptional.setOrderInfoDto(freightOrderInfo);
      }

      ErpBillInfoDTO erpBillInfo = request.getUpdateRefundEntity().getErpBillInfo();
      if(ObjectUtil.isNotNull(erpBillInfo)){
        orderUpdateOptional.setBillInfoDTO(erpBillInfo);
      }

      List<OrderDetailCommodityCostPriceDTO> orderDetailCommodityCostPriceList = request.getUpdateRefundEntity().getOrderDetailCommodityCostPriceList();
      if(CollUtil.isNotEmpty(orderDetailCommodityCostPriceList)){
        orderUpdateOptional.setOrderDetailCommodityCostPriceList(orderDetailCommodityCostPriceList);
      }

      List<OrderCommodityDetailCostPriceDTO> orderCommodityDetailCostPriceList = request.getUpdateRefundEntity().getOrderCommodityDetailCostPriceList();
      if(CollUtil.isNotEmpty(orderCommodityDetailCostPriceList)){
        orderUpdateOptional.setOrderCommodityDetailCostPriceList(orderCommodityDetailCostPriceList);
      }

      List<OrderPickInfoDTO> orderPickInfoList = request.getUpdateRefundEntity().getOrderPickInfoList();
      if(CollUtil.isNotEmpty(orderPickInfoList)){
        orderUpdateOptional.setOrderPickDtoList(orderPickInfoList);
      }
    }

    if(ObjectUtil.isNotNull(request.getInsertRefundEntity())){
      RefundOrderDTO freightRefundOrder = request.getInsertRefundEntity().getFreightRefundOrder();
      if(ObjectUtil.isNotNull(freightRefundOrder)){
        refundOrderInsertList.add(freightRefundOrder);
      }
      List<RefundDetailDTO> freightRefundDetailList = request.getInsertRefundEntity().getFreightRefundDetailList();
      if(CollUtil.isNotEmpty(freightRefundDetailList)){
        refundDetailInsertList.addAll(BeanUtil.copyToList(freightRefundDetailList, RefundDetailDO.class));
      }
      ErpRefundInfoDTO freightErpRefundInfo = request.getInsertRefundEntity().getFreightErpRefundInfo();
      if (ObjectUtil.isNotNull(freightErpRefundInfo)){
        erpRefundInsertList.add(freightErpRefundInfo);
      }
      ErpRefundInfoDTO erpRefundInfo = request.getInsertRefundEntity().getErpRefundInfo();
      if (ObjectUtil.isNotNull(erpRefundInfo)){
        erpRefundInsertList.add(erpRefundInfo);
      }
    }
    refundUpdateOptionalDO.setRefundOrderList(BeanUtil.copyToList(refundOrderUpdateList, RefundOrderDO.class));
    refundUpdateOptionalDO.setRefundDetailList(refundDetailUpdateList);

    //需要新增的退款单信息
    SaveRefundOptionalDO refundInsertOptionalDO = new SaveRefundOptionalDO();
    refundInsertOptionalDO.setRefundOrderList(BeanUtil.copyToList(refundOrderUpdateList, RefundOrderDO.class));
    refundInsertOptionalDO.setRefundDetailList(refundDetailUpdateList);
    refundInsertOptionalDO.setErpRefundList(BeanUtil.copyToList(erpRefundInsertList, ErpRefundInfoDO.class));

    for (AbstractUpdate<?> abstractUpdate : abstractOrderUpdateList) {
      abstractUpdate.exec(orderUpdateOptional);
      abstractUpdate.exec(freightOrderUpdateOptional);
    }
    for (AbstractRefundInsert<?> abstractRefundInsert : abstractRefundInsertList) {
      abstractRefundInsert.exec(refundInsertOptionalDO);
    }
    for (AbstractRefundUpdate<?> abstractRefundUpdate : abstractRefundUpdateList) {
      abstractRefundUpdate.exec(refundUpdateOptionalDO);
    }
  }

  @Override
  public void saveRefundLog(RefundLogOptionalReq request) {
    SaveRefundOptionalDO refundOptionalDO = new SaveRefundOptionalDO();
    SaveOrderOptionalDO orderOptionalDO = new SaveOrderOptionalDO();
    if(ObjectUtil.isNotNull(request.getRefundLog())){
      refundOptionalDO.setRefundLogList(ListUtil.toList(BeanUtil.toBean(request.getRefundLog(), RefundLogDO.class)));
    }
    if(ObjectUtil.isNotNull(request.getRefundCheck())){
      refundOptionalDO.setRefundCheckList(ListUtil.toList(BeanUtil.toBean(request.getRefundCheck(), RefundCheckDO.class)));
    }
    if(ObjectUtil.isNotNull(request.getOrderLog())){
      orderOptionalDO.setOrderLogList(ListUtil.toList(BeanUtil.toBean(request.getOrderLog(), OrderLogDO.class)));
    }
    for (AbstractInsert<?> abstractUpdate : abstractOrderInsertList) {
      abstractUpdate.exec(orderOptionalDO);
    }
    for (AbstractRefundInsert<?> abstractRefundInsert : abstractRefundInsertList) {
      abstractRefundInsert.exec(refundOptionalDO);
    }
  }

  @Override
  public List<ErpRefundInfoDTO> queryErpRefund(ErpRefundQueryReqDto request) {
    List<ErpRefundInfoDO> erpRefundInfoDOS = erpRefundInfoMapper.selectList(Wrappers.<ErpRefundInfoDO>lambdaQuery()
        .in(ErpRefundInfoDO::getRefundNo, request.getRefundNoList()));
    if(CollUtil.isEmpty(erpRefundInfoDOS)){
      return new ArrayList<>(0);
    }
    return BeanUtil.copyToList(erpRefundInfoDOS, ErpRefundInfoDTO.class);
  }

  @Override
  public List<RefundDetailDTO> createRefundDetail(RefundDetailInsertReq request) {
    if(CollUtil.isNotEmpty(request.getRefundDetailDTOList())){
      for (RefundDetailDTO refundDetailDTO : request.getRefundDetailDTOList()) {
        RefundDetailDO detailDO = BeanUtil.toBean(refundDetailDTO, RefundDetailDO.class);
        refundDetailMapper.insert(detailDO);
        refundDetailDTO.setRefundNo(detailDO.getId());
      }
    }
    return request.getRefundDetailDTOList();
  }

  @Override
  public List<RefundInfoQryResDto> queryRefundInfoBatch(RefundInfoQryBatchReqDto request) {
    Function<RefundOrderNo, Supplier<RefundInfoQryResDto>> function = (refundOrderNo) -> () -> {
      RefundInfoQryReqDto tempRequest = new RefundInfoQryReqDto(request.getQryScaleList(), refundOrderNo);
      return SpringUtil.getBean(RefundRepository.class).queryRefundInfo(tempRequest);
    };
    return CompletableFutureUtils.supplyAsync(function, request.getRefundOrderNoList(), 10, orderSearchPool).stream().filter(Objects::nonNull).collect(Collectors.toList());
  }
}
