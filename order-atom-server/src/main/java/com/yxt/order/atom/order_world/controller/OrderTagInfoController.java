package com.yxt.order.atom.order_world.controller;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.repository.OrderTagInfoRepository;
import com.yxt.order.atom.sdk.order_world.OrderTagAtomCmdApi;
import com.yxt.order.atom.sdk.order_world.OrderTagAtomQryApi;
import com.yxt.order.atom.sdk.order_world.req.QueryOrderTagReq;
import com.yxt.order.atom.sdk.order_world.req.SaveOrderTagReq;
import com.yxt.order.atom.sdk.order_world.req.UpdateOrderTagReq;
import com.yxt.order.atom.sdk.order_world.res.QueryOrderTagRes;
import com.yxt.order.atom.sdk.order_world.res.QueryOrderTagSelectedRes;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import lombok.EqualsAndHashCode;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "订单标签")
public class OrderTagInfoController extends AbstractController implements OrderTagAtomCmdApi, OrderTagAtomQryApi {

  @Resource
  private OrderTagInfoRepository orderTagInfoRepository;

  @Override
  @ApiOperation("保存订单标签")
  public ResponseBase<Boolean> saveOrderTag(SaveOrderTagReq req) {
    return generateSuccess(orderTagInfoRepository.insertOrderTagInfo(req));
  }

  @Override
  @ApiOperation("更新订单标签")
  public ResponseBase<Boolean> updateOrderTag(UpdateOrderTagReq req) {
    return generateSuccess(orderTagInfoRepository.updateOrderTagInfoById(req));
  }

  @Override
  @ApiOperation("查询订单标签")
  public ResponseBase<PageDTO<QueryOrderTagRes>> pageOrderTag(QueryOrderTagReq req) {
    return generateSuccess(orderTagInfoRepository.pageOrderTagInfo(req));
  }

  @Override
  @ApiOperation("查询订单标签详情")
  public ResponseBase<QueryOrderTagRes> queryOrderTagById(String tagId) {
    return generateSuccess(orderTagInfoRepository.queryOrderTagById(tagId));
  }

  @Override
  @ApiOperation("查询订单标签下拉")
  public ResponseBase<List<QueryOrderTagSelectedRes>> queryOrderTagSelected() {
    return generateSuccess(orderTagInfoRepository.queryOrderTagSelected());
  }

  @Override
  @ApiOperation("根据标签编码查询订单标签")
  public ResponseBase<List<QueryOrderTagRes>> listOrderTagByCode(@RequestBody List<String> tagCodes) {
    return generateSuccess(orderTagInfoRepository.listOrderTagByCode(tagCodes));
  }
}
