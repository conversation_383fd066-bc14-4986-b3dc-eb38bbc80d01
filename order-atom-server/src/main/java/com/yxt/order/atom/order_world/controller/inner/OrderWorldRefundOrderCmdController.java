package com.yxt.order.atom.order_world.controller.inner;

import static com.yxt.order.atom.sdk.OrderAtomServiceName.ORDER_ENDPOINT;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.es.mapper.EsOrderWorldOrderMapper;
import com.yxt.order.atom.order.es.mapper.EsOrderWorldRefundOrderMapper;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class OrderWorldRefundOrderCmdController {


  @Resource
  private EsOrderWorldRefundOrderMapper esOrderWorldRefundOrderMapper;


  /**
   * 创建正单索引
   */
  @PostMapping(ORDER_ENDPOINT + "/es/order-world/refund/createIndex")
  public ResponseBase<Boolean> createOrderWorldRefundIndex() {
    return ResponseBase.success(esOrderWorldRefundOrderMapper.createIndex());
  }


}
