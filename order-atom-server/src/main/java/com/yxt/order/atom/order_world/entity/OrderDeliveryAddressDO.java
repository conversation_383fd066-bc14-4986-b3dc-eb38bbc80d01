package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.order.common.annotations.SensitiveField;
import com.yxt.order.common.annotations.SensitiveModel;
import java.util.Date;
import lombok.Data;

/**
 * 订单收货人信息
 */
@Data
@SensitiveModel
@TableName("offline_order_delivery_address")
public class OrderDeliveryAddressDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 内部订单号，自己生成
   */
  private String orderNo;

  /**
   * 收货方编码
   */
  private String receiverCode;

  /**
   * 收货人姓名
   */
  private String receiverName;

  /**
   * 收货人手机号
   */
  @SensitiveField
  private String receiverPhone;

  /**
   * 收货人固定电话
   */
  @SensitiveField
  private String receiverLandlinePhone;

  /**
   * 省份
   */
  private String province;

  /**
   * 城市
   */
  private String city;

  /**
   * 区
   */
  private String district;

  /**
   * 城镇
   */
  private String town;

  /**
   * 详细地址
   */
  private String address;

  /**
   * 邮编
   */
  private String zipCode;

  /**
   * 完整详细地址
   */
  private String fullAddress;

  /**
   * 原始地址
   */
  private String originalFullAddress;

  /**
   * 收货人平台OA唯一号
   */
  private String thirdOaid;

  /**
   * 隐私保护后的收货人姓名
   */
  private String receiverNamePrivacy;

  /**
   * 隐私保护后的收货人详细地址
   */
  private String receiverAddressPrivacy;

  /**
   * 隐私保护后的收货人电话号码
   */
  private String receiverPhonePrivacy;

  /**
   * 隐私保护后的收货人固定电话
   */
  private String receiverLandlinePhonePrivacy;

  /**
   * 平台创建时间
   */
  private Date created;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

}
