package com.yxt.order.atom.order_world.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@TableName("offline_refund_order_detail")
public class RefundOrderDetailDO {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 内部订单号,自己生成
   */
  private String orderNo;

  /**
   * 内部退款单号,自己生成
   */
  private String refundNo;

  /**
   * 内部明细编号,自己生成
   */
  private String refundDetailNo;

  /**
   * 商品行号
   */
  private String rowNo;

  /**
   * 商品三方平台编码
   */
  private String platformSkuId;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品名称
   */
  private String erpName;

  /**
   * 退款数量
   */
  private BigDecimal refundCount;

  /**
   * 明细状态  NORMAL-正常
   */
  private String refundStatus;

  /**
   * 赠品类型 GIFT-赠品 NOT_GIFT - 非赠品
   */
  private String giftType;

  /**
   * 商品原单价
   */
  private BigDecimal originalPrice;

  /**
   * 商品售价
   */
  private BigDecimal price;

  /**
   * 商品成本价
   */
  private BigDecimal commodityCostPrice;

  /**
   * 商品总额
   */
  private BigDecimal totalAmount;

  /**
   * 优惠分摊
   */
  private BigDecimal discountShare;

  /**
   * 折扣金额
   */
  private BigDecimal discountAmount;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 创建时间
   */
  private Date createdTime;

  /**
   * 更新时间
   */
  private Date updatedTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

  /**
   * 是否参加促销 TRUE、FALSE
   */
  private String isOnPromotion;

  /**
   * 是否拆零是买 true,false
   */
  private String detachable;

  /**
   * 售货员Id
   */
  private String salerId;

  /**
   * 售货员Name
   */
  private String salerName;

  /**
   * 商品五级分类编码
   */
  private String fiveClass;

  /**
   * 商品五级分类Name
   */
  private String fiveClassName;

  /**
   * 商品规格
   */
  private String commoditySpec;

  /**
   * 生产商
   */
  private String manufacture;

  /**
   * 商品图片
   */
  private String mainPic;

  /**
   * 平台售后单明细唯一号
   */
  private String thirdAfterOrderDetailNo;

  /**
   * 订单明细唯一编码
   */
  private String orderDetailNo;

  /**
   * 实付金额 (已乘数量)
   */
  private BigDecimal actualPayAmount;

  /**
   * 商家订单级优惠分摊(已乘数量)
   */
  private BigDecimal merchantOrderDiscountShare;

  /**
   * 平台订单级优惠分摊 (已乘数量)
   */
  private BigDecimal platformOrderDiscountShare;

  /**
   * 商家商品级折扣金额 (已乘数量)
   */
  private BigDecimal merchantGoodsDiscountAmount;

  /**
   * 平台商品级折扣金额 (已乘数量)
   */
  private BigDecimal platformGoodsDiscountAmount;

  /**
   * 商家订单级优惠分摊(单个数量)
   */
  private BigDecimal merchantOrderDiscountSharePrice;

  /**
   * 平台订单级优惠分摊 (单个数量)
   */
  private BigDecimal platformOrderDiscountSharePrice;

  /**
   * 商家商品级折扣金额 (单个数量)
   */
  private BigDecimal merchantGoodsDiscountAmountPrice;

  /**
   * 平台商品级折扣金额 (单个数量)
   */
  private BigDecimal platformGoodsDiscountAmountPrice;

  /**
   * 进项税编码
   */
  private String inputTaxCode;

  /**
   * 进项税率
   */
  private BigDecimal inputTax;

  /**
   * 销项税编码
   */
  private String outputTaxCode;

  /**
   * 销项税率
   */
  private BigDecimal outputTax;

  /**
   * 过账含税成本价
   */
  private BigDecimal postedCostWithTaxPrice;

  /**
   * 过账成本价
   */
  private BigDecimal postedCostPrice;

  /**
   * 过账税率
   */
  private BigDecimal postedCostTax;

  /**
   * 套装类型 TRUE-套装子商品   ;FALSE 非套装
   */
  private String setType;

  /**
   * 订单套装明细唯一编码
   */
  private String refundOrderSetDetailNo;

  /**
   * 平台创建时间
   */
  private Date created;

  /**
   * 平台更新时间
   */
  private Date updated;

  /**
   * 系统创建时间
   */
  private Date sysCreateTime;

  /**
   * 系统更新时间
   */
  private Date sysUpdateTime;

}