package com.yxt.order.atom.order_world.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.YxtOrderSharding;
import com.yxt.order.atom.order_world.entity.ExtendDataDO;
import com.yxt.order.atom.order_world.mapper.ExtendDataMapper;
import java.util.Objects;
import org.springframework.stereotype.Repository;

@Repository
public class ExtendDataBatchRepository extends ServiceImpl<ExtendDataMapper, ExtendDataDO> {

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  @YxtOrderSharding(shardingNo = "#extendDataDO.businessNo")
  public Boolean saveOrUpdateExtendData(ExtendDataDO extendDataDO){
    if(Objects.isNull(extendDataDO)){
      return Boolean.FALSE;
    }
    if(Objects.isNull(extendDataDO.getId())){
      return save(extendDataDO);
    }else {
      return updateById(extendDataDO);
    }
  }


}
