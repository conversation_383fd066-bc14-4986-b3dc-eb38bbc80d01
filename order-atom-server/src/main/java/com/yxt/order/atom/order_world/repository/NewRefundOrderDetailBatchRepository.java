package com.yxt.order.atom.order_world.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order_world.entity.OrderDetailDO;
import com.yxt.order.atom.order_world.entity.RefundOrderDetailDO;
import com.yxt.order.atom.order_world.mapper.NewOrderDetailMapper;
import com.yxt.order.atom.order_world.mapper.NewRefundOrderDetailMapper;
import org.springframework.stereotype.Repository;

@Repository
public class NewRefundOrderDetailBatchRepository extends ServiceImpl<NewRefundOrderDetailMapper, RefundOrderDetailDO> {

}
