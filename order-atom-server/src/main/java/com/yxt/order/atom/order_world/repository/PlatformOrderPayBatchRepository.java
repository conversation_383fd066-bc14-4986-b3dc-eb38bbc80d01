package com.yxt.order.atom.order_world.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.order.atom.order_world.entity.OrderPayDO;
import com.yxt.order.atom.order_world.entity.PlatformOrderPayDO;
import com.yxt.order.atom.order_world.mapper.NewOrderPayMapper;
import com.yxt.order.atom.order_world.mapper.PlatformOrderPayMapper;
import org.springframework.stereotype.Repository;

@Repository
public class PlatformOrderPayBatchRepository extends ServiceImpl<PlatformOrderPayMapper, PlatformOrderPayDO> {

}
