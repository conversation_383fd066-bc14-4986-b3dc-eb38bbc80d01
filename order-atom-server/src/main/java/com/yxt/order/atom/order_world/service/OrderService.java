package com.yxt.order.atom.order_world.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.common.logic.flash.FlashParam;
import com.yxt.lang.exception.YxtParamException;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.common.sharding.YxtOrderSharding;
import com.yxt.order.atom.migration.config.ThreadPoolConfig;
import com.yxt.order.atom.order.es.mapper.EsOrderWorldOrderMapper;
import com.yxt.order.atom.order.es.sync.order_world.flash.OrderWorldOrderFlash;
import com.yxt.order.atom.order_world.entity.CommodityStockChangeRecordDO;
import com.yxt.order.atom.order_world.entity.ExtendDataDO;
import com.yxt.order.atom.order_world.entity.OrderAmountDO;
import com.yxt.order.atom.order_world.entity.OrderDeliveryAddressDO;
import com.yxt.order.atom.order_world.entity.OrderDetailDO;
import com.yxt.order.atom.order_world.entity.OrderInfoDO;
import com.yxt.order.atom.order_world.entity.OrderPayDO;
import com.yxt.order.atom.order_world.entity.OrderUserInfoDO;
import com.yxt.order.atom.order_world.mapper.NewOrderAmountMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderDeliveryAddressMapper;
import com.yxt.order.atom.order_world.mapper.ExtendDataMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderInfoMapper;
import com.yxt.order.atom.order_world.mapper.NewOrderUserInfoMapper;
import com.yxt.order.atom.order_world.repository.CommodityStockChangeRecordRepository;
import com.yxt.order.atom.order_world.repository.ExtendDataBatchRepository;
import com.yxt.order.atom.order_world.repository.NewOrderDetailBatchRepository;
import com.yxt.order.atom.order_world.repository.NewOrderPayBatchRepository;
import com.yxt.order.atom.sdk.common.order_world.BizLogInfoDTO;
import com.yxt.order.atom.sdk.common.order_world.ExtendDataDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderAmountDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderDeliveryAddressDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderDetailDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderInfoDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderPayDTO;
import com.yxt.order.atom.sdk.common.order_world.OrderUserInfoDTO;
import com.yxt.order.atom.sdk.order_world.req.OrderWorldOrderQueryByScaleReq;
import com.yxt.order.atom.sdk.order_world.req.OrderWorldOrderBatchQueryByScaleReq;
import com.yxt.order.atom.sdk.order_world.req.SaveOrderWorldOrderOptionalReq;
import com.yxt.order.atom.sdk.order_world.req.UpdateOrderMainStatusOptionalReq;
import com.yxt.order.atom.sdk.order_world.req.UpdateOrderPayStatusOptionalReq;
import com.yxt.order.atom.sdk.order_world.req.UpdateOrderWorldOrderOptionalReq;
import com.yxt.order.atom.sdk.order_world.res.OrderRelatedInfoRes;
import com.yxt.order.common.order_world_dto.enums.ExtendDataTypeEnum;
import com.yxt.order.common.utils.CompletableFutureUtils;
import com.yxt.order.types.order_world.OrderQueryScaleEnum;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

@Service
@Slf4j
public class OrderService {

  @Autowired
  private NewOrderInfoMapper orderInfoMapper;
  @Autowired
  private NewOrderAmountMapper orderAmountMapper;
  @Autowired
  private NewOrderDetailBatchRepository orderDetailBatchRepository;
  @Autowired
  private NewOrderDeliveryAddressMapper orderDeliveryAddressMapper;
  @Autowired
  private NewOrderPayBatchRepository orderPayBatchRepository;
  @Autowired
  private NewOrderUserInfoMapper orderUserInfoMapper;
  @Autowired
  private CommodityStockChangeRecordRepository commodityStockChangeRecordRepository;
  @Autowired
  private ExtendDataBatchRepository extendDataBatchRepository;
  @Resource
  private OrderWorldOrderFlash orderWorldFlash;
  @Autowired
  private OrderSearchService orderSearchService;

  @Resource(name = ThreadPoolConfig.ORDER_BATCH_SEARCH_POOL)
  private Executor orderSearchPool;

  @Resource(name = ThreadPoolConfig.ORDER_BATCH_SEARCH_SUB_POOL)
  private Executor orderSearchSubPool;


  @Resource(name = ThreadPoolConfig.ORDER_REFRESH_ES_POOL)
  private Executor orderRefreshEsPool;

  @Resource
  private EsOrderWorldOrderMapper esOrderWorldOrderMapper;


  public void saveOrderOptional(SaveOrderWorldOrderOptionalReq request) {
    if (ObjectUtil.isNull(request.getOrderInfo())) {
      return;
    }
    OrderService orderService = SpringUtil.getBean(OrderService.class);
    //分表数据保存
    orderService.saveShardingOrderRelated(request);

    //不分表数据保存
    orderService.saveNonShardingOrderRelated(request);

    //保存到ES中
   refreshOrderToEs(request.getOrderInfo().getOrderNo());
  }

  private void refreshOrderToEs(String orderNo) {

    FlashParam flashParam = new FlashParam();
    flashParam.setNoList(ListUtil.toList(orderNo));
    orderWorldFlash.startFlush(flashParam);

    esOrderWorldOrderMapper.refresh();
  }


  @Transactional
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public void saveShardingOrderRelated(SaveOrderWorldOrderOptionalReq request) {
    OrderInfoDTO orderInfo = request.getOrderInfo();
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderInfo.getOrderNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      //保存订单信息
      orderInfoMapper.insert(BeanUtil.toBean(request.getOrderInfo(), OrderInfoDO.class));

      List<ExtendDataDO> extendDataList = new ArrayList<>();
      if(ObjectUtil.isNotNull(request.getOrderInfo().getExtendData())){
        extendDataList.add(BeanUtil.toBean(request.getOrderInfo().getExtendData(), ExtendDataDO.class));
      }

      //保存订单明细信息
      if(CollUtil.isNotEmpty(request.getOrderDetailList())){
        List<OrderDetailDO> orderDetailDOList = BeanUtil.copyToList(request.getOrderDetailList(), OrderDetailDO.class);
        orderDetailBatchRepository.saveBatch(orderDetailDOList);
        List<ExtendDataDTO> orderDetailExtDTOS = request.getOrderDetailList().stream()
            .map(OrderDetailDTO::getExtendDataDTO)
            .filter(ObjectUtil::isNotNull)
            .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(orderDetailExtDTOS)){
          extendDataList.addAll(BeanUtil.copyToList(orderDetailExtDTOS, ExtendDataDO.class));
        }
      }

      //保存订单收件人信息
      if(ObjectUtil.isNotNull(request.getOrderDeliveryAddress())){
        orderDeliveryAddressMapper.insert(BeanUtil.toBean(request.getOrderDeliveryAddress(), OrderDeliveryAddressDO.class));
      }

      //保存订单支付方式信息
      if(ObjectUtil.isNotNull(request.getOrderPayList())){
        orderPayBatchRepository.saveBatch(BeanUtil.copyToList(request.getOrderPayList(), OrderPayDO.class));
      }

      //保存订单金额信息
      if(ObjectUtil.isNotNull(request.getOrderAmount())){
        orderAmountMapper.insert(BeanUtil.toBean(request.getOrderAmount(), OrderAmountDO.class));
      }

      //保存订单用户信息
      if(ObjectUtil.isNotNull(request.getOrderUserInfo())){
        orderUserInfoMapper.insert(BeanUtil.toBean(request.getOrderUserInfo(), OrderUserInfoDO.class));
      }

      if(CollUtil.isNotEmpty(extendDataList)){
        extendDataBatchRepository.saveBatch(extendDataList);
      }
    }
  }

  @Transactional
  @DS(DATA_SOURCE.ORDER_OFFLINE)
  public void saveNonShardingOrderRelated(SaveOrderWorldOrderOptionalReq request) {
    //保存库存占用记录
    if(CollUtil.isNotEmpty(request.getCommodityStockChangeRecordList())){
      commodityStockChangeRecordRepository.saveBatch(BeanUtil.copyToList(request.getCommodityStockChangeRecordList(), CommodityStockChangeRecordDO.class));
    }
  }

  public void updateMainStatusOptional(UpdateOrderMainStatusOptionalReq request) {
    OrderService orderService = SpringUtil.getBean(OrderService.class);
    //分表保存
    orderService.updateOrderMainStatusShardingRelated(request);

    //不分表保存
    orderService.updateOrderMainStatusNonShardingRelated(request);
  }


  @Transactional
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public void updateOrderMainStatusShardingRelated(UpdateOrderMainStatusOptionalReq request) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(request.getOrderNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      if(ObjectUtil.isNotNull(request.getOrderInfo())) {
        OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(Wrappers.<OrderInfoDO>lambdaQuery()
            .eq(OrderInfoDO::getOrderNo, request.getOrderNo()));
        if (ObjectUtil.isNull(orderInfoDO)) {
          throw new YxtParamException("订单不存在");
        }
        OrderInfoDO updateOrderInfo = BeanUtil.toBean(request.getOrderInfo(), orderInfoDO.getClass());
        updateOrderInfo.setId(orderInfoDO.getId());
        orderInfoMapper.updateById(updateOrderInfo);
      }
      if (CollUtil.isNotEmpty(request.getOrderDetailList())) {
        orderDetailBatchRepository.updateBatchById(BeanUtil.copyToList(request.getOrderDetailList(), OrderDetailDO.class));
      }
    }
    orderRefreshEsPool.execute(()->refreshOrderToEs(request.getOrderNo()));
  }

  @Transactional
  @DS(DATA_SOURCE.ORDER_OFFLINE)
  public void updateOrderMainStatusNonShardingRelated(UpdateOrderMainStatusOptionalReq request) {
    if(CollUtil.isNotEmpty(request.getCommodityStockChangeRecordList())) {
      commodityStockChangeRecordRepository.saveBatch(BeanUtil.copyToList(request.getCommodityStockChangeRecordList(), CommodityStockChangeRecordDO.class));
    }
  }


  public void updatePayStatusOptional(UpdateOrderPayStatusOptionalReq request) {
    OrderService orderService = SpringUtil.getBean(OrderService.class);
    //分表保存
    orderService.updateOrderPayStatusShardingRelated(request);

    //不分表保存
    orderService.updateOrderPayStatusNonShardingRelated(request);
    orderRefreshEsPool.execute(()->refreshOrderToEs(request.getOrderNo()));
  }

  @Transactional
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public void updateOrderPayStatusShardingRelated(UpdateOrderPayStatusOptionalReq request) {

    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(request.getOrderNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      List<ExtendDataDO> extendDataList = new ArrayList<>();
      if(ObjectUtil.isNotNull(request.getOrderInfo())){
        OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(Wrappers.<OrderInfoDO>lambdaQuery()
            .eq(OrderInfoDO::getOrderNo, request.getOrderNo()));
        if (ObjectUtil.isNull(orderInfoDO)) {
          throw new YxtParamException("订单不存在");
        }
        OrderInfoDO updateOrderInfo = BeanUtil.toBean(request.getOrderInfo(), OrderInfoDO.class);
        updateOrderInfo.setId(orderInfoDO.getId());
        orderInfoMapper.updateById(updateOrderInfo);
        if(ObjectUtil.isNotNull(request.getOrderInfo().getExtendData())){
          extendDataList.add(BeanUtil.toBean(request.getOrderInfo().getExtendData(), ExtendDataDO.class));
        }
      }

      if(CollUtil.isNotEmpty(request.getOrderDetailList())){
        orderDetailBatchRepository.updateBatchById(BeanUtil.copyToList(request.getOrderDetailList(), OrderDetailDO.class));
        List<ExtendDataDTO> orderDetailExtDTOS = request.getOrderDetailList().stream()
            .map(OrderDetailDTO::getExtendDataDTO)
            .filter(ObjectUtil::isNotNull)
            .collect(Collectors.toList());
        if(CollUtil.isNotEmpty(orderDetailExtDTOS)){
          extendDataList.addAll(BeanUtil.copyToList(orderDetailExtDTOS, ExtendDataDO.class));
        }
      }

      if(CollUtil.isNotEmpty(request.getOrderPayTypeList())){
        orderPayBatchRepository.saveBatch(BeanUtil.copyToList(request.getOrderPayTypeList(), OrderPayDO.class));
      }
      if(CollUtil.isNotEmpty(extendDataList)){
        extendDataBatchRepository.updateBatchById(extendDataList);
      }
    }
  }

  @Transactional
  @DS(DATA_SOURCE.ORDER_OFFLINE)
  public void updateOrderPayStatusNonShardingRelated(UpdateOrderPayStatusOptionalReq request) {
    if(CollUtil.isNotEmpty(request.getCommodityStockChangeRecordList())) {
      commodityStockChangeRecordRepository.saveBatch(BeanUtil.copyToList(request.getCommodityStockChangeRecordList(), CommodityStockChangeRecordDO.class));
    }
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public OrderRelatedInfoRes getOrderInfoByScale(OrderWorldOrderQueryByScaleReq request) {
    String orderNo = request.getOrderNo();
    AtomicReference<OrderRelatedInfoRes> resDto = new AtomicReference<>(new OrderRelatedInfoRes());
    OrderInfoDO orderInfoDO = null;
    List<CompletableFuture<Void>> futureList = new ArrayList<>();
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(orderNo);
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);
      // 查询主单
      orderInfoDO = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoDO>().eq(OrderInfoDO::getOrderNo, orderNo));

      Assert.isTrue(Objects.nonNull(orderInfoDO), String.format("正单%s不存在,req:%s", orderNo, JsonUtils.toJson(request)));
    }
    for (OrderQueryScaleEnum qryScaleEnum : request.getQryScaleList()) {
      switch (qryScaleEnum) {
        case MAIN:
          OrderInfoDTO orderInfo = BeanUtil.toBean(orderInfoDO, OrderInfoDTO.class);
          CompletableFuture<Void> orderInfoFuture = CompletableFuture.runAsync(() -> {
            ExtendDataDO orderExt = orderSearchService.getOrderExt(orderNo);
            if (ObjectUtil.isNotNull(orderExt)) {
              orderInfo.setExtendData(BeanUtil.toBean(orderExt, ExtendDataDTO.class));
            }
            resDto.get().setOrderInfo(orderInfo);
          }, orderSearchSubPool);
          futureList.add(orderInfoFuture);
          break;
        case DETAIL:
          CompletableFuture<Void> orderDetailFuture = CompletableFuture.runAsync(() -> {
            List<OrderDetailDO> orderDetailDOList = orderSearchService.getOrderDetailList(orderNo);
            List<OrderDetailDTO> orderDetailDTOS = BeanUtil.copyToList(orderDetailDOList, OrderDetailDTO.class);
            resDto.get().setOrderDetailList(orderDetailDTOS);
            if (CollUtil.isNotEmpty(orderDetailDTOS)) {
              List<String> orderDetailOrderNoList = orderDetailDTOS.stream().map(OrderDetailDTO::getOrderDetailNo).collect(Collectors.toList());
              //查询 扩展表
              List<ExtendDataDO> orderDetailExtList = orderSearchService.getOrderDetailExtList(orderNo, orderDetailOrderNoList);
              if (CollUtil.isNotEmpty(orderDetailExtList)) {
                Map<String, ExtendDataDO> extMap = orderDetailExtList.stream().collect(Collectors.toMap(ExtendDataDO::getSubBusinessNo, Function.identity(), (a, b) -> a));
                for (OrderDetailDTO orderDetailDTO : orderDetailDTOS) {
                  if (!extMap.containsKey(orderDetailDTO.getOrderDetailNo())) {
                    continue;
                  }
                  ExtendDataDO extendDataDO = extMap.get(orderDetailDTO.getOrderDetailNo());
                  orderDetailDTO.setExtendDataDTO(BeanUtil.toBean(extendDataDO, ExtendDataDTO.class));
                }
              }
            }
          }, orderSearchSubPool);
          futureList.add(orderDetailFuture);
          break;
        case PAY:
          // 查询支付方式信息
          CompletableFuture<Void> payFuture = CompletableFuture.runAsync(() -> {
            List<OrderPayDO> payDOList = orderSearchService.getOrderPayList(orderNo);
            resDto.get().setOrderPayList(BeanUtil.copyToList(payDOList, OrderPayDTO.class));
          }, orderSearchSubPool);
          futureList.add(payFuture);

          // 查询支付金额信息
          CompletableFuture<Void> amountFuture = CompletableFuture.runAsync(() -> {
            OrderAmountDO orderAmountDO = orderSearchService.getOrderAmount(orderNo);
            resDto.get().setOrderAmount(BeanUtil.toBean(orderAmountDO, OrderAmountDTO.class));
          }, orderSearchSubPool);
          futureList.add(amountFuture);
          break;
        case USER:
          // 查询用户信息
          CompletableFuture<Void> userFuture = CompletableFuture.runAsync(() -> {
            OrderUserInfoDO userDO = orderSearchService.getOrderUserInfo(orderNo);
            resDto.get().setOrderUserInfo(BeanUtil.toBean(userDO, OrderUserInfoDTO.class));
          }, orderSearchSubPool);
          futureList.add(userFuture);
          break;
        case RECEIVE_INFO:
          // 查询用户信息
          CompletableFuture<Void> receiverFuture = CompletableFuture.runAsync(() -> {
            OrderDeliveryAddressDO deliveryAddressDO = orderSearchService.getOrderDeliveryAddress(orderNo);
            resDto.get()
                .setReceiverInfo(BeanUtil.toBean(deliveryAddressDO, OrderDeliveryAddressDTO.class));
          }, orderSearchSubPool);
          futureList.add(receiverFuture);
          break;
        case BIZ_LOG:
          CompletableFuture<Void> bizLogFuture = CompletableFuture.runAsync(() -> {
            List<BizLogInfoDTO> bizLogInfoDTOList = orderSearchService.getOrderBizLog(orderNo);
            resDto.get().setBizLogList(bizLogInfoDTOList);
          }, orderSearchSubPool);
          futureList.add(bizLogFuture);
          break;
        default:
          break;
      }
    }
    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
    return resDto.get();
  }


  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public List<OrderRelatedInfoRes> getOrderInfoBatchByScale(OrderWorldOrderBatchQueryByScaleReq request) {

    Function<String, Supplier<OrderRelatedInfoRes>> function = (orderNo) -> () -> {
      OrderWorldOrderQueryByScaleReq tempRequest = new OrderWorldOrderQueryByScaleReq(request.getQryScaleList(), orderNo);
      return SpringUtil.getBean(OrderService.class).getOrderInfoByScale(tempRequest);
    };
    return CompletableFutureUtils.supplyAsync(function, request.getOrderNoList(), 10, orderSearchPool).stream().filter(Objects::nonNull).collect(Collectors.toList());
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  @Transactional
  public void updateOptional(UpdateOrderWorldOrderOptionalReq request) {
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setDefineNo(request.getOrderNo());
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(Wrappers.<OrderInfoDO>lambdaQuery()
          .eq(OrderInfoDO::getOrderNo, request.getOrderNo()));
      if (ObjectUtil.isNull(orderInfoDO)) {
        throw new YxtParamException("订单不存在");
      }
      OrderInfoDO updateOrderInfo = BeanUtil.toBean(request.getOrderInfo(), OrderInfoDO.class);
      updateOrderInfo.setId(orderInfoDO.getId());
      orderInfoMapper.updateById(updateOrderInfo);

      if (ObjectUtil.isNotNull(request.getOrderInfo().getExtendData())) {
        extendDataBatchRepository.updateById(BeanUtil.toBean(request.getOrderInfo().getExtendData(), ExtendDataDO.class));
      }
    }
  }

//  @Scheduled(fixedRate = 1000)
  public void orderSearchSubPoolMonitor(){
    ThreadPoolExecutor executor = (ThreadPoolExecutor) orderSearchSubPool;
    String format = "orderSearchSubPoolMonitor ------------- pool size={},active threads={},task count={},completed tasks={},largest-pool-size={},queue-size={}";
    log.warn(StrUtil.format(format, executor.getPoolSize(), executor.getActiveCount(), executor.getTaskCount(), executor.getCompletedTaskCount(), executor.getLargestPoolSize(), executor.getQueue().size()));
  }

  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  @YxtOrderSharding(shardingNo = "#orderNo")
  public OrderInfoDTO getOrderInfo(String orderNo) {
    OrderInfoDO orderInfoDO = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfoDO>().eq(OrderInfoDO::getOrderNo, orderNo));
    if (ObjectUtil.isNull(orderInfoDO)){
      return null;
    }
    return BeanUtil.toBean(orderInfoDO, OrderInfoDTO.class);
  }
}
