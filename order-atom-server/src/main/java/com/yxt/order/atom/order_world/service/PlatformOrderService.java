package com.yxt.order.atom.order_world.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.common.sharding.OfflineOrderHit;
import com.yxt.order.atom.common.sharding.OfflineOrderHit.QueryHit;
import com.yxt.order.atom.common.sharding.OfflineOrderTableShardingHintAlgorithm;
import com.yxt.order.atom.order_world.entity.ExtendDataDO;
import com.yxt.order.atom.order_world.entity.PlatformOrderAmountDO;
import com.yxt.order.atom.order_world.entity.PlatformOrderDetailDO;
import com.yxt.order.atom.order_world.entity.PlatformOrderInfoDO;
import com.yxt.order.atom.order_world.entity.PlatformOrderPayDO;
import com.yxt.order.atom.order_world.entity.PlatformOrderUserDO;
import com.yxt.order.atom.order_world.mapper.ExtendDataMapper;
import com.yxt.order.atom.order_world.mapper.PlatformOrderAmountMapper;
import com.yxt.order.atom.order_world.mapper.PlatformOrderInfoMapper;
import com.yxt.order.atom.order_world.mapper.PlatformOrderPayMapper;
import com.yxt.order.atom.order_world.mapper.PlatformOrderUserMapper;
import com.yxt.order.atom.order_world.repository.PlatformOrderDetailBatchRepository;
import com.yxt.order.atom.order_world.repository.PlatformOrderPayBatchRepository;
import com.yxt.order.atom.sdk.common.order_world.PlatformOrderInfoDTO;
import com.yxt.order.atom.sdk.order_world.req.SavePlatformOrderOptionalReq;
import com.yxt.order.common.order_world_dto.enums.ExtendDataTypeEnum;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.shardingsphere.api.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PlatformOrderService {

  @Autowired
  private PlatformOrderInfoMapper platformOrderInfoMapper;
  @Autowired
  private PlatformOrderAmountMapper platformOrderAmountMapper;
  @Autowired
  private PlatformOrderDetailBatchRepository platformOrderDetailBatchRepository;
  @Autowired
  private PlatformOrderUserMapper platformOrderUserMapper;
  @Autowired
  private PlatformOrderPayMapper platformOrderPayMapper;
  @Autowired
  private PlatformOrderPayBatchRepository platformOrderPayBatchRepository;
  @Autowired
  private ExtendDataMapper extendDataMapper;

  @Transactional
  @DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
  public void saveOptional(SavePlatformOrderOptionalReq request) {
    PlatformOrderInfoDTO platformOrderInfo = request.getPlatformOrderInfo();
    if (ObjectUtil.isNull(platformOrderInfo)) {
      return;
    }
    try (HintManager hintManager = HintManager.getInstance()) {
      OfflineOrderHit hit = new OfflineOrderHit();
      hit.setQueryHit(new QueryHit(platformOrderInfo.getCreated()));
      OfflineOrderTableShardingHintAlgorithm.setHintManager(hintManager, hit);

      PlatformOrderInfoDO originPlatformOrder = platformOrderInfoMapper.selectOne(Wrappers.<PlatformOrderInfoDO>lambdaQuery()
          .eq(PlatformOrderInfoDO::getThirdOrderNo, platformOrderInfo.getThirdOrderNo())
          .eq(PlatformOrderInfoDO::getThirdPlatformCode, platformOrderInfo.getThirdPlatformCode())
          .last(" limit 1"));
      PlatformOrderInfoDO newPlatformOrder = BeanUtil.toBean(platformOrderInfo, PlatformOrderInfoDO.class);
      if(ObjectUtil.isNotNull(originPlatformOrder)){
        newPlatformOrder.setId(originPlatformOrder.getId());
        platformOrderInfoMapper.updateById(newPlatformOrder);
      }else {
        platformOrderInfoMapper.insert(newPlatformOrder);
      }

      if(ObjectUtil.isNotNull(platformOrderInfo.getExtendData())) {
        ExtendDataDO newExtendData = BeanUtil.toBean(platformOrderInfo.getExtendData(), ExtendDataDO.class);
        ExtendDataDO originExtendData = extendDataMapper.selectOne(Wrappers.<ExtendDataDO>lambdaQuery()
            .eq(ExtendDataDO::getBusinessNo, platformOrderInfo.getThirdOrderNo())
            .eq(ExtendDataDO::getDataType, ExtendDataTypeEnum.PLATFORM_ORDER_EXTEND.getCode())
            .last(" limit 1"));
        if (ObjectUtil.isNotNull(originExtendData)) {
          newExtendData.setId(originExtendData.getId());
          extendDataMapper.updateById(newExtendData);
        } else {
          extendDataMapper.insert(newExtendData);
        }
      }

      if (CollUtil.isNotEmpty(request.getPlatformOrderDetailList())) {
        List<PlatformOrderDetailDO> originDetailList = platformOrderDetailBatchRepository.list(Wrappers.<PlatformOrderDetailDO>lambdaQuery()
            .eq(PlatformOrderDetailDO::getThirdOrderNo, platformOrderInfo.getThirdOrderNo())
            .eq(PlatformOrderDetailDO::getThirdPlatformCode, platformOrderInfo.getThirdPlatformCode()));
        List<PlatformOrderDetailDO> newPlatformOrderDetailList = BeanUtil.copyToList(request.getPlatformOrderDetailList(), PlatformOrderDetailDO.class);
        if (CollUtil.isEmpty(originDetailList)) {
          platformOrderDetailBatchRepository.saveBatch(newPlatformOrderDetailList);
        } else {
          Map<String, Long> originDetailMap = originDetailList.stream()
              .collect(Collectors.toMap(PlatformOrderDetailDO::getThirdOrderDetailNo, PlatformOrderDetailDO::getId));
          for (PlatformOrderDetailDO newDetailDO : newPlatformOrderDetailList) {
            newDetailDO.setId(originDetailMap.getOrDefault(newDetailDO.getThirdOrderDetailNo(), null));
          }
          platformOrderDetailBatchRepository.updateBatchById(newPlatformOrderDetailList);
        }
      }

      if (ObjectUtil.isNotNull(request.getPlatformOrderAmount())) {
        PlatformOrderAmountDO originPlatformOrderAmount = platformOrderAmountMapper.selectOne(Wrappers.<PlatformOrderAmountDO>lambdaQuery()
            .eq(PlatformOrderAmountDO::getThirdOrderNo, platformOrderInfo.getThirdOrderNo())
            .eq(PlatformOrderAmountDO::getThirdPlatformCode, platformOrderInfo.getThirdPlatformCode())
            .last(" limit 1")
        );
        PlatformOrderAmountDO newPlatformOrderAmount = BeanUtil.toBean(request.getPlatformOrderAmount(), PlatformOrderAmountDO.class);
        if (ObjectUtil.isNotNull(originPlatformOrderAmount)) {
          newPlatformOrderAmount.setId(originPlatformOrderAmount.getId());
          platformOrderAmountMapper.updateById(newPlatformOrderAmount);
        }else {
          platformOrderAmountMapper.insert(newPlatformOrderAmount);
        }
      }

      if (ObjectUtil.isNotNull(request.getPlatformOrderUser())) {
        PlatformOrderUserDO originPlatformUser = platformOrderUserMapper.selectOne(Wrappers.<PlatformOrderUserDO>lambdaQuery()
            .eq(PlatformOrderUserDO::getThirdOrderNo, platformOrderInfo.getThirdOrderNo())
            .eq(PlatformOrderUserDO::getThirdPlatformCode, platformOrderInfo.getThirdPlatformCode())
            .last(" limit 1")
        );
        PlatformOrderUserDO newPlatformOrderUser = BeanUtil.toBean(request.getPlatformOrderUser(), PlatformOrderUserDO.class);
        if (ObjectUtil.isNotNull(originPlatformUser)) {
          newPlatformOrderUser.setId(originPlatformUser.getId());
          platformOrderUserMapper.updateById(newPlatformOrderUser);
        }else {
          platformOrderUserMapper.insert(newPlatformOrderUser);
        }
      }

      if (CollUtil.isNotEmpty(request.getPlatformOrderPayList())) {
        List<PlatformOrderPayDO> originPayList = platformOrderPayMapper.selectList(Wrappers.<PlatformOrderPayDO>lambdaQuery()
            .eq(PlatformOrderPayDO::getThirdOrderNo, platformOrderInfo.getThirdOrderNo())
            .eq(PlatformOrderPayDO::getThirdPlatformCode, platformOrderInfo.getThirdPlatformCode()));
        List<PlatformOrderPayDO> newPlatformOrderPayList = BeanUtil.copyToList(request.getPlatformOrderPayList(),PlatformOrderPayDO.class);
        if (CollUtil.isEmpty(originPayList)) {
          platformOrderPayBatchRepository.saveBatch(newPlatformOrderPayList);
        } else {
          Map<String, Long> originPayMap = originPayList.stream()
              .collect(Collectors.toMap(PlatformOrderPayDO::getThirdOrderNo, PlatformOrderPayDO::getId));
          for (PlatformOrderPayDO newPayDO : newPlatformOrderPayList) {
            newPayDO.setId(originPayMap.getOrDefault(newPayDO.getThirdOrderNo(), null));
          }
          platformOrderPayBatchRepository.updateBatchById(newPlatformOrderPayList);
        }
      }
    }
  }
}
