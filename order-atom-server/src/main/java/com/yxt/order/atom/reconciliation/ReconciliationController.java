package com.yxt.order.atom.reconciliation;

import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.order.repository.ReconciliationRepository;
import com.yxt.order.atom.sdk.reconciliation.ReconciliationApi;
import com.yxt.order.atom.sdk.reconciliation.req.ReconciliationListReq;
import com.yxt.order.atom.sdk.reconciliation.req.ReconciliationReq;
import com.yxt.order.atom.sdk.reconciliation.res.ReconciliationListRes;
import com.yxt.order.atom.sdk.reconciliation.res.ReconciliationRes;
import com.yxt.starter.controller.AbstractController;
import javax.annotation.Resource;
import lombok.EqualsAndHashCode;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: moatkon
 * @time: 2025/4/23 16:34
 */
@EqualsAndHashCode(callSuper = true)
@RestController
public class ReconciliationController extends AbstractController implements ReconciliationApi {

  @Resource
  private ReconciliationRepository reconciliationRepository;

  @Override
  public ResponseBase<ReconciliationRes> offlineUnionReconciliation(ReconciliationReq req) {
    return generateSuccess(reconciliationRepository.offlineUnionReconciliation(req));
  }

  @Override
  public ResponseBase<ReconciliationListRes> offlineUnionListReconciliation(
      ReconciliationListReq req) {
    return generateSuccess(reconciliationRepository.offlineUnionListReconciliation(req));
  }

  @Override
  public ResponseBase<ReconciliationRes> offlineOrderReconciliation(ReconciliationReq req) {
    return generateSuccess(reconciliationRepository.offlineOrderReconciliation(req));
  }

  @Override
  public ResponseBase<ReconciliationRes> offlineRefundReconciliation(ReconciliationReq req) {
    return generateSuccess(reconciliationRepository.offlineRefundReconciliation(req));
  }
}
