package com.yxt.order.atom.repair;

import com.google.common.collect.Lists;
import com.yxt.order.atom.order.entity.OrderDataRepairDO;
import com.yxt.order.atom.order.repository.batch.OrderDataRepairBatchRepository;
import com.yxt.order.types.offline.enums.PreCheckEnum;
import com.yxt.order.types.repair.RepairScene;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;

/**
 * 基础
 *
 * @author: moatkon
 * @time: 2025/1/9 11:02
 */
public abstract class AbstractRepairDataBaseGet {



  @Resource
  protected OrderDataRepairBatchRepository orderDataRepairBatchRepository;


  public abstract RepairScene scene();


  /**
   * 直接入库
   */
  public void directEntry(String json) {
    entry(Lists.newArrayList(json));
  }

  /**
   * public修饰符的原因:可以从其他途径入表,例如mq、导入等
   *
   * @param inputList
   */
  public void entry(List<String> inputList) {
    if (CollectionUtils.isEmpty(inputList)) {
      return;
    }
    List<OrderDataRepairDO> orderDataRepairDOList = inputList.stream().map(input -> {
      OrderDataRepairDO orderDataRepairDO = new OrderDataRepairDO();
      orderDataRepairDO.setScene(scene().name());
      orderDataRepairDO.setInput(input);
      orderDataRepairDO.setPreCheck(PreCheckEnum.WAIT.name());
      orderDataRepairDO.setRepairResult(Strings.EMPTY);
      orderDataRepairDO.setRepairFailedReason(Strings.EMPTY);
      orderDataRepairDO.setBusinessNo(Strings.EMPTY);
      orderDataRepairDO.setCreatedBy(Strings.EMPTY);
      orderDataRepairDO.setUpdatedBy(Strings.EMPTY);
      orderDataRepairDO.setCreatedTime(new Date());
      orderDataRepairDO.setUpdatedTime(new Date());
      orderDataRepairDO.setVersion(1L);
      orderDataRepairDO.setPreCheckFailed(Strings.EMPTY);
      orderDataRepairDO.setBeforeImage(Strings.EMPTY);
      orderDataRepairDO.setAfterImage(Strings.EMPTY);
      return orderDataRepairDO;
    }).collect(Collectors.toList());

    orderDataRepairBatchRepository.saveBatch(orderDataRepairDOList);
  }


}
