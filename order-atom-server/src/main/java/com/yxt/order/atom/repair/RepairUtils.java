package com.yxt.order.atom.repair;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: moatkon
 * @time: 2025/1/24 16:59
 */
public class RepairUtils {

  public static void validateUniqueBusinessUk(List<String> detailList) {
    Map<String, Long> businessUkCounts = detailList.stream()
        .collect(Collectors.groupingBy(s -> s, Collectors.counting()));

    List<String> duplicateBusinessUks = businessUkCounts.entrySet().stream()
        .filter(entry -> entry.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());

    if (!duplicateBusinessUks.isEmpty()) {
      throw new IllegalArgumentException("Duplicate business UK found: " + duplicateBusinessUks);
    }
  }
}
