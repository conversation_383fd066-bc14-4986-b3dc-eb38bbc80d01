package com.yxt.order.atom.repair.dto;

import com.yxt.order.types.repair.RepairScene;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author: moatkon
 * @time: 2025/3/19 10:13
 */
@Data
public class RepairReq {

  @NotEmpty(message = "monitoryKey can not empty")
  private String monitoryKey;

  @NotNull(message = "startId can not empty")
  private Long startId;

  @NotNull(message = "endId can not empty")
  private Long endId;

  @NotNull(message = "repairScene can not empty")
  private RepairScene repairScene;

}
