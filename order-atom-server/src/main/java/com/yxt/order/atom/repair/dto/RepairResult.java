package com.yxt.order.atom.repair.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: moatkon
 * @time: 2025/1/23 15:03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepairResult {

  private Boolean result; // 修复结果

  /**
   * 如果修复成功
   */
  private String businessNo; // 记录单号

  private String afterImage; // 记录修复成功后的快照


  /**
   * 修复失败
   */
  private String repairFailedReason; // 记录原因


}
