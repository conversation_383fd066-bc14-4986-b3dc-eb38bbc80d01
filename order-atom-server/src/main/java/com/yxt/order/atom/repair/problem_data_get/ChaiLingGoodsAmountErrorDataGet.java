package com.yxt.order.atom.repair.problem_data_get;

import com.yxt.order.atom.repair.AbstractRepairDataBaseGet;
import com.yxt.order.types.repair.RepairScene;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2025/1/9 10:03
 */
@Component
public class ChaiLingGoodsAmountErrorDataGet extends AbstractRepairDataBaseGet {

  @Override
  public RepairScene scene() {
    return RepairScene.CHAI_LING_GOODS_AMOUNT_ERROR_ORDER;
  }

}
