package com.yxt.order.atom.repair.problem_data_repair;

import static com.yxt.order.atom.repair.RepairUtils.validateUniqueBusinessUk;

import cn.hutool.core.bean.BeanUtil;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDO;
import com.yxt.order.atom.order.entity.OfflineRefundOrderDetailDO;
import com.yxt.order.atom.order.entity.OrderDataRepairDO;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.repair.AbstractOrderRepair;
import com.yxt.order.atom.repair.dto.PreCheckResult;
import com.yxt.order.atom.repair.dto.RepairResult;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineRefundOrderDetailDTO;
import com.yxt.order.atom.sdk.offline_order.dto.repair.RepairOfflineRefundOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineRefundOrderExistsReqDto;
import com.yxt.order.common.utils.OrderJsonUtils;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import com.yxt.order.types.repair.RepairScene;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2025/1/9 10:03
 */
@Component
public class ChaiLingGoodsAmountRefundErrorRepair extends AbstractOrderRepair<RepairOfflineRefundOrderReqDto> {

  @Resource
  private OfflineOrderRepository offlineOrderRepository;

  @Override
  public RepairScene scene() {
    return RepairScene.CHAI_LING_GOODS_AMOUNT_ERROR_REFUND_ORDER;
  }


  @Override
  protected RepairOfflineRefundOrderReqDto parse(OrderDataRepairDO orderDataRepair) {
    return OrderJsonUtils.toObject(
        orderDataRepair.getInput(), RepairOfflineRefundOrderReqDto.class);
  }

  @Override
  protected String shardingNo(OrderDataRepairDO orderDataRepairDO) {
    return parse(orderDataRepairDO).getOfflineRefundOrderDTO().getRefundNo();
  }

  @Override
  protected PreCheckResult repairPreCheck(OrderDataRepairDO orderDataRepairDO) {
    RepairOfflineRefundOrderReqDto repairReqDto = parse(orderDataRepairDO);
    OfflineRefundOrderDTO needRepairRefundOrder = repairReqDto.getOfflineRefundOrderDTO();

    OfflineRefundOrderExistsReqDto reqDto = new OfflineRefundOrderExistsReqDto();
    reqDto.setStoreCode(needRepairRefundOrder.getStoreCode());
    reqDto.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
    reqDto.setThirdRefundNo(needRepairRefundOrder.getThirdRefundNo());
    reqDto.setThirdCreated(needRepairRefundOrder.getCreated());
    OfflineRefundOrderDO offlineRefundOrderDO = offlineOrderRepository.offlineRefundOrderForRepair(reqDto);
    if (Objects.isNull(offlineRefundOrderDO)) {
      return PreCheckResult.create()
          .failed(String.format("%s 查不到数据,可能是重复MQ消息,已经处理被删了,或者就是找不到", OrderJsonUtils.toJson(reqDto)));
    } else {
      return PreCheckResult.create()
          .passAndRecordBeforeImage(OrderJsonUtils.toJson(offlineRefundOrderDO), offlineRefundOrderDO.getRefundNo());
    }
  }

  @Override
  protected RepairResult orderRepair(OrderDataRepairDO orderDataRepair) {
    OfflineRefundOrderDO dbRefund = OrderJsonUtils.toObject(orderDataRepair.getBeforeImage(),OfflineRefundOrderDO.class);
    List<OfflineRefundOrderDetailDO> dbRefundDetailDOList = dbRefund.getRefundOrderDetailDOList();

    RepairOfflineRefundOrderReqDto reqDto = parse(orderDataRepair);
    OfflineRefundOrderDTO reqOfflineRefundOrderDTO = reqDto.getOfflineRefundOrderDTO();
    List<OfflineRefundOrderDetailDTO> reqDetailDTOList = reqDto.getOfflineRefundOrderDetailDTOList();
    // 转成DO
    List<OfflineRefundOrderDetailDO> reqDetailDOList = BeanUtil.copyToList(reqDetailDTOList,
        OfflineRefundOrderDetailDO.class);

    // 统一校验唯一性
    validateUniqueBusinessUk(dbRefundDetailDOList.stream().map(OfflineRefundOrderDetailDO::businessUk).collect(
        Collectors.toList()));
    validateUniqueBusinessUk(reqDetailDOList.stream().map(OfflineRefundOrderDetailDO::businessUk).collect(
        Collectors.toList()));

    // 使用新过来的值
    dbRefund.setTotalAmount(reqOfflineRefundOrderDTO.getTotalAmount());
    dbRefund.setShopRefund(reqOfflineRefundOrderDTO.getShopRefund());
    dbRefund.setConsumerRefund(reqOfflineRefundOrderDTO.getConsumerRefund());
    dbRefund.markUpdateBy("海典修复CLR"); //ChaiLingRefund

    // 处理明细
    for (OfflineRefundOrderDetailDO dbDetail : dbRefundDetailDOList) {
      for (OfflineRefundOrderDetailDO reqDetailDO : reqDetailDOList) {
        if (dbDetail.businessUk().equals(reqDetailDO.businessUk())) {
          String refundNo = dbDetail.getRefundNo();
          String orderNo = dbDetail.getOrderNo();
          String refundDetailNo = dbDetail.getRefundDetailNo();
          Date createdTime = dbDetail.getCreatedTime();
          Long id = dbDetail.getId();

          BeanUtil.copyProperties(reqDetailDO, dbDetail);
          dbDetail.setId(id);
          dbDetail.setRefundNo(refundNo);
          dbDetail.setOrderNo(orderNo);
          dbDetail.setRefundDetailNo(refundDetailNo);
          dbDetail.setCreatedTime(createdTime);
          dbDetail.markUpdateBy("海典修复CLR");
        }
      }
    }

    Boolean result = offlineOrderRepository.chaiLingOfflineRefundOrderUpdate(dbRefund);


    return RepairResult.builder()
        .result(result)
        .businessNo(dbRefund.getRefundNo())
        .afterImage(OrderJsonUtils.toJson(dbRefund))
        .build();
  }




}
