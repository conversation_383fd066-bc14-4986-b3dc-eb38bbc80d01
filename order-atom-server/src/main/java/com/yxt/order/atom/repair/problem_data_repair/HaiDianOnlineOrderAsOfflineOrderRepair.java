package com.yxt.order.atom.repair.problem_data_repair;

import com.yxt.order.atom.migration.constant.MigrationConstant;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OrderDataRepairDO;
import com.yxt.order.atom.order.repository.OfflineOrderRepository;
import com.yxt.order.atom.repair.AbstractOrderRepair;
import com.yxt.order.atom.repair.dto.PreCheckResult;
import com.yxt.order.atom.repair.dto.RepairResult;
import com.yxt.order.atom.sdk.offline_order.dto.OfflineOrderDTO;
import com.yxt.order.atom.sdk.offline_order.dto.repair.RepairOfflineOrderReqDto;
import com.yxt.order.atom.sdk.offline_order.req.OfflineOrderExistsReqDto;
import com.yxt.order.common.utils.OrderJsonUtils;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import com.yxt.order.types.repair.RepairScene;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @author: moatkon
 * @time: 2025/1/9 10:02
 */
@Component
public class HaiDianOnlineOrderAsOfflineOrderRepair extends AbstractOrderRepair<RepairOfflineOrderReqDto> {

  @Resource
  private OfflineOrderRepository offlineOrderRepository;

  @Override
  public RepairScene scene() {
    return RepairScene.ONLINE_ORDER_AS_OFFLINE_ORDER;
  }

  @Override
  protected RepairOfflineOrderReqDto parse(OrderDataRepairDO orderDataRepair) {
    return OrderJsonUtils.toObject(orderDataRepair.getInput(),
        RepairOfflineOrderReqDto.class);
  }

  @Override
  protected String shardingNo(OrderDataRepairDO orderDataRepairDO) {
    return parse(orderDataRepairDO).getOfflineOrderDTO().getOrderNo();
  }

  @Override
  protected PreCheckResult repairPreCheck(OrderDataRepairDO orderDataRepairDO) {

    RepairOfflineOrderReqDto onlineOrderReq = parse(orderDataRepairDO);
    OfflineOrderDTO onlineOrder = onlineOrderReq.getOfflineOrderDTO();

    OfflineOrderExistsReqDto reqDto = new OfflineOrderExistsReqDto();
    reqDto.setStoreCode(onlineOrder.getStoreCode());
    reqDto.setThirdPlatformCode(ThirdPlatformCodeEnum.HAIDIAN.name());
    reqDto.setThirdOrderNo(onlineOrder.getThirdOrderNo());
    reqDto.setThirdCreated(onlineOrder.getCreated());
    OfflineOrderDO offlineOrderDO = offlineOrderRepository.offlineOrderInfoForRepair(reqDto);
    if (Objects.isNull(offlineOrderDO)) {
      return PreCheckResult.create()
          .failed(String.format("%s 查不到数据,可能是重复MQ消息,已经处理被删了,或者就是找不到", OrderJsonUtils.toJson(reqDto)));
    } else {
      return PreCheckResult.create()
          .passAndRecordBeforeImage(OrderJsonUtils.toJson(offlineOrderDO), offlineOrderDO.getOrderNo());
    }
  }

  @Override
  protected RepairResult orderRepair(OrderDataRepairDO orderDataRepair) {

    // 物理删除
    OfflineOrderDO offlineOrderDO = OrderJsonUtils.toObject(orderDataRepair.getBeforeImage(),OfflineOrderDO.class);
    Boolean result = offlineOrderRepository.deletedOfflineOrder(offlineOrderDO,
        MigrationConstant.DELETED_HAIDIAN_ONLINE_ORDER_AS_OFFLINE_ORDER,Boolean.FALSE);

    return RepairResult.builder()
        .result(result)
        .businessNo(offlineOrderDO.getOrderNo())
        .build();
  }

}
