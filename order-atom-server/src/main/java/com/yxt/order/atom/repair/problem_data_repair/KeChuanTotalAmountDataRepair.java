package com.yxt.order.atom.repair.problem_data_repair;

import static com.yxt.order.types.repair.RepairScene.KE_CHUAN_TOTAL_AMOUNT_ERROR;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.order.atom.common.LocalConst.DATA_SOURCE;
import com.yxt.order.atom.order.entity.OfflineOrderDO;
import com.yxt.order.atom.order.entity.OfflineOrderDetailDO;
import com.yxt.order.atom.order.entity.OrderDataRepairDO;
import com.yxt.order.atom.order.es.sync.AbstractFlash;
import com.yxt.order.atom.order.es.sync.FixKeChuanAmountError;
import com.yxt.order.atom.order.es.sync.FlashQueryWrapper;
import com.yxt.order.atom.order.mapper.OfflineOrderDetailMapper;
import com.yxt.order.atom.order.mapper.OfflineOrderMapper;
import com.yxt.order.atom.order.repository.batch.OrderDataRepairBatchRepository;
import com.yxt.order.atom.repair.dto.BusinessNoForKeChuanTotalAmountFix;
import com.yxt.order.common.utils.OrderJsonUtils;
import com.yxt.order.types.offline.enums.PreCheckEnum;
import com.yxt.order.types.offline.enums.ThirdPlatformCodeEnum;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

/**
 * @author: moatkon
 * @time: 2024/12/13 10:21
 * <p>
 * 只修复正向订单; 退单科传没有传totalAmount字段
 */
@Component
@Slf4j
@DS(DATA_SOURCE.SHARDING_DATA_SOURCE_NAME)
public class KeChuanTotalAmountDataRepair extends
    AbstractFlash<OfflineOrderDO, OfflineOrderDO, FixKeChuanAmountError> {

  @Value("${keChuanTotalAmountDataGetLimit:2000}")
  private Integer keChuanTotalAmountDataGetLimit;

  @Autowired
  private TransactionTemplate transactionTemplate;

  @Resource
  private OfflineOrderMapper offlineOrderMapper;

  @Resource
  private OfflineOrderDetailMapper offlineOrderDetailMapper;

  @Resource
  private OrderDataRepairBatchRepository orderDataRepairBatchRepository;

  @Override
  protected Long queryCursorStartId() {
    return offlineOrderMapper.selectMinId(getFlashParam());
  }

  @Override
  protected Long queryCursorEndId() {
    return offlineOrderMapper.selectMaxId(getFlashParam());
  }

  @Override
  protected List<OfflineOrderDO> getSourceList() {
    LambdaQueryWrapper<OfflineOrderDO> query = FlashQueryWrapper.offlineOrderFlashQuery(
        getFlashParam(), defaultLimit());
    query.eq(OfflineOrderDO::getThirdPlatformCode, ThirdPlatformCodeEnum.KE_CHUAN.name());
    return offlineOrderMapper.selectList(query);
  }

  @Override
  protected List<OfflineOrderDO> assembleTargetData(List<OfflineOrderDO> offlineOrderDOList) {
    return offlineOrderDOList;
  }

  /**
   * 因为无输入逻辑,可以直接刷数
   *
   * @param offlineOrderList
   */
  @Override
  protected void flash(List<OfflineOrderDO> offlineOrderList) {

    for (OfflineOrderDO offlineOrderDO : offlineOrderList) {
      // 不处理迁移订单
      if (Boolean.TRUE.toString().equals(offlineOrderDO.getMigration())) {
        continue;
      }

      LambdaQueryWrapper<OfflineOrderDetailDO> detailQuery = new LambdaQueryWrapper<>();
      detailQuery.eq(OfflineOrderDetailDO::getOrderNo, offlineOrderDO.getOrderNo());
      List<OfflineOrderDetailDO> offlineOrderDetailDOS = offlineOrderDetailMapper.selectList(
          detailQuery);
      if (CollectionUtils.isEmpty(offlineOrderDetailDOS)) {
        continue;
      }

      for (OfflineOrderDetailDO offlineOrderDetailDO : offlineOrderDetailDOS) {
        try {

          if (Objects.isNull(offlineOrderDetailDO.getId())) {
            continue;
          }

          if (offlineOrderDetailDO.getTotalAmount().compareTo(
              offlineOrderDetailDO.getPrice().multiply(offlineOrderDetailDO.getCommodityCount())
                  .setScale(6,
                      RoundingMode.UNNECESSARY))==0) {
            continue;
          }

          transactionTemplate.execute(status -> {
            handle(offlineOrderDetailDO);
            return null;
          });
        } catch (Exception e) {
          log.warn("FixKeChuanAmount失败,原因:{},数据:{}-{}",e.getMessage(),offlineOrderDetailDO.getOrderNo(),offlineOrderDetailDO.getOrderDetailNo(),e);
        }
      }
    }

  }


  private void handle(OfflineOrderDetailDO offlineOrderDetailDO) {
    String beforeImage = OrderJsonUtils.toJson(offlineOrderDetailDO);

    // 修复totalAmount
    BigDecimal totalAmount = offlineOrderDetailDO.getPrice()
        .multiply(offlineOrderDetailDO.getCommodityCount());
    offlineOrderDetailDO.setTotalAmount(totalAmount);
    offlineOrderDetailDO.setUpdatedBy(
        "JIRA:ORDER-4119"); // 科传的错误处理: total_amount = origin_price乘以数量;准确的处理方式是: total_amount = price乘以数量
    int i = offlineOrderDetailMapper.updateById(offlineOrderDetailDO);
    if (i <= 0) {
      return;
    }

    String afterImage = OrderJsonUtils.toJson(offlineOrderDetailDO);

    OrderDataRepairDO orderDataRepairDO = new OrderDataRepairDO();
    orderDataRepairDO.setScene(KE_CHUAN_TOTAL_AMOUNT_ERROR.name());
    orderDataRepairDO.setInput(offlineOrderDetailDO.getOrderNo());
    orderDataRepairDO.setPreCheck(PreCheckEnum.PASS.name());
    orderDataRepairDO.setPreCheckFailed(Strings.EMPTY);
    orderDataRepairDO.setRepairResult(Boolean.TRUE.toString());
    orderDataRepairDO.setRepairFailedReason(Strings.EMPTY);

    BusinessNoForKeChuanTotalAmountFix businessNo = BusinessNoForKeChuanTotalAmountFix.builder()
        .orderNo(offlineOrderDetailDO.getOrderNo())
        .orderDetailNo(offlineOrderDetailDO.getOrderDetailNo())
        .build();

    orderDataRepairDO.setBusinessNo(OrderJsonUtils.toJson(businessNo));
    orderDataRepairDO.setCreatedBy(Strings.EMPTY);
    orderDataRepairDO.setUpdatedBy(Strings.EMPTY);
    orderDataRepairDO.setCreatedTime(new Date());
    orderDataRepairDO.setUpdatedTime(new Date());
    orderDataRepairDO.setVersion(1L);
    orderDataRepairDO.setBeforeImage(String.valueOf(beforeImage));
    orderDataRepairDO.setAfterImage(String.valueOf(afterImage));
    log.info("beforeImage:{}, afterImage:{}",beforeImage,afterImage);
    orderDataRepairBatchRepository.save(orderDataRepairDO);
  }

  @Override
  protected Boolean isSharding() {
    return Boolean.TRUE;
  }

  @Override
  protected Integer defaultLimit() {
    return keChuanTotalAmountDataGetLimit;
  }
}
