<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.order.atom.order.mapper.DeletedDataMapper">

  <select id="selectMinId" resultType="java.lang.Long">
    select min(id) from <include refid="suffixSql" />
  </select>

  <select id="selectMaxId" resultType="java.lang.Long">
    select max(id) from
    <include refid="suffixSql" />
  </select>

  <sql id="suffixSql">
    <if test="suffix == null or suffix.isEmpty()">
      deleted_data
    </if>
    <if test="suffix != null and !suffix.isEmpty()">
      deleted_data${suffix}
    </if>
  </sql>

  <select id="selectListByMinMaxId" resultType="com.yxt.order.atom.order.entity.DeletedDataDO">
    select
      business_no as businessNo,
      table_name as tableName,
      reason as reason,
      deleted_data as deletedData
    from
    <include refid="suffixSql" />
    where id <![CDATA[ >= ]]> #{startId} and id <![CDATA[ < ]]> #{endId}
  </select>
</mapper>