<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.yxt.order.atom.order.mapper.DsMerchantGroupInfoMapper">

  <!-- 通用查询映射结果 -->
  <resultMap id="BaseResultMap" type="com.yxt.order.atom.order.entity.DsMerchantGroupInfoDO">
    <id column="id" property="id" />
    <result column="mer_code" property="merCode" />
    <result column="mer_name" property="merName" />
    <result column="session_key" property="sessionKey" />
    <result column="create_time" property="createTime"/>
    <result column="modify_time" property="modifyTime" />
  </resultMap>

  <sql id="MerchantGroupInfoColumn">
    `id`,
      `mer_code`,
      `mer_name`,
      `session_key`,
      `create_time`,
      `modify_time`
  </sql>

  <!--查询网店授权信息-->
  <select id="querySessionKeyByMerCode" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
    <include refid="MerchantGroupInfoColumn"/>
    from ds_merchant_group_info
    where mer_code = #{merCode}
  </select>


</mapper>