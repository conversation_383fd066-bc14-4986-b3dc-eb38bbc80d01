<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yxt.order.atom.order.mapper.DsOnlineClientMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.yxt.order.atom.order.entity.DsOnlineClientDO">
        <id column="id" property="id" />
        <result column="mer_code" property="merCode" />
        <result column="platform_code" property="platformCode" />
        <result column="platform_name" property="platformName" />
        <result column="appid" property="appid" />
        <result column="app_secret" property="appSecret" />
        <result column="online_shop_code" property="onlineClientCode" />
        <result column="online_shop_name" property="onlineClientName" />
        <result column="auth_deadline" property="authDeadline" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="seller_id" property="sellerId" />
        <result column="access_token" property="accessToken" />
        <result column="refresh_token" property="refreshToken" />
        <result column="service_mask" property="serviceMask" />
        <result column="secret_key" property="secretKey" />
        <result column="access_type" property="accessType"/>
    </resultMap>

    <select id="selectSupplierClient" resultType="com.yxt.order.atom.order.entity.DsOnlineClientDO">
        select
            doc.mer_code,
            doc.online_client_code
        from
            ds_online_client doc
                join ds_online_store dos
                     on doc.mer_code = dos.mer_code
                         and doc.platform_code = dos.platform_code
                         and doc.online_client_code = dos.online_client_code
        where doc.platform_code = #{platformCode,jdbcType=VARCHAR}
          and doc.access_type = 2
          and dos.online_store_code = #{onlineStoreCode,jdbcType=VARCHAR}
          and (dos.status is null or dos.status not in ('2'))
    </select>


    <select id="getStoreAccess"
      resultType="com.yxt.order.atom.sdk.online_order.store.res.The3DsStoreResDto">
        select
        a.`mer_code` AS merCode,
        a.platform_code AS platformCode,
        b.access_token AS accessToken,
        b.`appid`,
        b.`app_secret` AS appSecret,
        b.online_client_code AS onlineClientCode,
        b.`online_client_out_code` AS onlineClientOutCode,
        a.platform_shop_id as platformShopId,
        a.address_id as addressId,
        a.online_store_code as onlineStoreCode
        from
        ds_online_store as a
        inner join ds_online_client as b on a.online_client_code = b.online_client_code
        where
        a.online_store_code = #{onlineStoreCode}
        and a.mer_code = #{merCode}
        and b.platform_code = #{platformCode}
        <if test="onlineClientCode != null and onlineClientCode != ''">
            and a.online_client_code = #{onlineClientCode}
        </if>
    </select>
  <select id="getOnlineStoreByPlatformShopId"
          resultType="com.yxt.order.atom.sdk.online_order.store.res.The3DsOnlineClientResDto">
      SELECT
      a.`mer_code` AS merCode,
      a.platform_code AS platformCode,
      a.access_token AS accessToken,
      a.`appid`,
      a.`app_secret` AS appSecret,
      a.online_client_code AS onlineClientCode,
      a.online_client_out_code AS onlineClientOutCode,
      c.online_store_code AS onlineStoreCode,
      c.`platform_shop_id` AS platformShopId,
      b.delivery_type  AS deliveryType
      FROM
      `ds_online_client` a
      LEFT JOIN ds_online_store c ON a.online_client_code = c.online_client_code
      LEFT JOIN ds_online_store_config b ON c.id = b.online_store_id
      WHERE
      a.access_type = 1
      AND a.platform_code = #{platformCode}
      AND c.platform_shop_id = #{platformShopId}
      AND a.mer_code = #{merCode}
      <if test="onlineClientCode != null and onlineClientCode != ''">
          and a.online_client_code = #{onlineClientCode}
      </if>
      LIMIT 1
  </select>

</mapper>
